<template>
  <div>
    <div v-if="pending" class="flex items-center justify-center py-12">
      <div class="text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
        <p class="text-muted-foreground">Loading...</p>
      </div>
    </div>

    <div v-else-if="error" class="text-center py-12">
      <div class="max-w-md mx-auto">
        <div class="p-6 border border-destructive/20 rounded-lg bg-destructive/5">
          <AlertCircle class="h-12 w-12 text-destructive mx-auto mb-4" />
          <h2 class="text-xl font-semibold mb-2">File Not Found</h2>
          <p class="text-muted-foreground mb-4">
            The requested YAML file could not be found.
          </p>
          <Button as="NuxtLink" to="/" variant="outline">
            Go Home
          </Button>
        </div>
      </div>
    </div>

    <YamlViewer
      v-else-if="yamlContent"
      :content="yamlContent.content"
      :title="yamlContent.title"
      :description="yamlContent.description"
      :filename="yamlContent.filename"
      :breadcrumbs="breadcrumbs"
      :metadata="yamlContent.metadata"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { AlertCircle } from 'lucide-vue-next'
import { Button } from '~/components/ui/button'

const route = useRoute()
const router = useRouter()

// Get the slug from the route
const slug = computed(() => {
  const slugArray = Array.isArray(route.params.slug) ? route.params.slug : [route.params.slug]
  return slugArray.join('/')
})

// Mock data for YAML files - in a real app, this would come from the content API
const yamlFiles = {
  'api/basics': {
    title: 'Basics & Fundamentals',
    description: 'Core concepts of the OpenAPI specification',
    filename: 'basics.yaml',
    content: `# API Basics & Fundamentals
# This section covers the core concepts of the OpenAPI specification

openapi: 3.0.3
info:
  title: Sample API Documentation
  description: |
    This specification covers the core concepts of the OpenAPI specification.
    It includes examples of common patterns and best practices for API design.
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>
    url: https://example.com/support
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.example.com/v1
    description: Production server
  - url: https://staging-api.example.com/v1
    description: Staging server
  - url: http://localhost:3000/v1
    description: Development server

# The Basics
# Each API specification starts with the OpenAPI version and basic information
# about the API. This includes the title, description, version, and contact details.

paths:
  /users:
    get:
      summary: List all users
      description: Retrieve a paginated list of users
      operationId: listUsers
      tags:
        - Users
      parameters:
        - name: page
          in: query
          description: Page number for pagination
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/User'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

components:
  schemas:
    User:
      type: object
      required:
        - id
        - email
        - name
        - createdAt
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the user
        email:
          type: string
          format: email
          description: User's email address
        name:
          type: string
          description: User's full name
        avatar:
          type: string
          format: uri
          description: URL to user's avatar image
        createdAt:
          type: string
          format: date-time
          description: When the user account was created

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

security:
  - BearerAuth: []

tags:
  - name: Users
    description: User management operations`,
    metadata: {
      lastModified: '2024-01-15T10:30:00Z',
      tags: ['api', 'openapi', 'basics']
    }
  },
  'api/authentication': {
    title: 'Authentication',
    description: 'Authentication methods and security patterns',
    filename: 'authentication.yaml',
    content: `# Authentication & Authorization
# This section covers authentication methods and security patterns

openapi: 3.0.3
info:
  title: Authentication API
  description: |
    Authentication and authorization patterns for secure API access.
    Covers JWT tokens, OAuth2, API keys, and role-based access control.
  version: 1.0.0

servers:
  - url: https://auth.example.com/v1
    description: Authentication server

paths:
  /auth/login:
    post:
      summary: User login
      description: Authenticate user with email and password
      operationId: login
      tags:
        - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                  format: email
                  example: <EMAIL>
                password:
                  type: string
                  format: password
                  example: secretpassword123
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  accessToken:
                    type: string
                    description: JWT access token
                  refreshToken:
                    type: string
                    description: Refresh token for obtaining new access tokens
                  expiresIn:
                    type: integer
                    description: Token expiration time in seconds
                    example: 3600
        '401':
          description: Invalid credentials

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: |
        JWT Bearer token authentication.
        
        To authenticate with the API:
        1. Login with your credentials to get an access token
        2. Include the token in the Authorization header: \`Bearer <token>\`
        3. Use the refresh token to get new access tokens when they expire

security:
  - BearerAuth: []

tags:
  - name: Authentication
    description: User authentication and session management`,
    metadata: {
      lastModified: '2024-01-14T15:45:00Z',
      tags: ['api', 'authentication', 'security', 'jwt']
    }
  }
}

// Load YAML content based on slug
const { data: yamlContent, pending, error } = await useLazyAsyncData(
  `yaml-${slug.value}`,
  async () => {
    // In a real app, you would fetch from the content API
    // const content = await queryContent(slug.value).findOne()
    
    const content = yamlFiles[slug.value as keyof typeof yamlFiles]
    if (!content) {
      throw createError({
        statusCode: 404,
        statusMessage: 'YAML file not found'
      })
    }
    
    return content
  }
)

// Generate breadcrumbs
const breadcrumbs = computed(() => {
  const parts = slug.value.split('/')
  const crumbs = []
  
  for (let i = 0; i < parts.length; i++) {
    const path = '/' + parts.slice(0, i + 1).join('/')
    const title = parts[i].charAt(0).toUpperCase() + parts[i].slice(1)
    
    crumbs.push({
      title,
      path: i === parts.length - 1 ? undefined : path // Don't link the current page
    })
  }
  
  return crumbs
})

// Set page meta
useHead(() => ({
  title: yamlContent.value ? `${yamlContent.value.title} - YAML Viewer` : 'Loading...',
  meta: [
    { 
      name: 'description', 
      content: yamlContent.value?.description || 'YAML documentation file' 
    }
  ]
}))
</script>
