<template>
  <div>
    <div v-if="pending" class="flex items-center justify-center py-12">
      <div class="text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
        <p class="text-muted-foreground">Loading...</p>
      </div>
    </div>

    <div v-else-if="error" class="text-center py-12">
      <div class="max-w-md mx-auto">
        <div class="p-6 border border-destructive/20 rounded-lg bg-destructive/5">
          <AlertCircle class="h-12 w-12 text-destructive mx-auto mb-4" />
          <h2 class="text-xl font-semibold mb-2">File Not Found</h2>
          <p class="text-muted-foreground mb-4">
            The requested YAML file could not be found.
          </p>
          <Button as="NuxtLink" to="/" variant="outline">
            Go Home
          </Button>
        </div>
      </div>
    </div>

    <YamlViewer v-else-if="yamlContent" :content="yamlContent.content" :title="yamlContent.title"
      :description="yamlContent.description" :filename="yamlContent.filename" :breadcrumbs="breadcrumbs"
      :metadata="yamlContent.metadata" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { AlertCircle } from 'lucide-vue-next'
import { Button } from '~/components/ui/button'

const route = useRoute()
const router = useRouter()

// Get the slug from the route
const slug = computed(() => {
  const slugArray = Array.isArray(route.params.slug) ? route.params.slug : [route.params.slug]
  return slugArray.join('/')
})

// Load YAML content using Nuxt Content
const { data: yamlContent, pending, error } = await useLazyAsyncData(
  `yaml-${slug.value}`,
  async () => {
    try {
      // Query the content using Nuxt Content
      const content = await queryContent(slug.value).findOne()

      if (!content) {
        throw createError({
          statusCode: 404,
          statusMessage: 'YAML file not found'
        })
      }

      // Read the raw YAML file content
      const yamlPath = `content${slug.value}.yaml`
      let rawContent = ''

      try {
        // Try to read the raw file content for display
        const response = await $fetch(`/api/content-raw?path=${slug.value}`)
        rawContent = response.content
      } catch (err) {
        // Fallback: use a placeholder if raw content can't be loaded
        rawContent = `# ${content.title || 'YAML Content'}\n# Raw content not available`
      }

      return {
        title: content.title || 'Untitled',
        description: content.description || '',
        filename: `${slug.value.split('/').pop()}.yaml`,
        content: rawContent,
        metadata: {
          lastModified: content.updatedAt || content.createdAt || new Date().toISOString(),
          tags: content.tags || []
        }
      }
    } catch (err) {
      throw createError({
        statusCode: 404,
        statusMessage: 'YAML file not found'
      })
    }
  }
)

// Generate breadcrumbs
info:
title: Sample API Documentation
description: |
  This specification covers the core concepts of the OpenAPI specification.
    It includes examples of common patterns and best practices for API design.
  version: 1.0.0
contact:
name: API Support
email: support @example.com
url: https://example.com/support
license:
name: MIT
url: https://opensource.org/licenses/MIT

servers:
- url: https://api.example.com/v1
description: Production server
  - url: https://staging-api.example.com/v1
description: Staging server
  - url: http://localhost:3000/v1
description: Development server

# The Basics
# Each API specification starts with the OpenAPI version and basic information
# about the API.This includes the title, description, version, and contact details.

  paths:
  /users:
get:
summary: List all users
description: Retrieve a paginated list of users
operationId: listUsers
tags:
- Users
parameters:
- name: page
  in: query
description: Page number for pagination
          required: false
schema:
type: integer
minimum: 1
            default: 1
  - name: limit
    in: query
description: Number of items per page
required: false
schema:
type: integer
minimum: 1
maximum: 100
            default: 20
responses:
'200':
description: Successful response
content:
application / json:
schema:
type: object
properties:
data:
type: array
items:
$ref: '#/components/schemas/User'
pagination:
$ref: '#/components/schemas/Pagination'
'400':
description: Bad request
content:
application / json:
schema:
$ref: '#/components/schemas/Error'

components:
schemas:
User:
type: object
required:
- id
  - email
  - name
  - createdAt
properties:
id:
type: string
format: uuid
description: Unique identifier for the user
        email:
  type: string
format: email
description: User's email address
name:
type: string
description: User's full name
avatar:
type: string
format: uri
description: URL to user's avatar image
createdAt:
type: string
format: date - time
description: When the user account was created

securitySchemes:
BearerAuth:
type: http
scheme: bearer
bearerFormat: JWT

security:
- BearerAuth: []

tags:
- name: Users
description: User management operations`,
    metadata: {
      lastModified: '2024-01-15T10:30:00Z',
      tags: ['api', 'openapi', 'basics']
    }
  },
  'api/authentication': {
    title: 'Authentication',
    description: 'Authentication methods and security patterns',
    filename: 'authentication.yaml',
    content: `# Authentication & Authorization
# This section covers authentication methods and security patterns

openapi: 3.0.3
info:
title: Authentication API
description: |
  Authentication and authorization patterns for secure API access.
    Covers JWT tokens, OAuth2, API keys, and role - based access control.
  version: 1.0.0

servers:
- url: https://auth.example.com/v1
description: Authentication server

paths:
/auth/login:
post:
summary: User login
description: Authenticate user with email and password
operationId: login
tags:
- Authentication
requestBody:
required: true
content:
application / json:
schema:
type: object
required:
- email
  - password
properties:
email:
type: string
format: email
example: user @example.com
password:
type: string
format: password
example: secretpassword123
responses:
'200':
description: Login successful
content:
application / json:
schema:
type: object
properties:
accessToken:
type: string
description: JWT access token
refreshToken:
type: string
description: Refresh token for obtaining new access tokens
                  expiresIn:
  type: integer
description: Token expiration time in seconds
example: 3600
'401':
description: Invalid credentials

components:
securitySchemes:
BearerAuth:
type: http
scheme: bearer
bearerFormat: JWT
description: |
  JWT Bearer token authentication.
        
        To authenticate with the API:
1. Login with your credentials to get an access token
2. Include the token in the Authorization header: \`Bearer <token>\`
        3. Use the refresh token to get new access tokens when they expire

security:
  - BearerAuth: []

tags:
  - name: Authentication
    description: User authentication and session management`,
  metadata: {
  lastModified: '2024-01-14T15:45:00Z',
    tags: ['api', 'authentication', 'security', 'jwt']
}
  },
'configuration/database': {
  title: 'Database Configuration',
    description: 'Database connection and management settings',
      filename: 'database.yaml',
        content: `# Database Configuration
# Configuration settings for database connections and management

# Primary Database Configuration
database:
  # Connection settings
  connection:
    host: localhost
    port: 5432
    database: myapp_production
    username: \${DB_USERNAME}
    password: \${DB_PASSWORD}
    ssl:
      enabled: true
      mode: require
      ca_cert: /path/to/ca-certificate.crt

  # Connection pool settings
  pool:
    min_connections: 5
    max_connections: 20
    idle_timeout: 30s
    max_lifetime: 1h
    health_check_period: 30s

  # Query settings
  query:
    timeout: 30s
    max_rows: 10000
    log_slow_queries: true
    slow_query_threshold: 1s

# Read Replicas Configuration
read_replicas:
  - name: replica_1
    connection:
      host: replica1.example.com
      port: 5432
      database: myapp_production
      username: \${DB_REPLICA_USERNAME}
      password: \${DB_REPLICA_PASSWORD}
      ssl:
        enabled: true
        mode: require
    pool:
      min_connections: 2
      max_connections: 10
      idle_timeout: 30s

# Cache Configuration
cache:
  redis:
    # Primary Redis instance
    primary:
      host: redis.example.com
      port: 6379
      password: \${REDIS_PASSWORD}
      database: 0
      ssl:
        enabled: true
        skip_verify: false
      pool:
        max_connections: 100
        min_idle_connections: 10
        dial_timeout: 5s
        read_timeout: 3s
        write_timeout: 3s
        pool_timeout: 4s
        idle_timeout: 5m

# Backup Configuration
backup:
  enabled: true
  schedule: "0 2 * * *"  # Daily at 2 AM
  retention:
    daily: 7    # Keep 7 daily backups
    weekly: 4   # Keep 4 weekly backups
    monthly: 12 # Keep 12 monthly backups

  storage:
    type: s3
    bucket: myapp-database-backups
    region: us-west-2
    access_key: \${AWS_ACCESS_KEY_ID}
    secret_key: \${AWS_SECRET_ACCESS_KEY}
    encryption: true`,
          metadata: {
    lastModified: '2024-01-13T09:15:00Z',
      tags: ['configuration', 'database', 'postgresql', 'redis']
  }
},
'configuration/server': {
  title: 'Server Configuration',
    description: 'Main server configuration settings',
      filename: 'server.yaml',
        content: `# Server Configuration
# Main server configuration settings for the application

# Server Settings
server:
  # Basic server configuration
  host: 0.0.0.0
  port: 8080
  name: MyApp Server
  version: 1.0.0

  # Environment
  environment: production
  debug: false

  # Timeouts
  timeouts:
    read: 30s
    write: 30s
    idle: 120s
    shutdown: 30s

  # TLS/SSL Configuration
  tls:
    enabled: true
    cert_file: /etc/ssl/certs/server.crt
    key_file: /etc/ssl/private/server.key
    min_version: "1.2"
    max_version: "1.3"

# Load Balancer Configuration
load_balancer:
  enabled: true
  algorithm: round_robin  # round_robin, least_connections, ip_hash
  health_check:
    enabled: true
    path: /health
    interval: 30s
    timeout: 5s
    healthy_threshold: 2
    unhealthy_threshold: 3

  # Upstream servers
  upstreams:
    - name: app-server-1
      address: *********:8080
      weight: 1
      max_fails: 3
      fail_timeout: 30s

    - name: app-server-2
      address: *********:8080
      weight: 1
      max_fails: 3
      fail_timeout: 30s

# Rate Limiting
rate_limiting:
  enabled: true

  # Global rate limits
  global:
    requests_per_second: 1000
    burst: 2000

  # Per-IP rate limits
  per_ip:
    requests_per_minute: 60
    burst: 120`,
          metadata: {
    lastModified: '2024-01-12T14:30:00Z',
      tags: ['configuration', 'server', 'load-balancer', 'security']
  }
},
'deployment/docker': {
  title: 'Docker Deployment',
    description: 'Docker and Docker Compose configuration',
      filename: 'docker.yaml',
        content: `# Docker Deployment Configuration
# Configuration for containerized deployment using Docker and Docker Compose

# Docker Compose Configuration
version: '3.8'

services:
  # Application Service
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
      args:
        NODE_ENV: production
        BUILD_VERSION: \${BUILD_VERSION:-latest}

    image: myapp:\${TAG:-latest}
    container_name: myapp-server

    restart: unless-stopped

    ports:
      - "8080:8080"
      - "9090:9090"  # Metrics port

    environment:
      - NODE_ENV=production
      - PORT=8080
      - DATABASE_URL=\${DATABASE_URL}
      - REDIS_URL=\${REDIS_URL}
      - JWT_SECRET=\${JWT_SECRET}
      - API_KEY=\${API_KEY}

    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
      - /etc/ssl/certs:/etc/ssl/certs:ro

    networks:
      - app-network
      - db-network

    depends_on:
      database:
        condition: service_healthy
      redis:
        condition: service_healthy

  # Database Service
  database:
    image: postgres:15-alpine
    container_name: myapp-postgres

    restart: unless-stopped

    environment:
      - POSTGRES_DB=\${DB_NAME:-myapp}
      - POSTGRES_USER=\${DB_USER:-postgres}
      - POSTGRES_PASSWORD=\${DB_PASSWORD}

    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d:ro

    ports:
      - "5432:5432"

    networks:
      - db-network

# Networks
networks:
  app-network:
    driver: bridge
  db-network:
    driver: bridge
    internal: true

# Volumes
volumes:
  postgres_data:
    driver: local`,
          metadata: {
    lastModified: '2024-01-11T16:20:00Z',
      tags: ['deployment', 'docker', 'containers', 'infrastructure']
  }
}
}

// Load YAML content based on slug
const { data: yamlContent, pending, error } = await useLazyAsyncData(
  `yaml-${slug.value}`,
  async () => {
    // In a real app, you would fetch from the content API
    // const content = await queryContent(slug.value).findOne()

    const content = yamlFiles[slug.value as keyof typeof yamlFiles]
    if (!content) {
      throw createError({
        statusCode: 404,
        statusMessage: 'YAML file not found'
      })
    }

    return content
  }
)

// Generate breadcrumbs
const breadcrumbs = computed(() => {
  const parts = slug.value.split('/')
  const crumbs = []

  for (let i = 0; i < parts.length; i++) {
    const path = '/' + parts.slice(0, i + 1).join('/')
    const title = parts[i].charAt(0).toUpperCase() + parts[i].slice(1)

    crumbs.push({
      title,
      path: i === parts.length - 1 ? undefined : path // Don't link the current page
    })
  }

  return crumbs
})

// Set page meta
useHead(() => ({
  title: yamlContent.value ? `${yamlContent.value.title} - YAML Viewer` : 'Loading...',
  meta: [
    {
      name: 'description',
      content: yamlContent.value?.description || 'YAML documentation file'
    }
  ]
}))
</script>
