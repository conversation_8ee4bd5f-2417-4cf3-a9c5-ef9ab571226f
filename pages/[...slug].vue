<template>
  <div>
    <div v-if="pending" class="flex items-center justify-center py-12">
      <div class="text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
        <p class="text-muted-foreground">Loading...</p>
      </div>
    </div>

    <div v-else-if="error" class="text-center py-12">
      <div class="max-w-md mx-auto">
        <div class="p-6 border border-destructive/20 rounded-lg bg-destructive/5">
          <AlertCircle class="h-12 w-12 text-destructive mx-auto mb-4" />
          <h2 class="text-xl font-semibold mb-2">File Not Found</h2>
          <p class="text-muted-foreground mb-4">
            The requested YAML file could not be found.
          </p>
          <Button as="NuxtLink" to="/" variant="outline">
            Go Home
          </Button>
        </div>
      </div>
    </div>

    <YamlViewer v-else-if="yamlContent" :content="yamlContent.content" :title="yamlContent.title"
      :description="yamlContent.description" :filename="yamlContent.filename" :breadcrumbs="breadcrumbs"
      :metadata="yamlContent.metadata" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { AlertCircle } from 'lucide-vue-next'
import { Button } from '~/components/ui/button'

const route = useRoute()

// Get the slug from the route
const slug = computed(() => {
  const slugArray = Array.isArray(route.params.slug) ? route.params.slug : [route.params.slug]
  return slugArray.join('/')
})

// Load YAML content using Nuxt Content
const { data: yamlContent, pending, error } = await useLazyAsyncData(
  `yaml-${slug.value}`,
  async () => {
    try {
      // Query the content using Nuxt Content
      const content = await queryContent(slug.value).findOne()

      if (!content) {
        throw createError({
          statusCode: 404,
          statusMessage: 'YAML file not found'
        })
      }

      // Try to get the raw YAML content
      let rawContent = ''
      try {
        const response = await $fetch(`/api/content-raw?path=${slug.value}`)
        rawContent = response.content
      } catch (err) {
        // Fallback to processed content
        rawContent = convertContentToYaml(content)
      }

      return {
        title: content.title || 'Untitled',
        description: content.description || '',
        filename: `${slug.value.split('/').pop()}.yaml`,
        content: rawContent,
        metadata: {
          lastModified: content.updatedAt || content.createdAt || new Date().toISOString(),
          tags: content.tags || []
        }
      }
    } catch (err) {
      throw createError({
        statusCode: 404,
        statusMessage: 'YAML file not found'
      })
    }
  }
)

// Helper function to convert content back to YAML format for display
function convertContentToYaml(content: any): string {
  // Remove Nuxt Content specific fields
  const { _path, _dir, _draft, _partial, _locale, _type, _id, _source, _file, _extension, ...yamlContent } = content

  // Convert to YAML-like string representation
  return `# ${content.title || 'YAML Content'}
${content.description ? `# ${content.description}\n` : ''}
# File: ${content._path}
# Last modified: ${content.updatedAt || content.createdAt || 'Unknown'}

${JSON.stringify(yamlContent, null, 2).replace(/"/g, '').replace(/,/g, '').replace(/\{/g, '').replace(/\}/g, '').replace(/\[/g, '').replace(/\]/g, '')}`
}

// Generate breadcrumbs
const breadcrumbs = computed(() => {
  const parts = slug.value.split('/')
  const crumbs = []

  for (let i = 0; i < parts.length; i++) {
    const path = '/' + parts.slice(0, i + 1).join('/')
    const title = parts[i].charAt(0).toUpperCase() + parts[i].slice(1)

    crumbs.push({
      title,
      path: i === parts.length - 1 ? undefined : path // Don't link the current page
    })
  }

  return crumbs
})

// Set page meta
useHead(() => ({
  title: yamlContent.value ? `${yamlContent.value.title} - YAML Viewer` : 'Loading...',
  meta: [
    {
      name: 'description',
      content: yamlContent.value?.description || 'YAML documentation file'
    }
  ]
}))
</script>
