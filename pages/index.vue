<template>
  <div>
    <!-- Hero Section -->
    <div class="text-center mb-12">
      <h1 class="text-4xl font-bold mb-4">YAML Documentation Viewer</h1>
      <p class="text-xl text-muted-foreground mb-8">
        Modern documentation site for viewing and navigating YAML files with syntax highlighting
      </p>
      <div class="flex justify-center gap-4">
        <Button as="NuxtLink" to="/api/basics" size="lg">
          Get Started
        </Button>
        <Button variant="outline" size="lg" @click="scrollToFeatures">
          Learn More
        </Button>
      </div>
    </div>

    <!-- Features Grid -->
    <div ref="featuresSection" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
      <div class="p-6 border rounded-lg">
        <div class="flex items-center gap-3 mb-3">
          <div class="p-2 bg-primary/10 rounded-lg">
            <FileText class="h-5 w-5 text-primary" />
          </div>
          <h3 class="font-semibold">Syntax Highlighting</h3>
        </div>
        <p class="text-muted-foreground">
          Beautiful syntax highlighting for YAML files with proper formatting and color coding.
        </p>
      </div>

      <div class="p-6 border rounded-lg">
        <div class="flex items-center gap-3 mb-3">
          <div class="p-2 bg-primary/10 rounded-lg">
            <Search class="h-5 w-5 text-primary" />
          </div>
          <h3 class="font-semibold">Powerful Search</h3>
        </div>
        <p class="text-muted-foreground">
          Find specific YAML files or content within files using fuzzy search functionality.
        </p>
      </div>

      <div class="p-6 border rounded-lg">
        <div class="flex items-center gap-3 mb-3">
          <div class="p-2 bg-primary/10 rounded-lg">
            <FolderTree class="h-5 w-5 text-primary" />
          </div>
          <h3 class="font-semibold">Tree Navigation</h3>
        </div>
        <p class="text-muted-foreground">
          Hierarchical tree navigation showing the structure of your YAML documentation.
        </p>
      </div>

      <div class="p-6 border rounded-lg">
        <div class="flex items-center gap-3 mb-3">
          <div class="p-2 bg-primary/10 rounded-lg">
            <Smartphone class="h-5 w-5 text-primary" />
          </div>
          <h3 class="font-semibold">Responsive Design</h3>
        </div>
        <p class="text-muted-foreground">
          Works perfectly on all devices with mobile-friendly navigation and layout.
        </p>
      </div>

      <div class="p-6 border rounded-lg">
        <div class="flex items-center gap-3 mb-3">
          <div class="p-2 bg-primary/10 rounded-lg">
            <Copy class="h-5 w-5 text-primary" />
          </div>
          <h3 class="font-semibold">Easy Copy & Download</h3>
        </div>
        <p class="text-muted-foreground">
          Copy YAML content to clipboard or download files with a single click.
        </p>
      </div>

      <div class="p-6 border rounded-lg">
        <div class="flex items-center gap-3 mb-3">
          <div class="p-2 bg-primary/10 rounded-lg">
            <Palette class="h-5 w-5 text-primary" />
          </div>
          <h3 class="font-semibold">Dark Mode</h3>
        </div>
        <p class="text-muted-foreground">
          Toggle between light and dark themes for comfortable viewing in any environment.
        </p>
      </div>
    </div>

    <!-- Quick Start -->
    <div class="border rounded-lg p-8 mb-12">
      <h2 class="text-2xl font-bold mb-4">Quick Start</h2>
      <p class="text-muted-foreground mb-6">
        Explore our sample YAML documentation to see the viewer in action:
      </p>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <NuxtLink to="/api/basics" class="p-4 border rounded-lg hover:bg-accent transition-colors group">
          <div class="flex items-center gap-3 mb-2">
            <FileText class="h-5 w-5 text-primary" />
            <h3 class="font-medium group-hover:text-primary">API Basics</h3>
          </div>
          <p class="text-sm text-muted-foreground">
            Core concepts of the OpenAPI specification
          </p>
        </NuxtLink>

        <NuxtLink to="/configuration/database" class="p-4 border rounded-lg hover:bg-accent transition-colors group">
          <div class="flex items-center gap-3 mb-2">
            <FileText class="h-5 w-5 text-primary" />
            <h3 class="font-medium group-hover:text-primary">Database Config</h3>
          </div>
          <p class="text-sm text-muted-foreground">
            Database connection and management settings
          </p>
        </NuxtLink>
      </div>
    </div>

    <!-- About -->
    <div class="text-center">
      <h2 class="text-2xl font-bold mb-4">Built with Modern Technologies</h2>
      <p class="text-muted-foreground mb-6">
        This documentation site is built using Nuxt.js, Shadcn Vue components, and Tailwind CSS
        for a modern, fast, and accessible user experience.
      </p>
      <div class="flex justify-center gap-6 text-sm text-muted-foreground">
        <div class="flex items-center gap-2">
          <div class="w-2 h-2 bg-green-500 rounded-full"></div>
          Nuxt.js 3
        </div>
        <div class="flex items-center gap-2">
          <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
          Vue 3
        </div>
        <div class="flex items-center gap-2">
          <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
          Tailwind CSS
        </div>
        <div class="flex items-center gap-2">
          <div class="w-2 h-2 bg-orange-500 rounded-full"></div>
          Shadcn Vue
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  FileText,
  Search,
  FolderTree,
  Smartphone,
  Copy,
  Palette
} from 'lucide-vue-next'
import { Button } from '~/components/ui/button'

const featuresSection = ref<HTMLElement>()

const scrollToFeatures = () => {
  featuresSection.value?.scrollIntoView({ behavior: 'smooth' })
}

// Set page meta
useHead({
  title: 'YAML Viewer - Modern Documentation Site',
  meta: [
    {
      name: 'description',
      content: 'Modern documentation site for viewing and navigating YAML files with syntax highlighting, search functionality, and responsive design.'
    }
  ]
})
</script>
