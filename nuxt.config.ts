import tailwindcss from "@tailwindcss/vite";

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-05-15',
  devtools: { enabled: true },
  modules: [
    '@nuxt/content',
    '@nuxt/fonts',
    '@nuxt/test-utils',
    'shadcn-nuxt',
    '@nuxtjs/color-mode'
  ],
  css: ['~/assets/css/tailwind.css'],
  vite: {
    plugins: [
      tailwindcss()
    ]
  },
  shadcn: {
    prefix: '',
    componentDir: './components/ui',
  },

  content: {
    // Content configuration
  }
})