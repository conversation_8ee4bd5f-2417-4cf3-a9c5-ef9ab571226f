# Docker Deployment Configuration
# Configuration for containerized deployment using <PERSON><PERSON> and <PERSON>er Compose

# Docker Compose Configuration
version: '3.8'

services:
  # Application Service
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
      args:
        NODE_ENV: production
        BUILD_VERSION: ${BUILD_VERSION:-latest}
    
    image: myapp:${TAG:-latest}
    container_name: myapp-server
    
    restart: unless-stopped
    
    ports:
      - "8080:8080"
      - "9090:9090"  # Metrics port
    
    environment:
      - NODE_ENV=production
      - PORT=8080
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - JWT_SECRET=${JWT_SECRET}
      - API_KEY=${API_KEY}
    
    env_file:
      - .env.production
    
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
      - /etc/ssl/certs:/etc/ssl/certs:ro
    
    networks:
      - app-network
      - db-network
    
    depends_on:
      database:
        condition: service_healthy
      redis:
        condition: service_healthy
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3

  # Database Service
  database:
    image: postgres:15-alpine
    container_name: myapp-postgres
    
    restart: unless-stopped
    
    environment:
      - POSTGRES_DB=${DB_NAME:-myapp}
      - POSTGRES_USER=${DB_USER:-postgres}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_INITDB_ARGS=--auth-host=scram-sha-256
    
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d:ro
      - ./backups:/backups
    
    ports:
      - "5432:5432"
    
    networks:
      - db-network
    
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-postgres} -d ${DB_NAME:-myapp}"]
      interval: 10s
      timeout: 5s
      retries: 5
    
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  # Redis Service
  redis:
    image: redis:7-alpine
    container_name: myapp-redis
    
    restart: unless-stopped
    
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf:ro
    
    ports:
      - "6379:6379"
    
    networks:
      - app-network
    
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: myapp-nginx
    
    restart: unless-stopped
    
    ports:
      - "80:80"
      - "443:443"
    
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    
    networks:
      - app-network
    
    depends_on:
      - app
    
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: myapp-prometheus
    
    restart: unless-stopped
    
    ports:
      - "9091:9090"
    
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    
    networks:
      - monitoring-network
      - app-network

  # Monitoring - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: myapp-grafana
    
    restart: unless-stopped
    
    ports:
      - "3000:3000"
    
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    
    networks:
      - monitoring-network
    
    depends_on:
      - prometheus

# Networks
networks:
  app-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
  
  db-network:
    driver: bridge
    internal: true
    ipam:
      config:
        - subnet: **********/16
  
  monitoring-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Volumes
volumes:
  postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/lib/docker/volumes/myapp_postgres
  
  redis_data:
    driver: local
  
  prometheus_data:
    driver: local
  
  grafana_data:
    driver: local

# Docker Swarm Configuration (for production clusters)
x-deploy-config: &deploy-config
  replicas: 3
  update_config:
    parallelism: 1
    delay: 10s
    failure_action: rollback
    order: start-first
  rollback_config:
    parallelism: 1
    delay: 10s
    failure_action: pause
    order: stop-first
  restart_policy:
    condition: on-failure
    delay: 5s
    max_attempts: 3
    window: 120s

# Production overrides
x-production-overrides:
  app:
    deploy:
      <<: *deploy-config
      placement:
        constraints:
          - node.role == worker
          - node.labels.tier == app
      resources:
        limits:
          cpus: '4.0'
          memory: 4G
        reservations:
          cpus: '2.0'
          memory: 2G
