# Server Configuration
# Main server configuration settings for the application

# Server Settings
server:
  # Basic server configuration
  host: 0.0.0.0
  port: 8080
  name: MyApp Server
  version: 1.0.0
  
  # Environment
  environment: production
  debug: false
  
  # Timeouts
  timeouts:
    read: 30s
    write: 30s
    idle: 120s
    shutdown: 30s
  
  # TLS/SSL Configuration
  tls:
    enabled: true
    cert_file: /etc/ssl/certs/server.crt
    key_file: /etc/ssl/private/server.key
    min_version: "1.2"
    max_version: "1.3"
    cipher_suites:
      - TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384
      - TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305
      - TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256
  
  # CORS Configuration
  cors:
    enabled: true
    allowed_origins:
      - https://example.com
      - https://app.example.com
      - https://admin.example.com
    allowed_methods:
      - GET
      - POST
      - PUT
      - DELETE
      - OPTIONS
    allowed_headers:
      - Content-Type
      - Authorization
      - X-Requested-With
      - X-API-Key
    exposed_headers:
      - X-Total-Count
      - X-Page-Count
    allow_credentials: true
    max_age: 86400

# Load Balancer Configuration
load_balancer:
  enabled: true
  algorithm: round_robin  # round_robin, least_connections, ip_hash
  health_check:
    enabled: true
    path: /health
    interval: 30s
    timeout: 5s
    healthy_threshold: 2
    unhealthy_threshold: 3
  
  # Upstream servers
  upstreams:
    - name: app-server-1
      address: *********:8080
      weight: 1
      max_fails: 3
      fail_timeout: 30s
    
    - name: app-server-2
      address: *********:8080
      weight: 1
      max_fails: 3
      fail_timeout: 30s
    
    - name: app-server-3
      address: *********:8080
      weight: 2  # Higher weight for more powerful server
      max_fails: 3
      fail_timeout: 30s

# Rate Limiting
rate_limiting:
  enabled: true
  
  # Global rate limits
  global:
    requests_per_second: 1000
    burst: 2000
    
  # Per-IP rate limits
  per_ip:
    requests_per_minute: 60
    burst: 120
    
  # Per-user rate limits (authenticated users)
  per_user:
    requests_per_minute: 300
    burst: 600
    
  # API key rate limits
  api_key:
    requests_per_minute: 1000
    burst: 2000
  
  # Rate limit storage
  storage:
    type: redis
    connection: ${REDIS_URL}
    key_prefix: "ratelimit:"

# Security Configuration
security:
  # Security headers
  headers:
    x_frame_options: DENY
    x_content_type_options: nosniff
    x_xss_protection: "1; mode=block"
    strict_transport_security: "max-age=31536000; includeSubDomains"
    content_security_policy: "default-src 'self'; script-src 'self' 'unsafe-inline'"
    referrer_policy: strict-origin-when-cross-origin
  
  # Request size limits
  limits:
    max_request_size: 10MB
    max_header_size: 1MB
    max_multipart_memory: 32MB
  
  # IP filtering
  ip_filtering:
    enabled: true
    whitelist:
      - 10.0.0.0/8
      - **********/12
      - ***********/16
    blacklist:
      - *************
      - *********

# Logging Configuration
logging:
  level: info  # debug, info, warn, error
  format: json  # json, text
  output: stdout  # stdout, stderr, file
  
  # File logging (when output is file)
  file:
    path: /var/log/myapp/server.log
    max_size: 100MB
    max_backups: 5
    max_age: 30  # days
    compress: true
  
  # Request logging
  access_log:
    enabled: true
    format: combined  # combined, common, custom
    exclude_paths:
      - /health
      - /metrics
      - /favicon.ico
  
  # Structured logging fields
  fields:
    service: myapp
    version: 1.0.0
    environment: ${ENVIRONMENT}

# Metrics and Monitoring
metrics:
  enabled: true
  path: /metrics
  port: 9090  # Separate port for metrics
  
  # Prometheus configuration
  prometheus:
    enabled: true
    namespace: myapp
    subsystem: server
    
  # Custom metrics
  custom:
    - name: request_duration_histogram
      type: histogram
      help: Request duration in seconds
      buckets: [0.1, 0.5, 1.0, 2.5, 5.0, 10.0]
    
    - name: active_connections_gauge
      type: gauge
      help: Number of active connections

# Health Checks
health:
  enabled: true
  path: /health
  
  # Health check components
  checks:
    - name: database
      type: database
      timeout: 5s
      critical: true
    
    - name: redis
      type: redis
      timeout: 3s
      critical: false
    
    - name: external_api
      type: http
      url: https://api.external-service.com/health
      timeout: 10s
      critical: false

# Graceful Shutdown
shutdown:
  timeout: 30s
  signals:
    - SIGTERM
    - SIGINT
  
  # Pre-shutdown hooks
  hooks:
    - name: drain_connections
      timeout: 15s
    - name: flush_metrics
      timeout: 5s
    - name: cleanup_resources
      timeout: 10s

# Development Settings
development:
  hot_reload: true
  auto_restart: true
  debug_endpoints: true
  profiling:
    enabled: true
    path: /debug/pprof/
