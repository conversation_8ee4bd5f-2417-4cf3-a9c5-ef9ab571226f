# Database Configuration
# Configuration settings for database connections and management

# Primary Database Configuration
database:
  # Connection settings
  connection:
    host: localhost
    port: 5432
    database: myapp_production
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    ssl:
      enabled: true
      mode: require
      ca_cert: /path/to/ca-certificate.crt
      client_cert: /path/to/client-certificate.crt
      client_key: /path/to/client-key.key

  # Connection pool settings
  pool:
    min_connections: 5
    max_connections: 20
    idle_timeout: 30s
    max_lifetime: 1h
    health_check_period: 30s

  # Query settings
  query:
    timeout: 30s
    max_rows: 10000
    log_slow_queries: true
    slow_query_threshold: 1s

  # Migration settings
  migrations:
    enabled: true
    directory: ./migrations
    table: schema_migrations
    auto_migrate: false

# Read Replicas Configuration
read_replicas:
  - name: replica_1
    connection:
      host: replica1.example.com
      port: 5432
      database: myapp_production
      username: ${DB_REPLICA_USERNAME}
      password: ${DB_REPLICA_PASSWORD}
      ssl:
        enabled: true
        mode: require
    pool:
      min_connections: 2
      max_connections: 10
      idle_timeout: 30s

  - name: replica_2
    connection:
      host: replica2.example.com
      port: 5432
      database: myapp_production
      username: ${DB_REPLICA_USERNAME}
      password: ${DB_REPLICA_PASSWORD}
      ssl:
        enabled: true
        mode: require
    pool:
      min_connections: 2
      max_connections: 10
      idle_timeout: 30s

# Cache Configuration
cache:
  redis:
    # Primary Redis instance
    primary:
      host: redis.example.com
      port: 6379
      password: ${REDIS_PASSWORD}
      database: 0
      ssl:
        enabled: true
        skip_verify: false
      pool:
        max_connections: 100
        min_idle_connections: 10
        dial_timeout: 5s
        read_timeout: 3s
        write_timeout: 3s
        pool_timeout: 4s
        idle_timeout: 5m

    # Redis Sentinel for high availability
    sentinel:
      enabled: true
      master_name: mymaster
      addresses:
        - sentinel1.example.com:26379
        - sentinel2.example.com:26379
        - sentinel3.example.com:26379
      password: ${REDIS_SENTINEL_PASSWORD}

  # Memory cache settings
  memory:
    max_size: 100MB
    ttl: 1h
    cleanup_interval: 10m

# Backup Configuration
backup:
  enabled: true
  schedule: "0 2 * * *"  # Daily at 2 AM
  retention:
    daily: 7    # Keep 7 daily backups
    weekly: 4   # Keep 4 weekly backups
    monthly: 12 # Keep 12 monthly backups
  
  storage:
    type: s3
    bucket: myapp-database-backups
    region: us-west-2
    access_key: ${AWS_ACCESS_KEY_ID}
    secret_key: ${AWS_SECRET_ACCESS_KEY}
    encryption: true
    
  compression:
    enabled: true
    algorithm: gzip
    level: 6

# Monitoring and Logging
monitoring:
  metrics:
    enabled: true
    interval: 30s
    endpoint: /metrics
    
  health_checks:
    enabled: true
    interval: 10s
    timeout: 5s
    
  logging:
    level: info
    format: json
    output: stdout
    
    # Log specific database events
    log_queries: false
    log_slow_queries: true
    log_connections: true
    log_disconnections: true

# Environment-specific overrides
environments:
  development:
    database:
      connection:
        host: localhost
        database: myapp_development
        ssl:
          enabled: false
      pool:
        max_connections: 5
      query:
        log_slow_queries: false
    
    cache:
      redis:
        primary:
          host: localhost
          ssl:
            enabled: false
        sentinel:
          enabled: false
    
    backup:
      enabled: false
    
    monitoring:
      logging:
        level: debug
        log_queries: true

  test:
    database:
      connection:
        host: localhost
        database: myapp_test
        ssl:
          enabled: false
      pool:
        max_connections: 3
      migrations:
        auto_migrate: true
    
    cache:
      memory:
        max_size: 10MB
        ttl: 5m
    
    backup:
      enabled: false
    
    monitoring:
      metrics:
        enabled: false
      health_checks:
        enabled: false

  staging:
    database:
      connection:
        host: staging-db.example.com
        database: myapp_staging
      pool:
        max_connections: 10
    
    cache:
      redis:
        primary:
          host: staging-redis.example.com
        sentinel:
          enabled: false
    
    backup:
      schedule: "0 4 * * *"  # Daily at 4 AM
      retention:
        daily: 3
        weekly: 2
        monthly: 6
