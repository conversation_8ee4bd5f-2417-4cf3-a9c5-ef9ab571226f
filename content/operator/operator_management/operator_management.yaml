system: operator_management
description: "Villiers.ai Operator Management Domain - Comprehensive operator lifecycle management system encompassing operator onboarding, profile administration, fleet coordination, contact management, data enrichment, compliance verification, and business relationship optimization for charter operator network management"

intent_assertions:
- "Complete operator lifecycle management from initial registration through ongoing relationship optimization with comprehensive profile administration"
- "Advanced operator onboarding workflow with automated data enrichment, compliance verification, and fleet integration capabilities"
- "Sophisticated fleet management integration with aircraft assignment, availability synchronization, and operational constraint tracking"
- "Comprehensive contact management system with multi-representative support, role-based access, and communication preference coordination"
- "Automated data enrichment through web scraping, external API integration, and intelligent profile completion with quality validation"
- "Robust compliance verification framework with regulatory requirement validation, certification tracking, and audit trail maintenance"
- "Advanced search and filtering capabilities with reliability-based ranking, category specialization, and regional operation filtering"
- "Comprehensive CRUD operations with transaction management, validation frameworks, and error handling with recovery mechanisms"
- "Real-time profile updates with change tracking, audit trails, and automated notification systems for stakeholder coordination"
- "Zero operator data inconsistencies through comprehensive validation, relationship integrity checking, and automated quality assurance"

technical_assertions:
  # Core Operator Management API
  - path: "app/api/v1/endpoints/admin/admin_ui.py"
    purpose: "Primary admin operator management API with comprehensive CRUD operations and advanced filtering"
    lines: 1450
    subdomain: "operator_management"
    endpoints: [
      "GET /api/v1/admin/operators - Paginated operator list with search, status filtering, and performance metrics",
      "POST /api/v1/admin/operators - Create new operator with validation and relationship establishment",
      "GET /api/v1/admin/operators/{operator_id} - Detailed operator information with fleet and performance data",
      "PUT /api/v1/admin/operators/{operator_id} - Update operator profile with validation and audit tracking",
      "DELETE /api/v1/admin/operators/{operator_id} - Deactivate or remove operator with dependency management"
    ]
    features: [
      "Paginated listing with 20 items per page default and 100 item maximum",
      "Advanced search by operator name with partial matching and fuzzy search capabilities",
      "Status filtering with active, inactive, and custom status support",
      "Performance metrics integration with reliability scoring and business analytics",
      "Comprehensive error handling with detailed validation messages and recovery guidance"
    ]

  - path: "app/api/v1/endpoints/aircraft/operators.py"
    purpose: "Public operator listing API with filtering and reliability statistics"
    lines: 79
    subdomain: "operator_management"
    endpoints: [
      "GET /api/v1/aircraft/operators/ - Public operator list with reliability filtering and category specialization",
      "GET /api/v1/aircraft/operators/{operator_id}/reliability - Public operator reliability statistics"
    ]
    features: [
      "Reliability-based filtering with minimum score thresholds and quality ranking",
      "Category specialization filtering with aircraft type and service focus",
      "Regional operation filtering with geographic coverage and market presence",
      "Public reliability statistics with performance indicators and quality metrics"
    ]

  # Data Enrichment & Web Scraping
  - path: "app/api/v1/endpoints/admin/web_scraper_v2.py"
    purpose: "Operator data enrichment API with automated web scraping and profile completion"
    lines: 112
    subdomain: "operator_management"
    endpoints: [
      "POST /api/v1/admin/web-scraper/operators - Automated operator data scraping with background processing"
    ]
    features: [
      "Background task scheduling with configurable limits and rate control",
      "Multi-source data aggregation with quality validation and conflict resolution",
      "Automated profile completion with intelligent field mapping and data normalization",
      "Source attribution tracking with enrichment history and quality metrics"
    ]

  # Core Operator Service
  - path: "app/services/operator_service.py"
    purpose: "Primary operator management service with comprehensive business logic and database integration"
    lines: 329
    subdomain: "operator_management"
    operations: [
      "get_operator - Retrieve operator by ID with performance metrics and relationship data",
      "get_operator_performance - Detailed performance statistics with reliability scoring",
      "list_operators - Operator listing with filtering, sorting, and reliability ranking",
      "create_operator - New operator creation with validation and relationship establishment",
      "update_operator - Operator profile updates with validation and audit tracking",
      "delete_operator - Operator deactivation with dependency management and cleanup",
      "get_operators_for_admin - Admin-specific operator listing with enhanced metrics",
      "get_operator_reliability - Reliability score calculation with fallback mechanisms"
    ]

  # Database Repository Layer
  - path: "app/db/manager/repositories/operator_repository.py"
    purpose: "Operator database operations with comprehensive CRUD, querying, and relationship management"
    lines: 364
    subdomain: "operator_management"
    operations: [
      "get_by_id - Retrieve operator by UUID with relationship loading and error handling",
      "get_operator_performance - Performance metrics retrieval with statistical calculation",
      "list_operators - Operator listing with filtering by reliability, category, and region",
      "get_operators_paginated - Paginated operator listing with search and status filtering",
      "create_operator - New operator creation with validation and field mapping",
      "update_operator - Operator updates with field validation and change tracking",
      "delete_operator - Operator deletion with cascade handling and cleanup"
    ]
    features: [
      "Comprehensive filtering with reliability thresholds, category specialization, and regional coverage",
      "Advanced search with partial name matching and email-based lookup capabilities",
      "Pagination support with configurable page sizes and total count calculation",
      "Transaction management with error handling and rollback capabilities"
    ]

  # Database Models
  - path: "app/db/models/operator.py"
    purpose: "Operator database models with comprehensive entity relationships and metadata management"
    lines: 278
    subdomain: "operator_management"
    models: [
      "Operator - Primary operator entity with comprehensive profile and relationship management",
      "OperatorContact - Multi-representative contact management with preferences and roles",
      "OperatorPricing - Operator-specific pricing rules and market adjustments",
      "OperatorResponsePattern - Response parsing patterns for automation and optimization"
    ]
    operator_fields: [
      "Core Identity: name, description, website, country, legal_name, slug",
      "Operational Details: operator_type, status, fleet_size, currency, accepts_bitcoin",
      "Contact Information: email, phone with validation and formatting",
      "Performance Metrics: reliability_score, conversion_rate with statistical tracking",
      "Enrichment Tracking: last_enriched, enrichment_source with quality attribution",
      "Metadata: operator_metadata JSONB with flexible schema support"
    ]
    relationships: [
      "aircraft - Fleet management with availability and utilization tracking",
      "quotes - Quote generation and pricing history with performance analysis",
      "bookings - Booking management with completion tracking and revenue analysis",
      "contacts - Multi-representative contact management with role-based access",
      "pricing - Operator-specific pricing rules with seasonal and market adjustments",
      "performance_stats - Comprehensive performance metrics with reliability scoring"
    ]

  # Database Schemas
  - path: "app/db/schemas/operator.py"
    purpose: "Operator API schemas with validation, serialization, and relationship handling"
    lines: 149
    subdomain: "operator_management"
    schemas: [
      "OperatorBase - Base operator schema with core validation and business rules",
      "OperatorCreate - Operator creation schema with required field validation",
      "OperatorUpdate - Operator update schema with optional fields and change tracking",
      "Operator - Complete operator schema with relationships and computed fields",
      "OperatorWithFleet - Operator schema including fleet relationship and utilization data",
      "OperatorInList - Simplified operator schema for list endpoints with performance optimization",
      "OperatorContactBase - Contact management schema with role and preference validation"
    ]

  # Data Enrichment Services
  - path: "app/services/optimized_data_enrichment_service.py"
    purpose: "Optimized operator data enrichment with GPT-powered analysis and validation"
    lines: 253
    subdomain: "operator_management"
    operations: [
      "_optimized_enrich_operator - GPT-powered operator enrichment with structured schema validation"
    ]
    enrichment_schema: [
      "Basic Information: name, legal_name, website, headquarters with validation",
      "Operational Data: founded, fleet_size, fleet_types with verification",
      "Geographic Coverage: base_airports, operational_regions with validation",
      "Certifications: certifications, safety_rating with compliance tracking",
      "Business Details: contact, services, description with quality scoring",
      "Confidence Scoring: confidence_score with accuracy tracking and validation"
    ]

  - path: "app/tasks/enrichment_tasks.py"
    purpose: "Scheduled operator data enrichment with automated updates and quality maintenance"
    lines: 46
    subdomain: "operator_management"
    operations: [
      "update_operator_data - Periodic operator data updates with quality validation and error handling"
    ]

behavior:
  # Operator Lifecycle Management
  operator_onboarding_workflow:
    - "Comprehensive operator registration with business information collection and validation"
    - "Automated data enrichment through web scraping, API integration, and intelligent profile completion"
    - "Fleet relationship establishment with aircraft assignment, availability tracking, and utilization monitoring"
    - "Contact management setup with multi-representative configuration, role assignment, and preference coordination"
    - "Performance baseline establishment with historical data integration and initial reliability scoring"
    - "Compliance verification with regulatory requirement validation, certification tracking, and audit documentation"

  operator_profile_management:
    - "Real-time profile updates with comprehensive change tracking, audit trail maintenance, and stakeholder notification"
    - "Fleet composition management with aircraft addition, removal, status updates, and availability synchronization"
    - "Contact relationship management with multiple representatives, role-based access, and communication preference coordination"
    - "Pricing rule configuration with seasonal adjustments, market factors, and competitive positioning optimization"
    - "Service capability definition with specialization tracking, operational constraint management, and quality assurance"
    - "Preference management for communication channels, booking procedures, and operational workflow optimization"

  # Advanced Search & Filtering
  operator_discovery_engine:
    - "Multi-criteria search with name-based lookup, email matching, and fuzzy search capabilities for comprehensive operator discovery"
    - "Reliability-based filtering with minimum score thresholds, quality ranking, and performance tier classification"
    - "Category specialization filtering with aircraft type focus, service specialization, and operational capability matching"
    - "Regional operation filtering with geographic coverage analysis, market presence evaluation, and operational reach assessment"
    - "Status-based filtering with active/inactive states, operational status tracking, and availability management"
    - "Performance-based ranking with reliability scoring, customer satisfaction metrics, and business value assessment"

  fleet_integration_management:
    - "Aircraft assignment coordination with operator capacity validation, availability tracking, and utilization optimization"
    - "Fleet composition tracking with aircraft type distribution, capability assessment, and operational constraint management"
    - "Availability synchronization with real-time status updates, booking system integration, and conflict resolution"
    - "Utilization monitoring with efficiency analysis, performance tracking, and optimization recommendation generation"
    - "Maintenance coordination with scheduling integration, availability impact assessment, and operational planning"
    - "Capacity planning with demand forecasting, fleet expansion analysis, and strategic growth support"

  # Data Quality & Enrichment
  automated_data_enrichment:
    - "Web scraping automation with multi-source data aggregation, quality validation, and conflict resolution"
    - "External API integration with data source verification, quality scoring, and accuracy validation"
    - "Profile completion intelligence with field mapping, data normalization, and consistency verification"
    - "Source attribution tracking with enrichment history, quality metrics, and reliability assessment"
    - "Quality assurance with validation rules, consistency checking, and automated correction mechanisms"
    - "Enrichment scheduling with periodic updates, change detection, and priority-based processing"

  contact_relationship_management:
    - "Multi-representative support with role assignment, access control, and responsibility distribution"
    - "Communication preference coordination with channel optimization, timing management, and delivery tracking"
    - "Role-based access management with permission control, responsibility assignment, and audit trail maintenance"
    - "Preference synchronization with notification settings, communication channels, and interaction tracking"
    - "Contact validation with information verification, accuracy checking, and update notification"
    - "Relationship tracking with interaction history, communication quality, and engagement scoring"

invariants:
  # Data Integrity & Validation
  - "Operator records must have unique identification with consistent naming conventions and duplicate prevention mechanisms"
  - "Fleet relationships must be validated with active aircraft associations, capacity verification, and availability synchronization"
  - "Contact information must be verified with format validation, role assignment, and communication preference setup"
  - "Profile updates must maintain consistency with existing relationships, dependency validation, and change tracking"
  - "Pricing rules must be mathematically consistent with non-negative adjustments, valid ranges, and market reasonableness"
  - "Performance metrics must be calculated using standardized algorithms with consistent weighting and statistical validation"

  # Business Logic Enforcement
  - "Operator creation must include required fields with business rule compliance and relationship establishment"
  - "Fleet assignments must validate aircraft availability with capacity constraints and operational compatibility"
  - "Contact management must enforce role uniqueness with primary contact designation and communication responsibility"
  - "Status changes must maintain operational consistency with booking impact assessment and stakeholder notification"
  - "Data enrichment must preserve manual overrides with quality attribution and source prioritization"
  - "Compliance verification must track regulatory requirements with certification validation and audit documentation"

  # System Reliability
  - "Database operations must maintain transaction integrity with proper rollback capabilities and error recovery"
  - "API responses must include proper error handling with detailed validation messages and recovery guidance"
  - "Search operations must handle edge cases with graceful degradation and fallback mechanisms"
  - "Pagination must maintain consistency with accurate total counts and stable ordering"
  - "Background tasks must include progress tracking with error handling and retry mechanisms"
  - "Data validation must prevent invalid states with comprehensive checking and correction procedures"

forbidden_states:
  # Data Consistency Violations
  - "Operators without valid contact information or primary contact designation creating communication gaps"
  - "Fleet relationships without aircraft availability validation causing booking conflicts and operational issues"
  - "Profile updates without change tracking creating audit trail gaps and accountability issues"
  - "Pricing rules with negative adjustments or mathematically impossible configurations causing system errors"

  # Business Logic Violations
  - "Operator creation without required field validation allowing incomplete profiles and operational issues"
  - "Contact management without role validation creating responsibility conflicts and communication failures"
  - "Status changes without impact assessment causing booking disruptions and operational conflicts"
  - "Data enrichment without source attribution creating quality issues and reliability concerns"

  # System Integrity Issues
  - "Database operations without transaction management risking data corruption and consistency failures"
  - "API operations without proper error handling exposing system vulnerabilities and user confusion"
  - "Search operations without pagination limits causing performance degradation and system overload"
  - "Background tasks without progress tracking creating monitoring gaps and failure detection issues"

depends_on:
  - aircraft: "Fleet management requires aircraft entity relationships, availability data, and utilization tracking"
  - authentication: "Admin access control for operator management, configuration, and sensitive operations"
  - database_manager: "Transactional database operations, consistency management, and relationship integrity"
  - web_scraping: "Automated data enrichment through external source integration and profile completion"
  - communication: "Email services for notification delivery, stakeholder coordination, and relationship management"
  - validation: "Data validation frameworks for profile integrity, business rule enforcement, and quality assurance"
  - audit: "Change tracking systems for compliance documentation, accountability, and regulatory requirements"
  - performance_analytics: "Reliability scoring integration for operator evaluation and selection optimization"

provides:
  - operator_directory: "Comprehensive operator database with profile management, fleet coordination, and performance tracking"
  - operator_onboarding: "Complete operator registration workflow with validation, enrichment, and relationship establishment"
  - fleet_management: "Aircraft-operator relationship coordination with availability tracking and utilization optimization"
  - contact_management: "Multi-representative contact coordination with role-based access and communication preferences"
  - data_enrichment: "Automated profile completion through web scraping, API integration, and quality validation"
  - compliance_tracking: "Regulatory requirement validation with certification management and audit documentation"
  - search_capabilities: "Advanced operator discovery with filtering, ranking, and performance-based selection"
  - profile_administration: "Complete operator profile management with change tracking and stakeholder coordination"

enforcement_hooks:
  # Data Validation
  pre_operator_create:
    - "Validate operator uniqueness across name, email, and identification fields with duplicate detection"
    - "Verify required fields completeness with business rule compliance and validation framework integration"
    - "Check fleet relationship validity with aircraft availability verification and capacity constraint validation"
    - "Validate contact information format with communication preference setup and role assignment verification"

  pre_operator_update:
    - "Ensure update consistency with existing relationships, dependency validation, and change impact assessment"
    - "Validate pricing rule changes with mathematical consistency, market reasonableness, and competitive analysis"
    - "Check performance metric updates with historical data consistency and statistical validation"
    - "Verify contact changes with proper notification delivery, preference migration, and role reassignment"

  post_operator_creation:
    - "Initialize performance baseline with historical data integration and reliability scoring establishment"
    - "Setup fleet relationships with aircraft assignment coordination and availability synchronization"
    - "Configure communication preferences with channel optimization and notification setup"
    - "Trigger data enrichment with web scraping initiation and profile completion scheduling"

  # Fleet Management
  pre_fleet_assignment:
    - "Validate aircraft availability with capacity constraints and operational compatibility verification"
    - "Check operator capacity with fleet size limits and operational constraint assessment"
    - "Verify aircraft-operator compatibility with service specialization and geographic coverage matching"
    - "Ensure availability synchronization with booking system integration and conflict prevention"

  post_fleet_update:
    - "Update availability status with real-time synchronization and booking system notification"
    - "Recalculate capacity metrics with utilization analysis and optimization recommendation generation"
    - "Notify dependent systems with fleet change propagation and stakeholder coordination"
    - "Trigger performance recalculation with reliability scoring updates and analytics refresh"

  # Contact Management
  pre_contact_update:
    - "Validate contact role uniqueness with primary designation verification and responsibility assignment"
    - "Check communication preference consistency with channel validation and delivery capability verification"
    - "Verify contact information format with validation rules and accessibility requirements"
    - "Ensure notification setup with preference coordination and delivery channel configuration"

  post_contact_change:
    - "Update communication routing with preference synchronization and channel optimization"
    - "Notify stakeholders with contact change propagation and relationship coordination"
    - "Audit trail maintenance with change documentation and accountability tracking"
    - "Preference migration with historical data preservation and continuity assurance"

  # Scheduled Maintenance
  daily_data_validation:
    - "Validate operator profile consistency with relationship integrity checking and quality assurance"
    - "Update performance metrics with latest data integration and reliability scoring refresh"
    - "Clean up orphaned relationships with dependency management and consistency restoration"
    - "Generate data quality reports with validation metrics and improvement recommendations"

  weekly_enrichment_processing:
    - "Execute scheduled data enrichment with web scraping automation and profile completion"
    - "Process enrichment results with quality validation and conflict resolution"
    - "Update enrichment metrics with source attribution and quality scoring"
    - "Generate enrichment reports with coverage analysis and improvement identification"

primary_flows:
  operator_onboarding_flow:
    steps:
      - "Registration: Collect comprehensive operator information with business details and validation"
      - "Validation: Verify required fields with business rule compliance and uniqueness checking"
      - "Enrichment: Automated data completion through web scraping and external API integration"
      - "Fleet Setup: Aircraft relationship establishment with availability tracking and capacity validation"
      - "Contact Configuration: Multi-representative setup with role assignment and preference coordination"
      - "Compliance Verification: Regulatory requirement validation with certification tracking and documentation"
      - "Performance Baseline: Historical data integration with initial reliability scoring and analytics setup"
      - "Activation: Operator activation with system integration and stakeholder notification"
    
    automation:
      - "Automated data enrichment with web scraping, API integration, and intelligent profile completion"
      - "Real-time validation with business rule enforcement and consistency checking"
      - "Fleet synchronization with aircraft availability tracking and utilization monitoring"

  operator_profile_management_flow:
    steps:
      - "Profile Access: Secure operator profile retrieval with authentication and authorization verification"
      - "Change Validation: Update validation with business rule compliance and consistency checking"
      - "Relationship Management: Fleet and contact relationship coordination with dependency validation"
      - "Data Enrichment: Automated profile enhancement with quality validation and source attribution"
      - "Audit Trail: Change tracking with documentation and stakeholder notification"
      - "Synchronization: System-wide updates with dependent service coordination and consistency maintenance"
    
    intelligence:
      - "Intelligent change detection with impact assessment and optimization recommendations"
      - "Automated quality assurance with validation rules and consistency verification"
      - "Performance impact analysis with reliability scoring updates and analytics refresh"

  fleet_integration_flow:
    steps:
      - "Fleet Discovery: Aircraft identification with operator compatibility assessment and capacity validation"
      - "Assignment Validation: Aircraft-operator relationship verification with availability checking and constraint validation"
      - "Availability Synchronization: Real-time status coordination with booking system integration and conflict resolution"
      - "Utilization Tracking: Fleet efficiency monitoring with performance analysis and optimization recommendations"
      - "Maintenance Coordination: Scheduling integration with availability impact assessment and operational planning"
      - "Capacity Optimization: Fleet composition analysis with demand forecasting and strategic planning support"
    
    optimization:
      - "Dynamic fleet allocation with demand-based assignment and utilization optimization"
      - "Availability prediction with maintenance scheduling and operational constraint integration"
      - "Performance monitoring with efficiency analysis and improvement recommendation generation"

  operator_search_flow:
    steps:
      - "Query Processing: Search parameter validation with filter application and criteria optimization"
      - "Database Query: Optimized operator retrieval with relationship loading and performance data integration"
      - "Filtering Application: Multi-criteria filtering with reliability thresholds and specialization matching"
      - "Performance Ranking: Reliability-based sorting with quality scoring and competitive positioning"
      - "Result Formatting: Response preparation with pagination and metadata inclusion"
      - "Analytics Tracking: Search pattern analysis with optimization insights and user behavior tracking"
    
    optimization:
      - "Query optimization with index utilization and performance monitoring"
      - "Caching strategies with result optimization and response time improvement"
      - "Ranking algorithms with relevance scoring and quality assessment"

scheduler_integration:
  operator_maintenance:
    - task: "Daily Profile Validation"
      schedule: "daily 1:00 AM"
      purpose: "Validate operator profiles with relationship integrity checking and quality assurance"
      handler: "operator_service.py:validate_daily_profiles"
      validation: "Comprehensive profile consistency checking with error detection and correction"

    - task: "Weekly Data Enrichment"
      schedule: "weekly Sunday 2:00 AM"
      purpose: "Automated operator data enrichment with web scraping and profile completion"
      handler: "enrichment_tasks.py:update_operator_data"
      enrichment: "Multi-source data aggregation with quality validation and conflict resolution"

    - task: "Monthly Compliance Review"
      schedule: "monthly 1st 3:00 AM"
      purpose: "Comprehensive compliance verification with regulatory requirement validation"
      handler: "operator_service.py:review_monthly_compliance"
      compliance: "Regulatory requirement tracking with certification validation and audit documentation"

  fleet_synchronization:
    - task: "Hourly Fleet Status"
      schedule: "hourly :15"
      purpose: "Fleet availability synchronization with real-time status updates"
      handler: "operator_service.py:sync_hourly_fleet_status"
      synchronization: "Real-time fleet coordination with availability tracking and conflict resolution"

    - task: "Daily Utilization Analysis"
      schedule: "daily 6:00 AM"
      purpose: "Fleet utilization analysis with efficiency monitoring and optimization recommendations"
      handler: "operator_service.py:analyze_daily_utilization"
      analytics: "Fleet efficiency tracking with performance analysis and improvement identification"

endpoints:
  admin_management:
    - path: "/api/v1/admin/operators"
      methods: ["GET", "POST"]
      description: "Admin operator management with comprehensive CRUD operations and advanced filtering"
      response_time: "<1000ms"
      handler: "admin_ui.py:operator_management_endpoints"
      access: "Admin-only access with role-based authentication and audit logging"

    - path: "/api/v1/admin/operators/{operator_id}"
      methods: ["GET", "PUT", "DELETE"]
      description: "Individual operator management with detailed operations and relationship coordination"
      response_time: "<1500ms"
      handler: "admin_ui.py:operator_detail_operations"
      access: "Admin-only access with operator-specific permissions and change tracking"

  public_discovery:
    - path: "/api/v1/aircraft/operators"
      methods: ["GET"]
      description: "Public operator listing with filtering, ranking, and reliability statistics"
      response_time: "<500ms"
      handler: "operators.py:list_operators"
      access: "Public access with rate limiting and performance optimization"

    - path: "/api/v1/aircraft/operators/{operator_id}/reliability"
      methods: ["GET"]
      description: "Public operator reliability statistics with performance indicators"
      response_time: "<800ms"
      handler: "operators.py:get_operator_reliability"
      access: "Public access with filtered performance data and privacy protection"

  data_enrichment:
    - path: "/api/v1/admin/web-scraper/operators"
      methods: ["POST"]
      description: "Automated operator data enrichment with background processing and quality validation"
      response_time: "<2000ms"
      handler: "web_scraper_v2.py:scrape_operators"
      access: "Admin-only access with background task scheduling and progress tracking"

error_handling:
  operator_management_errors:
    - "OperatorNotFoundError: Operator ID not found in database with recovery suggestions and alternative options"
    - "OperatorValidationError: Operator data validation failure with field-specific error details and correction guidance"
    - "FleetRelationshipError: Aircraft-operator relationship inconsistency with resolution options and dependency management"
    - "ContactManagementError: Contact information validation failure with format guidance and role assignment support"
    - "DuplicateOperatorError: Operator uniqueness violation with merge options and resolution procedures"
    - "EnrichmentError: Data enrichment failure with source attribution and quality validation issues"

  data_integrity_errors:
    - "ProfileInconsistencyError: Operator profile consistency violation with validation requirements and correction procedures"
    - "RelationshipConstraintError: Operator relationship constraint violation with dependency resolution and repair guidance"
    - "PricingRuleError: Pricing configuration inconsistency with validation requirements and mathematical correction"
    - "ComplianceValidationError: Regulatory requirement validation failure with certification tracking and documentation needs"

testing:
  operator_management_testing:
    - test_type: "Unit Tests"
      coverage: "Operator service methods, validation logic, and business rule enforcement"
      location: "tests/unit/services/test_operator_service.py"
      key_tests: [
        "test_get_operator - Operator retrieval with performance metrics integration",
        "test_list_operators - Operator listing with filtering and reliability ranking",
        "test_create_operator - Operator creation with validation and relationship establishment",
        "test_update_operator - Operator updates with change tracking and audit logging",
        "test_delete_operator - Operator deactivation with dependency management",
        "test_get_operators_for_admin - Admin-specific operator listing with enhanced metrics"
      ]

    - test_type: "Integration Tests"
      coverage: "Complete operator workflows from creation through fleet management"
      location: "tests/integration/services/test_operator_management_integration.py"

    - test_type: "Repository Tests"
      coverage: "Database operations, relationship management, and transaction handling"
      location: "tests/unit/repositories/test_operator_repository.py"

    - test_type: "API Tests"
      coverage: "Operator API endpoints, authentication, and response validation"
      location: "tests/integration/endpoints/test_operator_endpoints.py"

security:
  operator_management_security:
    - "Operator data access controlled by role-based authentication with admin-only management functions and audit logging"
    - "Profile updates restricted to authorized personnel with change tracking and approval workflows"
    - "Fleet management operations secured with operator-specific permissions and relationship validation"
    - "Contact information protected with privacy controls and access restriction based on roles"
    - "Data enrichment processes validated with source authentication and quality assurance checks"
    - "API endpoints protected with rate limiting, authentication verification, and abuse prevention mechanisms"

performance:
  operator_management_performance_targets:
    - "Operator listing queries: <500ms response time with pagination, filtering, and performance data integration"
    - "Operator profile retrieval: <800ms per operator with comprehensive relationship loading and metrics calculation"
    - "Operator creation processing: <1500ms per operator with validation, enrichment, and relationship establishment"
    - "Fleet synchronization updates: <1000ms per operation with availability tracking and conflict resolution"
    - "Search and filtering operations: <600ms with multi-criteria filtering and reliability-based ranking"
    - "Data enrichment processing: <5000ms per operator with web scraping, validation, and profile completion"

consolidation_notes:
  operator_management_domain_ownership:
    - "Complete operator lifecycle management consolidated in OperatorService with database manager integration and comprehensive business logic"
    - "Fleet relationship coordination centralized with aircraft availability tracking and utilization optimization"
    - "Contact management unified with multi-representative support and communication preference coordination"
    - "Data enrichment automation standardized with web scraping integration and quality validation frameworks"
    - "Compliance verification integrated with regulatory requirement tracking and certification management"
    - "Search and discovery capabilities optimized with reliability-based ranking and performance-driven selection"
    - "Profile administration streamlined with change tracking, audit trails, and stakeholder notification systems"
    - "API endpoints secured with comprehensive authentication, role-based access control, and audit logging"

implementation_status:
  current_implementation:
    - "✅ Comprehensive CRUD operations with validation and error handling"
    - "✅ Advanced search and filtering with reliability-based ranking"
    - "✅ Fleet relationship management with availability tracking"
    - "✅ Contact management with multi-representative support"
    - "✅ Data enrichment automation with web scraping integration"
    - "✅ Admin API endpoints with pagination and performance metrics"
    - "✅ Database repository layer with transaction management"
    - "✅ Profile validation with business rule enforcement"

  implementation_gaps:
    - "⚠️ Real-time fleet synchronization with automated conflict resolution"
    - "⚠️ Advanced compliance verification with regulatory requirement automation"
    - "⚠️ Intelligent data enrichment with machine learning-powered quality validation"
    - "⚠️ Automated relationship optimization with performance-based recommendations"
    - "⚠️ Advanced audit trail system with comprehensive change tracking"
    - "⚠️ Predictive analytics for operator performance and relationship management"

restoration_methods:
  operator_management_recovery:
    - "Profile restoration from audit logs with change history replay and consistency verification"
    - "Fleet relationship reconstruction with aircraft availability synchronization and dependency resolution"
    - "Contact information recovery with preference restoration and communication channel validation"
    - "Data enrichment replay with source attribution and quality validation restoration"
    - "Performance baseline restoration with historical data integration and reliability scoring recalculation"
    - "Compliance status reconstruction with certification tracking and regulatory requirement validation"

advanced_recovery:
  relationship_recovery:
    - "Aircraft-operator relationship restoration with availability synchronization and capacity validation"
    - "Contact relationship reconstruction with role assignment and communication preference restoration"
    - "Performance metric recalculation with historical data integration and statistical validation"
    - "Fleet utilization restoration with efficiency analysis and optimization recommendation regeneration"
    - "Compliance verification restoration with regulatory requirement tracking and certification validation"
    - "Search index reconstruction with operator discovery optimization and ranking algorithm restoration" 