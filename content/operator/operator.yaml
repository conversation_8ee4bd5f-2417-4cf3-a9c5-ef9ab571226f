system: operator
description: "Villiers.ai Operator Domain - Comprehensive Charter Operator Management System encompassing operator onboarding, fleet management, performance tracking, reliability scoring, pricing optimization, communication coordination, and business relationship management for private jet charter operations"

intent_assertions:
- "Complete operator lifecycle management from onboarding through performance optimization with comprehensive fleet and relationship tracking"
- "Advanced operator reliability scoring with multi-metric performance tracking, predictive analytics, and automated quality assurance"
- "Sophisticated pricing rule management with dynamic adjustments, seasonal factors, and operator-specific optimization strategies"
- "Comprehensive communication coordination with automated solicitation campaigns, response tracking, and preference management"
- "Fleet management integration with aircraft assignment, availability synchronization, and operational constraint tracking"
- "Performance analytics and business intelligence for operator selection, relationship management, and network optimization"
- "Automated empty leg solicitation workflows with operator outreach, response tracking, and inventory management"
- "Contact management system with multi-representative support, communication preferences, and relationship tracking"
- "Pricing transparency and optimization with market analysis, competitive benchmarking, and revenue optimization"
- "Zero operator relationship failures through proactive communication, performance monitoring, and automated issue resolution"

technical_assertions:
  # Core Operator Management
  - path: "app/api/v1/endpoints/aircraft/operators.py"
    purpose: "Primary operator API endpoints for listing, filtering, and basic operations"
    lines: 79
    subdomain: "operator_management"
    endpoints: [
      "GET /api/v1/aircraft/operators/ - List operators with filtering by reliability, category, region"
    ]
    
  - path: "app/api/v1/endpoints/admin/admin_ui.py"
    purpose: "Admin operator management endpoints with comprehensive CRUD operations"
    lines: 1450
    subdomain: "operator_management"
    endpoints: [
      "GET /api/v1/admin/operators - Paginated operator list with search and status filtering",
      "POST /api/v1/admin/operators - Create new operator with validation",
      "GET /api/v1/admin/operators/{operator_id} - Get detailed operator information",
      "PUT /api/v1/admin/operators/{operator_id} - Update operator details and configuration",
      "DELETE /api/v1/admin/operators/{operator_id} - Deactivate or remove operator"
    ]

  # Operator Reliability & Performance
  - path: "app/api/v1/endpoints/admin/reliability_v2.py"
    purpose: "Operator reliability tracking and performance metrics API"
    lines: 35
    subdomain: "reliability"
    endpoints: [
      "GET /api/v1/admin/reliability/{operator_id} - Get detailed reliability metrics for operator"
    ]

  # Empty Leg Solicitation
  - path: "app/api/v1/endpoints/bookings/empty_legs.py"
    purpose: "Operator solicitation and response tracking for empty leg inventory management"
    lines: 1314
    subdomain: "communication"
    endpoints: [
      "POST /api/v1/empty-legs/solicitation/start - Start weekly operator solicitation campaign",
      "POST /api/v1/empty-legs/solicitation/manual - Manually trigger solicitation process",
      "GET /api/v1/empty-legs/solicitation/operators - Get operators eligible for solicitation",
      "POST /api/v1/empty-legs/solicitation/response/{operator_id} - Track operator response",
      "POST /api/v1/empty-legs/solicitation/opt-out/{operator_id} - Opt operator out of solicitations",
      "POST /api/v1/empty-legs/solicitation/opt-in/{operator_id} - Opt operator back into solicitations"
    ]

  # Web Scraping & Data Enrichment
  - path: "app/api/v1/endpoints/admin/web_scraper_v2.py"
    purpose: "Operator data scraping and enrichment API endpoints"
    lines: 39
    subdomain: "operator_management"
    endpoints: [
      "POST /api/v1/admin/web-scraper/operators - Scrape operator data from external sources"
    ]

  # Core Operator Services
  - path: "app/services/operator_service.py"
    purpose: "Primary operator management service with database manager integration"
    lines: 165
    subdomain: "operator_management"
    operations: [
      "get_operator - Retrieve operator by ID with performance metrics",
      "get_operator_performance - Get detailed performance statistics",
      "list_operators - List operators with filtering and reliability scoring",
      "create_operator - Create new operator with validation",
      "update_operator - Update operator details and configuration",
      "get_operators_for_admin - Admin-specific operator listing with pagination"
    ]

  - path: "app/services/operator_reliability_service.py"
    purpose: "Operator reliability tracking and predictive analytics service"
    lines: 273
    subdomain: "reliability"
    operations: [
      "calculate_operator_reliability - Comprehensive reliability metrics calculation",
      "predict_operator_performance - Predictive performance analytics",
      "get_operator_reliability_trends - Historical reliability trend analysis",
      "rank_operators_by_reliability - Operator ranking and comparison",
      "get_reliability_benchmark - Industry benchmarking and comparison"
    ]

  - path: "app/services/operator_communication_service.py"
    purpose: "Operator communication coordination and preference management"
    lines: 32
    subdomain: "communication"
    operations: [
      "send_operator_notification - Send notifications to operators",
      "track_communication_preferences - Manage operator communication preferences",
      "coordinate_multi_operator_outreach - Coordinate mass communication campaigns"
    ]

  - path: "app/services/empty_leg_solicitation_service.py"
    purpose: "Automated operator solicitation for empty leg inventory management"
    lines: 100
    subdomain: "communication"
    operations: [
      "start_weekly_solicitation - Launch weekly solicitation campaigns",
      "track_empty_leg_response - Track operator responses and submission",
      "get_operators_to_notify - Get eligible operators for solicitation",
      "opt_out_operator - Manage operator opt-out preferences",
      "opt_in_operator - Re-enable operator solicitation participation"
    ]

  # Legacy Services
  - path: "app/services/legacy_services/operators.py"
    purpose: "Legacy operator service (deprecated)"
    lines: 12
    subdomain: "operator_management"
    status: "deprecated"

  - path: "app/services/legacy_services/operator_communication.py"
    purpose: "Legacy operator communication service with advanced features"
    lines: 162
    subdomain: "communication"
    operations: [
      "send_quote_request - Send quote requests to operators",
      "track_response_patterns - Analyze operator response patterns",
      "manage_operator_relationships - Maintain operator relationship scoring"
    ]

  - path: "app/services/legacy_services/operator_reliability.py"
    purpose: "Legacy operator reliability tracking with comprehensive metrics"
    lines: 261
    subdomain: "reliability"
    operations: [
      "calculate_operator_reliability - Multi-metric reliability calculation",
      "predict_operator_performance - Performance prediction algorithms",
      "analyze_response_patterns - Response pattern analysis and learning"
    ]

  # Database Repositories
  - path: "app/db/manager/repositories/operator_repository.py"
    purpose: "Operator database operations with comprehensive CRUD and query capabilities"
    lines: 364
    subdomain: "operator_management"
    operations: [
      "get_by_id - Retrieve operator by UUID with relationship loading",
      "get_operator_performance - Get performance metrics from database",
      "list_operators - List operators with filtering by reliability, category, region",
      "get_operators_paginated - Paginated operator listing with search",
      "create_operator - Create new operator with validation",
      "update_operator - Update operator with field validation",
      "delete_operator - Soft delete operator with cascade handling"
    ]

  - path: "app/db/manager/repositories/operator_reliability_repository.py"
    purpose: "Operator reliability data access layer with metrics calculation"
    lines: 76
    subdomain: "reliability"
    operations: [
      "get_operator_by_id - Retrieve operator for reliability calculations",
      "calculate_reliability_metrics - Database-driven reliability calculations",
      "get_performance_history - Historical performance data retrieval"
    ]

  # Database Models
  - path: "app/db/models/operator.py"
    purpose: "Operator database models with comprehensive relationship mapping"
    lines: 278
    subdomain: "operator_management"
    models: [
      "Operator - Primary operator entity with fleet and performance relationships",
      "OperatorContact - Operator contact management with preferences",
      "OperatorPricing - Operator-specific pricing rules and adjustments",
      "OperatorResponsePattern - Response parsing patterns for automation"
    ]

  - path: "app/db/models/adaptive.py"
    purpose: "Operator performance statistics model for analytics"
    lines: 179
    subdomain: "reliability"
    models: [
      "OperatorPerformanceStats - Comprehensive performance metrics tracking"
    ]

  # Database Schemas
  - path: "app/db/schemas/operator.py"
    purpose: "Operator API schemas with relationship handling and validation"
    lines: 149
    subdomain: "operator_management"
    schemas: [
      "OperatorBase - Base operator schema with core fields",
      "OperatorCreate - Operator creation schema with validation",
      "OperatorUpdate - Operator update schema with optional fields",
      "Operator - Complete operator schema with relationships",
      "OperatorWithFleet - Operator schema including fleet relationship",
      "OperatorInList - Simplified operator schema for list endpoints",
      "OperatorReliabilityStats - Reliability statistics schema"
    ]

  - path: "app/db/schemas/operator_reliability.py"
    purpose: "Operator reliability and performance prediction schemas"
    lines: 190
    subdomain: "reliability"
    schemas: [
      "OperatorReliabilityBase - Base reliability metrics schema",
      "OperatorReliabilityCreate - Create reliability tracking record",
      "OperatorReliability - Complete reliability schema with calculations",
      "OperatorPerformancePrediction - Performance prediction schema",
      "OperatorReliabilityTrend - Historical trend analysis schema"
    ]

subdomains:
  operator_management:
    description: "Core operator lifecycle management and business relationship coordination"
    purpose: "Complete operator onboarding, profile management, fleet coordination, and business relationship optimization"
    system_definition: "villiers_system_definitions/operator/operator_management/operator_management.yaml"
    api_files:
      - "app/api/v1/endpoints/aircraft/operators.py"
      - "app/api/v1/endpoints/admin/admin_ui.py"
      - "app/api/v1/endpoints/admin/web_scraper_v2.py"
    service_files:
      - "app/services/operator_service.py"
      - "app/services/web_scraper_service.py"
    key_features:
      - "Comprehensive operator profile management with fleet integration"
      - "Advanced search and filtering with reliability-based ranking"
      - "Automated data enrichment through web scraping and API integration"
      - "Multi-contact management with role-based access and preferences"
      - "Fleet relationship management with aircraft assignment tracking"
      - "Operator status and lifecycle management with audit trails"

  reliability:
    description: "Operator performance tracking, reliability scoring, and predictive analytics"
    purpose: "Comprehensive operator performance monitoring with predictive insights for optimal operator selection"
    system_definition: "villiers_system_definitions/operator/reliability/reliability.yaml"
    api_files:
      - "app/api/v1/endpoints/admin/reliability_v2.py"
    service_files:
      - "app/services/operator_reliability_service.py"
      - "app/services/legacy_services/operator_reliability.py"
    key_features:
      - "Multi-metric reliability scoring with weighted calculations"
      - "Predictive performance analytics for operator selection optimization"
      - "Historical trend analysis with seasonal and market factor integration"
      - "Operator ranking and benchmarking against industry standards"
      - "Real-time performance monitoring with automated alerting"
      - "Machine learning-powered response pattern analysis"

  communication:
    description: "Operator communication coordination, solicitation campaigns, and relationship management"
    purpose: "Automated operator outreach, response tracking, and communication preference management for optimal business relationships"
    system_definition: "villiers_system_definitions/operator/communication/communication.yaml"
    api_files:
      - "app/api/v1/endpoints/bookings/empty_legs.py"
    service_files:
      - "app/services/operator_communication_service.py"
      - "app/services/empty_leg_solicitation_service.py"
      - "app/services/legacy_services/operator_communication.py"
    key_features:
      - "Automated weekly empty leg solicitation campaigns"
      - "Operator response tracking with performance impact analysis"
      - "Communication preference management with opt-in/opt-out support"
      - "Multi-channel operator outreach with delivery tracking"
      - "Relationship scoring with communication quality metrics"
      - "Automated follow-up and reminder systems"

behavior:
  # Operator Lifecycle Management
  operator_onboarding_workflow:
    - "Comprehensive operator profile creation with fleet and contact information"
    - "Automated data enrichment through web scraping and external API integration"
    - "Fleet relationship establishment with aircraft assignment and availability tracking"
    - "Contact management setup with role-based access and communication preferences"
    - "Initial performance baseline establishment with historical data integration"
    - "Compliance verification and regulatory requirement validation"

  operator_profile_management:
    - "Real-time operator profile updates with change tracking and audit trails"
    - "Fleet composition management with aircraft addition, removal, and status updates"
    - "Contact relationship management with multiple representatives and role assignments"
    - "Pricing rule configuration with seasonal adjustments and market factors"
    - "Service capability definition with specialization and operational constraint tracking"
    - "Preference management for communication, booking, and operational procedures"

  # Performance Tracking & Analytics
  reliability_calculation_engine:
    - "Multi-metric reliability scoring with weighted performance calculations"
    - "Response time tracking with statistical analysis and trend identification"
    - "Quote accuracy measurement with pricing variance analysis"
    - "On-time performance monitoring with flight punctuality scoring"
    - "Cancellation rate tracking with reason analysis and impact assessment"
    - "Customer satisfaction integration with feedback correlation and improvement tracking"

  predictive_analytics_workflow:
    - "Historical performance trend analysis with seasonal and market factor integration"
    - "Machine learning-powered performance prediction with confidence scoring"
    - "Operator selection optimization based on route, aircraft, and timing factors"
    - "Market benchmarking with industry standard comparison and competitive analysis"
    - "Risk assessment with cancellation probability and reliability confidence intervals"
    - "Performance improvement recommendations with actionable insights and tracking"

  # Communication & Relationship Management
  automated_solicitation_campaigns:
    - "Weekly empty leg solicitation with personalized operator messaging"
    - "Automated email delivery with tracking and open rate monitoring"
    - "Response collection and processing with structured data validation"
    - "Follow-up campaign management with automated reminder systems"
    - "Opt-out preference management with compliance and relationship preservation"
    - "Campaign performance analytics with response rate optimization"

  relationship_optimization:
    - "Communication preference tracking with channel optimization and timing analysis"
    - "Response pattern analysis with machine learning-powered insights"
    - "Relationship scoring with interaction quality and business value assessment"
    - "Automated issue detection and resolution with proactive relationship management"
    - "Performance feedback delivery with constructive improvement recommendations"
    - "Partnership opportunity identification with strategic relationship development"

  # Pricing & Fleet Management
  dynamic_pricing_optimization:
    - "Operator-specific pricing rule management with market factor integration"
    - "Seasonal adjustment calculation with demand forecasting and capacity optimization"
    - "Route-specific pricing factors with competitive analysis and market positioning"
    - "Volume discount rule management with loyalty program integration"
    - "Real-time pricing adjustment with market condition monitoring"
    - "Commission optimization with profitability analysis and operator incentive alignment"

  fleet_coordination:
    - "Aircraft assignment optimization with operator capability and availability matching"
    - "Fleet utilization tracking with efficiency analysis and optimization recommendations"
    - "Maintenance coordination with scheduling and availability impact management"
    - "Empty leg opportunity identification with aircraft positioning and route optimization"
    - "Capacity planning support with demand forecasting and fleet expansion analysis"
    - "Operational constraint management with regulatory compliance and safety oversight"

invariants:
  # Data Integrity
  - "Operator records must have unique identification with consistent naming conventions"
  - "Fleet relationships must be validated with active aircraft and operator associations"
  - "Contact information must be verified with role-based access and communication preferences"
  - "Performance metrics must be calculated using standardized algorithms with consistent weighting"
  - "Reliability scores must be bounded between 0.0 and 1.0 with proper statistical validation"
  - "Pricing rules must be mathematically consistent with non-negative adjustments and valid ranges"

  # Communication Compliance
  - "Operator solicitation must respect opt-out preferences with immediate effect"
  - "Email campaigns must comply with anti-spam regulations with proper unsubscribe mechanisms"
  - "Communication tracking must maintain audit trails with privacy protection"
  - "Response data must be validated for completeness with structured format requirements"
  - "Preference changes must be timestamped with proper version control"

  # Performance Tracking
  - "Reliability calculations must use minimum sample sizes for statistical significance"
  - "Performance metrics must be updated within 24 hours of new data availability"
  - "Trend analysis must account for seasonal variations with appropriate normalization"
  - "Benchmarking must use industry-standard metrics with comparable operator segments"
  - "Predictive models must be validated against historical performance with accuracy tracking"

forbidden_states:
  # Data Consistency
  - "Operators without valid contact information or fleet relationships"
  - "Performance metrics calculated without sufficient historical data"
  - "Reliability scores outside valid ranges or with inconsistent calculation methods"
  - "Pricing rules with negative adjustments or mathematically impossible configurations"

  # Communication Violations
  - "Solicitation emails sent to operators who have opted out"
  - "Communication without proper tracking and audit trail maintenance"
  - "Response data processing without validation and structured format verification"
  - "Preference management without proper versioning and change tracking"

  # System Limits
  - "Performance calculations without proper error handling and fallback mechanisms"
  - "Bulk operations without transaction management and rollback capabilities"
  - "External API integration without rate limiting and error recovery"
  - "Data enrichment without source attribution and quality validation"

depends_on:
  - aircraft: "Fleet management requires aircraft entity relationships and availability data"
  - booking: "Performance tracking depends on booking completion and feedback data"
  - communication: "Email services for solicitation campaigns and notification delivery"
  - authentication: "Admin access control for operator management and configuration"
  - pricing: "Dynamic pricing calculations and market factor integration"
  - analytics: "Performance metrics calculation and business intelligence reporting"
  - external_web_scraping: "Data enrichment through automated web scraping services"
  - external_email_providers: "Email delivery for solicitation campaigns and notifications"

provides:
  - operator_directory: "Comprehensive operator database with fleet and performance information"
  - reliability_scoring: "Standardized operator reliability metrics with predictive analytics"
  - fleet_management: "Aircraft-operator relationship management with availability coordination"
  - pricing_optimization: "Operator-specific pricing rules with dynamic market adjustments"
  - communication_coordination: "Automated operator outreach with response tracking and preference management"
  - performance_analytics: "Business intelligence for operator selection and relationship optimization"
  - solicitation_automation: "Empty leg inventory management through automated operator campaigns"
  - relationship_management: "Comprehensive operator relationship tracking with optimization insights"

enforcement_hooks:
  # Data Validation
  pre_operator_create:
    - "Validate operator uniqueness across name, email, and identification fields"
    - "Verify required fields completeness with business rule compliance"
    - "Check fleet relationship validity with aircraft availability and operator capacity"
    - "Validate contact information format with communication preference setup"

  pre_operator_update:
    - "Ensure update consistency with existing relationships and dependencies"
    - "Validate pricing rule changes with mathematical consistency and market reasonableness"
    - "Check performance metric updates with historical data consistency"
    - "Verify contact changes with proper notification and preference migration"

  pre_reliability_calculation:
    - "Validate minimum data requirements for statistical significance"
    - "Check calculation methodology consistency with historical scoring"
    - "Verify data sources and quality for metric calculation inputs"
    - "Ensure proper weighting and normalization for comparative analysis"

  # Communication Compliance
  pre_solicitation_send:
    - "Verify operator opt-in status with current preference validation"
    - "Check rate limiting compliance with email provider restrictions"
    - "Validate message content with regulatory compliance and brand standards"
    - "Ensure tracking setup with proper attribution and measurement"

  post_solicitation_delivery:
    - "Log delivery status with success rate and error tracking"
    - "Update operator engagement metrics with response tracking"
    - "Monitor opt-out requests with immediate preference updating"
    - "Track response quality with structured data validation"

  # Performance Monitoring
  post_performance_update:
    - "Validate performance metric consistency with historical trends"
    - "Update reliability scores with proper recalculation and validation"
    - "Trigger alerts for significant performance changes or anomalies"
    - "Synchronize updates with dependent systems and operator ranking"

  post_relationship_change:
    - "Update operator scoring with relationship impact assessment"
    - "Notify dependent systems of operator status changes"
    - "Maintain audit trail with change attribution and approval tracking"
    - "Trigger workflow updates with business process integration"

  # Scheduled Maintenance
  daily_data_validation:
    - "Validate operator data consistency with relationship integrity checks"
    - "Update performance metrics with latest booking and feedback data"
    - "Clean up expired solicitation campaigns with proper archival"
    - "Generate performance reports with trend analysis and insights"

  weekly_relationship_maintenance:
    - "Execute automated relationship scoring updates with performance integration"
    - "Process operator feedback with improvement recommendation generation"
    - "Update market benchmarks with industry data integration"
    - "Generate relationship health reports with actionable insights"

primary_flows:
  operator_onboarding_flow:
    steps:
      - "Operator Registration: Collect basic operator information and business details"
      - "Data Enrichment: Automated web scraping and API integration for profile completion"
      - "Fleet Integration: Aircraft assignment and availability synchronization setup"
      - "Contact Management: Multi-representative setup with role assignment and preferences"
      - "Performance Baseline: Historical data integration and initial reliability scoring"
      - "Compliance Verification: Regulatory requirement validation and approval process"
    
    automation:
      - "Automated data enrichment with web scraping and external API integration"
      - "Real-time fleet synchronization with aircraft availability and status updates"
      - "Dynamic pricing rule application with market factor integration"

  performance_tracking_flow:
    steps:
      - "Data Collection: Continuous performance data gathering from bookings and feedback"
      - "Metric Calculation: Multi-factor reliability scoring with weighted analysis"
      - "Trend Analysis: Historical performance tracking with seasonal adjustment"
      - "Benchmarking: Industry comparison with competitive analysis and positioning"
      - "Prediction: Machine learning-powered performance forecasting with confidence intervals"
      - "Optimization: Actionable insights with improvement recommendations and tracking"
    
    integration:
      - "Real-time booking system integration with performance impact tracking"
      - "Customer feedback correlation with satisfaction scoring and improvement tracking"
      - "Market data integration with competitive benchmarking and positioning analysis"

  solicitation_campaign_flow:
    steps:
      - "Campaign Planning: Operator segmentation with targeting and messaging optimization"
      - "Content Generation: Personalized solicitation emails with dynamic content and branding"
      - "Delivery Management: Automated email delivery with rate limiting and error handling"
      - "Response Tracking: Real-time response monitoring with structured data validation"
      - "Follow-up Processing: Automated reminder systems with escalation and optimization"
      - "Performance Analysis: Campaign effectiveness measurement with optimization insights"
    
    automation:
      - "Weekly automated solicitation with intelligent timing and frequency optimization"
      - "Response processing with machine learning-powered data extraction and validation"
      - "Preference management with real-time opt-in/opt-out processing and compliance"

  relationship_optimization_flow:
    steps:
      - "Relationship Assessment: Current relationship health evaluation with multi-factor analysis"
      - "Performance Correlation: Business value correlation with relationship quality metrics"
      - "Issue Identification: Proactive problem detection with automated alerting and escalation"
      - "Improvement Planning: Strategic relationship enhancement with actionable recommendations"
      - "Implementation Tracking: Progress monitoring with milestone achievement and validation"
      - "Value Measurement: ROI analysis with relationship impact quantification and optimization"
    
    intelligence:
      - "Machine learning-powered relationship scoring with predictive insights and recommendations"
      - "Automated issue detection with proactive resolution and relationship preservation"
      - "Performance-based operator selection with dynamic ranking and optimization"

scheduler_integration:
  operator_maintenance:
    - task: "Daily Performance Update"
      schedule: "daily 2:00 AM"
      purpose: "Update operator performance metrics with latest booking and feedback data"
      handler: "operator_reliability_service.py:update_daily_performance_metrics"
      optimization: "Real-time performance tracking with automated reliability score updates"

    - task: "Weekly Reliability Calculation"
      schedule: "weekly Sunday 3:00 AM"
      purpose: "Comprehensive operator reliability recalculation with trend analysis"
      handler: "operator_reliability_service.py:calculate_weekly_reliability_scores"
      analytics: "Statistical analysis with confidence intervals and benchmarking"

    - task: "Monthly Relationship Scoring"
      schedule: "monthly 1st 4:00 AM"
      purpose: "Comprehensive relationship health assessment with strategic insights"
      handler: "operator_service.py:calculate_monthly_relationship_scores"
      intelligence: "Relationship optimization with strategic partnership identification"

  solicitation_automation:
    - task: "Weekly Empty Leg Solicitation"
      schedule: "weekly Monday 9:00 AM"
      purpose: "Automated empty leg solicitation campaign with operator outreach"
      handler: "empty_leg_solicitation_service.py:start_weekly_solicitation"
      outreach: "Intelligent operator targeting with personalized messaging and optimization"

    - task: "Daily Response Processing"
      schedule: "daily 10:00 AM"
      purpose: "Process operator responses with structured data validation"
      handler: "empty_leg_solicitation_service.py:process_daily_responses"
      validation: "Automated response processing with quality validation and integration"

    - task: "Weekly Campaign Analytics"
      schedule: "weekly Friday 5:00 PM"
      purpose: "Campaign performance analysis with optimization recommendations"
      handler: "operator_communication_service.py:analyze_weekly_campaigns"
      optimization: "Campaign effectiveness measurement with strategic insights and improvements"

endpoints:
  operator_management:
    - path: "/api/v1/aircraft/operators"
      methods: ["GET"]
      description: "List operators with filtering by reliability, category, and region"
      response_time: "<500ms"
      handler: "operators.py:list_operators"
      access: "Public access with optional authentication for enhanced filtering"

    - path: "/api/v1/admin/operators"
      methods: ["GET", "POST"]
      description: "Admin operator management with comprehensive CRUD operations"
      response_time: "<1000ms"
      handler: "admin_ui.py:get_admin_operators"
      access: "Admin-only access for operator management and configuration"

    - path: "/api/v1/admin/operators/{operator_id}"
      methods: ["GET", "PUT", "DELETE"]
      description: "Individual operator management with detailed operations"
      response_time: "<1000ms"
      handler: "admin_ui.py:operator_detail_operations"
      access: "Admin-only access for operator profile management"

  reliability_tracking:
    - path: "/api/v1/admin/reliability/{operator_id}"
      methods: ["GET"]
      description: "Detailed operator reliability metrics and analytics"
      response_time: "<2000ms"
      handler: "reliability_v2.py:get_operator_reliability"
      access: "Admin-only access for performance analytics and insights"

  solicitation_management:
    - path: "/api/v1/empty-legs/solicitation/start"
      methods: ["POST"]
      description: "Start weekly operator solicitation campaign"
      response_time: "<1000ms"
      handler: "empty_legs.py:start_weekly_empty_leg_solicitation"
      access: "Admin-only access for campaign management and coordination"

    - path: "/api/v1/empty-legs/solicitation/operators"
      methods: ["GET"]
      description: "Get operators eligible for solicitation campaigns"
      response_time: "<500ms"
      handler: "empty_legs.py:get_operators_for_solicitation"
      access: "Admin-only access for campaign planning and operator segmentation"

    - path: "/api/v1/empty-legs/solicitation/response/{operator_id}"
      methods: ["POST"]
      description: "Track operator response to solicitation campaigns"
      response_time: "<1000ms"
      handler: "empty_legs.py:track_operator_response"
      access: "Admin-only access for response tracking and performance measurement"

error_handling:
  operator_errors:
    - "OperatorNotFoundError: Operator ID not found in database with recovery suggestions"
    - "OperatorValidationError: Operator data validation failure with field-specific error details"
    - "FleetRelationshipError: Aircraft-operator relationship inconsistency with resolution options"
    - "ReliabilityCalculationError: Performance metric calculation failure with fallback options"
    - "SolicitationError: Email campaign delivery failure with retry and escalation procedures"
    - "CommunicationPreferenceError: Operator preference update failure with validation guidance"

  data_integrity_errors:
    - "DuplicateOperatorError: Operator uniqueness violation with merge and resolution options"
    - "PerformanceDataError: Insufficient data for reliability calculation with data collection guidance"
    - "RelationshipConstraintError: Operator relationship constraint violation with repair procedures"
    - "PricingRuleError: Pricing configuration inconsistency with validation and correction guidance"

testing:
  operator_testing:
    - test_type: "Unit Tests"
      coverage: "Operator service methods, reliability calculations, and data validation"
      location: "tests/unit/services/test_operator_service.py"

    - test_type: "Integration Tests"
      coverage: "Complete operator workflows from creation through performance tracking"
      location: "tests/integration/services/test_operator_integration.py"

    - test_type: "Repository Tests"
      coverage: "Database operations, query optimization, and relationship management"
      location: "tests/unit/repositories/test_operator_repository.py"

    - test_type: "API Tests"
      coverage: "Operator API endpoints, authentication, and response validation"
      location: "tests/integration/endpoints/test_operator_endpoints.py"

security:
  operator_security:
    - "Operator data access controlled by role-based authentication with admin-only management functions"
    - "Performance metrics restricted to authorized personnel with audit trail maintenance"
    - "Solicitation campaigns require explicit admin approval with compliance monitoring"
    - "Communication preferences respect opt-out requests with immediate effect and audit logging"
    - "Data enrichment processes validate source authenticity with quality assurance checks"
    - "API endpoints implement rate limiting with abuse prevention and monitoring"

performance:
  operator_performance_targets:
    - "Operator listing queries: <500ms response time with pagination and filtering support"
    - "Reliability calculation processing: <2000ms per operator with comprehensive metrics"
    - "Solicitation campaign delivery: <5000ms per campaign with batch processing optimization"
    - "Performance metric updates: <1000ms per operator with real-time synchronization"
    - "Data enrichment processing: <10000ms per operator with quality validation"
    - "Relationship scoring calculations: <3000ms per operator with predictive analytics"

consolidation_notes:
  operator_domain_ownership:
    - "Complete operator lifecycle management consolidated in OperatorService with database manager integration"
    - "Performance tracking centralized in OperatorReliabilityService with predictive analytics capabilities"
    - "Communication coordination unified in OperatorCommunicationService with preference management"
    - "Solicitation automation standardized in EmptyLegSolicitationService with campaign optimization"
    - "Fleet management integration consolidated through aircraft relationships with availability synchronization"
    - "Pricing optimization integrated through operator-specific rules with dynamic market adjustments"