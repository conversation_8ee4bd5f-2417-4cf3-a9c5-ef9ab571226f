system_intent: Villiers.ai enables seamless private jet booking through intelligent trip planning, real-time operator integration,
  and personalized customer experiences. The platform handles the complete booking lifecycle from natural language trip requests
  to post-flight feedback, optimizing pricing, availability, and service quality.
metadata:
  title: Villiers.ai System Definitions - Canonical Index
  description: Complete domain-driven system definition index with full content inlining for private jet charter platform
  generated: '2025-06-21T11:17:55.501015+00:00'
  domains_count: 7
  architecture_type: domain_driven_design
  platform_type: private_jet_charter_platform
  validation_status: post_analytics_format_correction
  total_systems: 23
  domains_processed: 7
structure:
  core_domains:
    core:
      description: Villiers.ai Core Domain - Foundational Infrastructure Layer providing essential services, shared utilities,
        security infrastructure, error handling, configuration management, and cross-cutting concerns that enable consistent
        operation across the entire charter aviation platform
      systems_count: 1
      has_main_system: true
      subdomains: []
      primary_services: []
      api_endpoints: []
    aircraft:
      description: Villiers.ai Aircraft Domain - Comprehensive aircraft fleet management system encompassing aircraft specifications,
        real-time positioning, availability tracking, image management, repositioning optimization, empty leg opportunities,
        and maintenance scheduling for private jet charter operations
      systems_count: 5
      has_main_system: true
      subdomains:
      - availability_maintenance
      - aircraft_tracking
      - positioning_empty_legs
      - aircraft_images
      primary_services:
      - aircraft_service.py
      - positioning_service.py
      - aircraft_tracking_service.py
      - adsb_service.py
      - aircraft_images_service.py
      api_endpoints: []
    booking:
      description: Villiers.ai Comprehensive Booking Domain - World-Class Private Jet Booking Ecosystem with Complete Lifecycle
        Management, Multi-Leg Trip Planning, Shared Flight Capabilities, Empty Leg Optimization, Quote Orchestration, and
        Advanced Payment Processing. This unified domain encompasses all booking-related operations from initial trip planning
        through post-flight completion, including sophisticated revenue optimization through shared flights and empty leg
        marketplace.
      systems_count: 6
      has_main_system: true
      subdomains:
      - trip_planning_trip_planning
      - quotes_quotes
      - empty_legs_empty_legs
      - booking_lifecycle_booking_lifecycle
      - shared_flights_shared_flights
      primary_services:
      - empty_leg_processor.py
      - orchestration_service.py
      - orchestration.py
      - flight_sharing.py
      - empty_leg_processor_service.py
      - booking_service.py
      - trips_service.py
      - flight_sharing_service.py
      - payment_service.py
      api_endpoints: []
    airport:
      description: Villiers.ai Airport Domain - Comprehensive airport data management system encompassing global airport database,
        geographical search, distance calculations, fee management, slot availability, and operational metadata for private
        jet charter route planning and optimization
      systems_count: 3
      has_main_system: true
      subdomains:
      - search_discovery
      - distance_routing
      primary_services:
      - airports.py
      - airport_service.py
      api_endpoints: []
    communication:
      description: Multi-channel customer and operator communication, notification orchestration, and message delivery across
        the charter aviation platform
      systems_count: 3
      has_main_system: true
      subdomains:
      - chat_chat
      - email_email
      primary_services: []
      api_endpoints: []
    authentication:
      description: Villiers.ai Comprehensive Authentication System - World-Class Email-Based Passwordless Authentication with
        BIP39 Mnemonic Restoration Keys and Bulletproof Security. This system owns all user identity, authentication, registration,
        session management, and account recovery logic across the entire platform.
      systems_count: 1
      has_main_system: true
      subdomains: []
      primary_services:
      - email_service.py
      - auth_service.py
      api_endpoints: []
    analytics:
      description: Villiers.ai Analytics Domain - Comprehensive analytics and business intelligence system for data-driven
        decision making across all Villiers.ai operations including event tracking, user behavior analysis, conversion optimization,
        performance monitoring, and business intelligence reporting
      systems_count: 4
      has_main_system: true
      subdomains:
      - admin_dashboards
      - user_behavior
      - performance_monitoring
      primary_services:
      - analytics_service.py
      - analytics.py
      api_endpoints: []
domains:
  core:
    main:
      system: core
      description: Villiers.ai Core Domain - Foundational Infrastructure Layer providing essential services, shared utilities,
        security infrastructure, error handling, configuration management, and cross-cutting concerns that enable consistent
        operation across the entire charter aviation platform
      intent_assertions:
      - Provide robust foundational infrastructure supporting all domain operations with zero single points of failure
      - Ensure comprehensive security enforcement across authentication, authorization, data validation, and API protection
      - Deliver consistent error handling and recovery mechanisms with centralized exception management
      - Enable efficient database operations through connection pooling, transaction management, and query optimization
      - Provide standardized API response patterns and middleware for consistent client experiences
      - Support scalable configuration management with environment-aware settings and feature flags
      - Enable comprehensive logging, monitoring, and observability across all system operations
      - Facilitate efficient service orchestration through dependency injection and lifecycle management
      - Ensure data integrity through comprehensive validation, sanitization, and security checks
      - Provide shared utilities and constants for consistent business logic across domains
      technical_assertions:
      - path: app/core/config.py
        purpose: Centralized configuration management with environment-aware settings
        lines: 256
        features:
        - Environment-based configuration with fallback mechanisms
        - Database URL management with test and migration support
        - External API key and service configuration
        - CORS and security settings management
        - Email provider configuration with multiple backends
        - Cached settings for production performance optimization
      - path: app/core/security.py
        purpose: JWT token management and cryptographic security utilities
        lines: 179
        operations:
        - create_access_token - JWT access token generation with expiry
        - create_refresh_token - Refresh token generation for session management
        - verify_token - Token validation and signature verification
        - generate_login_code - Secure random code generation for authentication
      - path: app/core/auth.py
        purpose: Authentication dependencies and user resolution middleware
        lines: 201
        dependencies:
        - get_current_user - Extract and validate authenticated user from token
        - get_current_superuser - Admin user authentication and authorization
        - get_optional_current_user - Optional authentication for mixed endpoints
        - verify_api_key - API key validation for service-to-service communication
      - path: app/core/errors.py
        purpose: Centralized error handling with standardized API responses
        lines: 282
        exception_types:
        - APIException - Base exception for all API errors with status codes
        - BadRequestError - Client request validation failures (400)
        - NotFoundError - Resource not found errors (404)
        - ValidationError - Data validation and format errors (422)
        - AuthenticationError - Authentication failures (401)
        - AuthorizationError - Permission and access control failures (403)
        - DatabaseOperationError - Database operation failures (500)
      - path: app/core/api_response.py
        purpose: Standardized API response patterns with consistent formatting
        lines: 357
        response_types:
        - success_response - Standardized success responses with data payload
        - error_response - Consistent error response format with details
        - paginated_response - Paginated list responses with metadata
        - created_response - Resource creation success responses (201)
        - accepted_response - Asynchronous operation acceptance (202)
      - path: app/core/validation.py
        purpose: Comprehensive security validation and input sanitization
        lines: 495
        security_features:
        - XSS pattern detection and prevention
        - SQL injection pattern validation
        - HTML tag sanitization and filtering
        - Name and contact information validation
        - UUID and email security validation
        - Comprehensive security validator factories
      - path: app/core/database_config.py
        purpose: Database configuration utilities with fallback mechanisms
        lines: 134
        capabilities:
        - Environment-aware database URL resolution
        - Test database configuration with isolation
        - Alembic migration database management
        - Centralized configuration with fallback support
      - path: app/core/logger.py
        purpose: Structured logging with security data masking
        lines: 116
        features:
        - Sensitive data masking for security compliance
        - Service-specific logger creation with context
        - Structured logging with JSON context support
        - Centralized log formatting and configuration
      - path: app/core/middleware.py
        purpose: Request/response middleware for cross-cutting concerns
        lines: 151
        middleware_types:
        - SessionMiddleware - Anonymous session management with cookies
        - SecurityMiddleware - Security headers and protection
        - RequestLoggingMiddleware - Request/response logging and tracing
      - path: app/core/scheduler_integration.py
        purpose: Application-wide scheduler initialization and lifecycle management
        lines: 97
        operations:
        - initialize_scheduler - Startup scheduler initialization with database integration
        - shutdown_scheduler - Graceful scheduler shutdown and cleanup
        - register_system_jobs - Automated system job registration
      - path: app/core/constants.py
        purpose: Centralized business logic constants and configuration defaults
        lines: 65
        constant_categories:
        - AI model configuration with fallback support
        - Authentication defaults and security parameters
        - Business rules for fees, timeouts, and operational limits
        - Email workflow configuration and intervals
      - path: app/core/utils.py
        purpose: Common utility functions and helper methods
        lines: 8
        utilities:
        - UTC timestamp generation and date utilities
      - path: app/core/user_agent_utils.py
        purpose: User agent detection and device analytics
        lines: 303
        capabilities:
        - Device type detection (mobile, tablet, desktop)
        - Browser and operating system identification
        - User agent parsing for analytics and security
        - Request integration utilities for FastAPI
      endpoints:
        health_monitoring:
        - path: /health
          methods:
          - GET
          description: System health check with comprehensive metrics
          response_time: <100ms
          handler: main.py:health_check
        - path: /inspector
          methods:
          - GET
          description: Database inspector interface for development
          response_time: <500ms
          handler: main.py:inspector_page
        api_root:
        - path: /
          methods:
          - GET
          description: API root endpoint with service information
          response_time: <50ms
          handler: main.py:root
      database_models:
        configuration:
        - model: SystemConfiguration
          purpose: System-wide configuration storage
          fields:
          - key
          - value
          - category
          - environment
          - updated_at
          indexes:
          - key
          - category
          - environment
        audit_logging:
        - model: AuditLog
          purpose: Comprehensive audit trail for security and compliance
          fields:
          - event_type
          - user_id
          - resource_id
          - action
          - details
          - timestamp
          - ip_address
          indexes:
          - event_type
          - user_id
          - timestamp
          - resource_id
      services:
      - service: BaseService
        path: app/services/base_service.py
        purpose: Abstract base service providing common functionality for all services
        methods:
        - __init__
        dependencies:
        - db_manager
        inheritance: Abstract base class for all domain services
      - service: ServiceManager
        path: app/service_manager.py
        purpose: Service orchestration and dependency injection management
        status: placeholder_for_future_implementation
        design: Centralized service lifecycle management and dependency resolution
      repositories:
      - repository: BaseRepository
        path: app/db/manager/base_repository.py
        purpose: Abstract base repository providing common data access patterns
        methods:
        - get_by_id
        - create
        - update
        - delete
        - list_with_pagination
        inheritance: Base class for all domain repositories
      - repository: ConfigurationRepository
        purpose: System configuration data access and management
        methods:
        - get_config_value
        - set_config_value
        - get_config_by_category
        validation:
        - configuration_key_format
        - environment_validation
      schemas:
        core_responses:
        - schema: ApiResponse
          purpose: Standardized API response wrapper with generic data support
          fields:
          - status
          - code
          - message
          - request_id
          - data
          validation:
          - response_format
          - status_code_consistency
        - schema: PaginatedResponse
          purpose: Paginated response format with metadata
          fields:
          - status
          - code
          - message
          - data
          - meta
          - request_id
          validation:
          - pagination_metadata
          - data_list_format
        - schema: APIError
          purpose: Standardized error response format
          fields:
          - status
          - code
          - message
          - details
          - error_type
          - request_id
          validation:
          - error_format
          - sensitive_data_exclusion
        validation_schemas:
        - schema: ValidationPatterns
          purpose: Security validation patterns and rules
          patterns:
          - XSS_PATTERNS
          - SQL_INJECTION_PATTERNS
          - HTML_TAGS
          - NAME_PATTERN
          security: Input sanitization and attack prevention
      behavior:
        configuration_management:
          environment_awareness:
          - Development environment uses fresh settings for rapid iteration
          - Production environment uses cached settings for performance optimization
          - Test environment uses isolated configuration with database separation
          - Configuration fallback mechanisms ensure system stability
          setting_resolution:
          - 'Priority order: environment variables > .env file > default values'
          - Database URLs support test and migration environment separation
          - External API keys loaded securely with validation
          - CORS origins configured per environment with security defaults
        security_enforcement:
          authentication_flow:
          - JWT tokens generated with cryptographically secure random JTI
          - Token validation includes signature verification and expiry checks
          - Refresh tokens enable secure session extension without re-authentication
          - Optional authentication supports mixed public/private endpoints
          authorization_patterns:
          - Role-based access control with admin user validation
          - Resource ownership validation for user-specific data
          - API key authentication for service-to-service communication
          - Permission checking with clear error responses
        error_handling_workflow:
          exception_processing:
          - Centralized exception handlers registered with FastAPI application
          - Database exceptions transformed to appropriate API responses
          - Validation errors provide detailed field-level feedback
          - Unexpected errors logged with request context for debugging
          response_consistency:
          - All errors follow standardized response format with status codes
          - Error details include actionable information without sensitive data
          - Request IDs enable error tracing across distributed operations
          - Error types categorize issues for client-side handling
        database_operations:
          connection_management:
          - Connection pooling optimized for environment (test vs production)
          - Pool size and timeout configuration based on load requirements
          - Connection health monitoring with pre-ping validation
          - Graceful connection cleanup on application shutdown
          transaction_handling:
          - Automatic retry logic for transient database infrastructure issues
          - Application-level errors preserved without retry interference
          - Read-only transaction optimization for query operations
          - Transaction isolation levels appropriate for operation types
      invariants:
      - All API responses must follow standardized format with consistent status codes
      - Security validation must be applied to all user inputs across domains
      - Database connections must be properly managed with connection pooling
      - Error handling must provide clear feedback without exposing sensitive data
      - Configuration must be environment-aware with appropriate fallback mechanisms
      - Logging must mask sensitive data while providing adequate debugging information
      - Authentication tokens must be cryptographically secure with proper expiry
      - Service dependencies must be properly injected and lifecycle managed
      - Input validation must prevent XSS, SQL injection, and other security vulnerabilities
      - Middleware must process requests consistently across all endpoints
      forbidden_states:
      - Sensitive data (passwords, tokens, keys) exposed in logs or error messages
      - Database connections left open without proper cleanup
      - Configuration secrets stored in version control or logs
      - API responses with inconsistent format or missing error handling
      - Authentication bypasses or security validation circumvention
      - Unvalidated user input processed by business logic
      - Database operations without proper transaction management
      - Services initialized without proper dependency injection
      - Error responses exposing internal system details or stack traces
      - Middleware failing to process security headers or session management
      depends_on:
      - system: database
        purpose: Persistent storage and transaction management
        components:
        - postgresql
        - connection_pooling
        - migration_management
      provides:
      - foundational_infrastructure
      - security_enforcement
      - error_handling_framework
      - api_response_standardization
      - configuration_management
      - logging_and_monitoring
      - database_connection_management
      - service_orchestration_framework
      - input_validation_and_sanitization
      - middleware_and_request_processing
      files:
        core_infrastructure:
        - app/core/config.py - Centralized configuration with environment awareness
        - app/core/security.py - JWT token management and cryptographic utilities
        - app/core/auth.py - Authentication dependencies and user resolution
        - app/core/errors.py - Centralized error handling and exception management
        - app/core/api_response.py - Standardized API response patterns
        - app/core/validation.py - Security validation and input sanitization
        - app/core/database_config.py - Database configuration utilities
        - app/core/logger.py - Structured logging with security masking
        - app/core/middleware.py - Request/response middleware
        - app/core/scheduler_integration.py - Scheduler lifecycle management
        - app/core/constants.py - Business logic constants
        - app/core/utils.py - Common utility functions
        - app/core/user_agent_utils.py - User agent detection and analytics
        - app/core/json_encoder.py - Custom JSON encoding for API responses
        - app/core/responses.py - HTTP response utilities
        - app/core/enums.py - Core enumeration types
        service_framework:
        - app/services/base_service.py - Abstract base service class
        - app/service_manager.py - Service orchestration (placeholder)
        - app/api/deps.py - FastAPI dependency injection utilities
        application_setup:
        - app/main.py - FastAPI application initialization and configuration
      implementation_status:
        fully_implemented:
        - Comprehensive configuration management with environment awareness
        - JWT-based authentication and authorization infrastructure
        - Centralized error handling with standardized API responses
        - Security validation and input sanitization framework
        - Database connection management with pooling and transaction support
        - Structured logging with sensitive data masking
        - API response standardization with consistent formatting
        - Middleware framework for cross-cutting concerns
        - User agent detection and device analytics
        - Business constants and configuration defaults
        partially_implemented:
        - Service manager for dependency injection (placeholder exists)
        - Comprehensive audit logging (basic framework in place)
        - Advanced monitoring and observability (basic health checks implemented)
        - Feature flag management (configuration framework supports it)
        implementation_gaps:
        - Advanced service orchestration with dependency injection container
        - Comprehensive audit trail implementation with detailed logging
        - Advanced monitoring with metrics collection and alerting
        - Feature flag management interface and API
        - Configuration management UI for runtime settings
        - Advanced caching layer with Redis integration
        - Rate limiting middleware for API protection
        - Request/response compression middleware
        planned_enhancements:
        - Service mesh integration for microservices architecture
        - Advanced security middleware with threat detection
        - Performance monitoring with distributed tracing
        - Configuration encryption for sensitive settings
        - Advanced logging with structured query capabilities
        - API versioning framework with backward compatibility
        - Circuit breaker patterns for external service resilience
        - Advanced validation with custom rule engines
      performance_requirements:
        response_times:
        - 'Health check endpoint: <100ms'
        - 'Configuration retrieval: <50ms'
        - 'Token validation: <25ms'
        - 'Error response generation: <10ms'
        - 'Validation operations: <5ms'
        scalability_targets:
        - 'Database connection pool: 20 connections with 10 overflow'
        - 'Concurrent request handling: 1000+ requests/second'
        - 'Memory usage: <512MB for core services'
        - 'CPU utilization: <30% under normal load'
      security_considerations:
        authentication_security:
        - JWT tokens with cryptographically secure generation
        - Token expiry enforcement with automatic refresh
        - Secure session management with proper cleanup
        data_protection:
        - Sensitive data masking in logs and error responses
        - Input validation preventing injection attacks
        - Secure configuration management with environment isolation
        api_security:
        - CORS configuration with environment-specific origins
        - Security headers enforcement through middleware
        - Request/response logging without sensitive data exposure
      integration_points:
        cross_domain_services:
        - Authentication services consumed by all domains
        - Database management used across all data operations
        - Error handling framework used by all API endpoints
        - Validation utilities used across all input processing
        - Configuration management accessed by all services
        external_systems:
        - Database infrastructure (PostgreSQL)
        - Email services (Mailgun, SMTP)
        - AI services (OpenAI)
        - Payment processors (Stripe, BTCPay)
        - Monitoring and logging systems
      monitoring_and_observability:
        health_monitoring:
        - System health endpoint with comprehensive metrics
        - Database connection health monitoring
        - Service dependency health checks
        logging_strategy:
        - Structured logging with JSON formatting
        - Request/response logging with correlation IDs
        - Error logging with context and stack traces
        - Security event logging for audit trails
        performance_monitoring:
        - Response time tracking for all endpoints
        - Database query performance monitoring
        - Memory and CPU usage tracking
      enforcement_hooks:
      - validate_configuration_consistency
      - ensure_security_headers_present
      - confirm_error_handling_coverage
      - verify_input_validation_completeness
      - validate_authentication_requirements
      domain: core
      subdomain: null
      path: core/core.yaml
      file_size_kb: 20.41
  aircraft:
    main:
      system: aircraft
      description: Villiers.ai Aircraft Domain - Comprehensive aircraft fleet management system encompassing aircraft specifications,
        real-time positioning, availability tracking, image management, repositioning optimization, empty leg opportunities,
        and maintenance scheduling for private jet charter operations
      intent_assertions:
      - Must provide complete aircraft fleet visibility with real-time positioning and status
      - Automated aircraft tracking through ADS-B Exchange integration with intelligent polling
      - Dynamic empty leg opportunity detection and marketing optimization
      - Comprehensive aircraft specification database with detailed performance metrics
      - Real-time availability management synchronized with booking lifecycle
      - Advanced aircraft image management with compression and metadata tracking
      - Intelligent repositioning cost calculations and optimization algorithms
      - Multi-operator fleet management with operator-specific configurations
      - Aircraft maintenance tracking with predictive scheduling capabilities
      - Integration with booking system for seamless aircraft assignment and scheduling
      technical_assertions:
      - path: /app/api/v1/endpoints/aircraft/aircraft.py
        desc: Primary aircraft API endpoints (558 lines) - CRUD operations, search, availability checking
        critical: true
      - path: /app/services/aircraft_service.py
        desc: Core aircraft business logic service (319 lines) using centralized database manager pattern
        critical: true
      - path: /app/db/models/aircraft.py
        desc: Aircraft database models (330 lines) - AircraftType, Aircraft, AircraftCategory, AircraftImage
        critical: true
      - path: /app/db/schemas/aircraft.py
        desc: Aircraft API schemas (368 lines) with forward references and relationship handling
        critical: true
      - path: /app/api/v1/endpoints/aircraft/positioning.py
        desc: Aircraft positioning API endpoints (208 lines) - current positions, movements, empty legs
        critical: true
      - path: /app/services/aircraft_tracking_service.py
        desc: Aircraft tracking service (341 lines) with intelligent polling and fleet monitoring
        critical: true
      - path: /app/services/positioning_service.py
        desc: Positioning service (695 lines) with cost calculations and movement management
        critical: true
      - path: /app/services/adsb_service.py
        desc: ADS-B Exchange integration service (460 lines) with rate limiting and caching
        critical: true
      - path: /app/db/models/aircraft_position.py
        desc: Aircraft position tracking models (184 lines) with spatial indexing
        critical: true
      - path: /app/db/models/positioning.py
        desc: Positioning models (218 lines) - AircraftPositioning, AircraftMovement, AircraftAvailability
        critical: true
      - path: /app/api/v1/endpoints/aircraft/repositioning.py
        desc: Aircraft repositioning API endpoints (157 lines) - empty leg opportunities and bookings
        critical: true
      - path: /app/services/aircraft_images_service.py
        desc: Aircraft image management service (484 lines) with compression and storage
        critical: true
      - path: /app/tasks/aircraft_tracking.py
        desc: Aircraft tracking scheduled tasks (131 lines) - position cleanup and API counter reset
        critical: true
      behavior: null
      aircraft_specification_management:
      - Maintain comprehensive aircraft type database with performance specifications
      - Support aircraft categories (Light, Midsize, Super-midsize, Large) with operational constraints
      - Track individual aircraft with operator assignments and maintenance schedules
      - Manage aircraft images with automatic compression and metadata extraction
      position_tracking_workflow:
      - Continuously monitor aircraft positions through ADS-B Exchange API integration
      - Implement intelligent polling with priority-based updates (active flights every 3 minutes)
      - Cache position data for 10 minutes to optimize API usage within monthly limits
      - Store position history with intelligent data retention (24hr full, 7-day hourly, 30-day daily)
      - Track nearest airports and calculate positioning costs automatically
      empty_leg_optimization:
      - Automatically detect empty leg opportunities from aircraft movements
      - Calculate optimal repositioning routes and costs
      - Market empty legs with dynamic discount pricing
      - Synchronize with booking system for availability conflicts
      availability_management:
      - Real-time aircraft availability tracking synchronized with booking lifecycle
      - Support maintenance windows and operator-specific constraints
      - Automated conflict detection and resolution suggestions
      - Integration with quote generation for accurate aircraft assignment
      aircraft_search_capabilities:
      - Multi-criteria aircraft search (category, capacity, range, airport proximity)
      - Similar aircraft recommendations for alternative options
      - Geographic search within radius of airports or coordinates
      - Performance-based filtering with operational constraints
      invariants:
      - Aircraft registration must be unique across the system
      - Aircraft type relationships must be valid and maintained
      - Position data must include timestamp and source attribution
      - Availability periods cannot overlap for the same aircraft
      - Maintenance schedules must not conflict with active bookings
      - ADS-B Exchange API calls must not exceed 8,000 per month (80% of limit)
      - Position updates must respect minimum 30-second intervals between calls
      - Cache must be utilized to minimize redundant API requests
      - Aircraft must have valid operator assignment before activation
      - Empty leg opportunities must have valid departure and arrival airports
      - Repositioning costs must be calculated using current fuel and operational rates
      forbidden_states:
      - Aircraft without valid aircraft type reference
      - Position data without proper timestamp or source attribution
      - Empty legs with invalid or conflicting airport assignments
      - Availability periods extending beyond aircraft operational limits
      - Exceeding ADS-B Exchange API monthly or daily rate limits
      - Storing position data without proper compression or retention policies
      - Aircraft tracking without proper error handling and fallback mechanisms
      - Exposing sensitive aircraft operator information to unauthorized users
      - Allowing unauthorized modifications to aircraft specifications or positioning data
      depends_on:
      - operator: Aircraft must be associated with valid operators
      - airport: Positioning and movements require valid airport references
      - booking: Availability synchronization with booking lifecycle
      - pricing: Repositioning cost calculations and empty leg pricing
      - external_adsb_exchange: Real-time aircraft position data via ADS-B Exchange API
      - external_aviation_stack: Backup aircraft data source for positioning fallback
      - external_opensky: Alternative aircraft tracking data source
      provides:
      - aircraft_fleet_data: Complete aircraft specifications and performance data
      - real_time_positions: Current aircraft locations with sub-10-minute accuracy
      - availability_status: Real-time aircraft availability for booking systems
      - empty_leg_opportunities: Dynamically generated empty leg flight opportunities
      - repositioning_costs: Accurate positioning cost calculations for operations
      - aircraft_images: Compressed and optimized aircraft media assets
      - maintenance_schedules: Aircraft maintenance tracking and predictive scheduling
      - fleet_analytics: Aircraft utilization and performance metrics
      enforcement_hooks: null
      pre_aircraft_create:
      - Validate aircraft registration uniqueness across system
      - Verify aircraft type and operator exist and are active
      - Ensure required operational specifications are provided
      pre_position_update:
      - Validate position data completeness and accuracy
      - Check ADS-B API rate limits before making requests
      - Verify aircraft exists and is active for tracking
      pre_empty_leg_creation:
      - Validate movement has valid departure and arrival airports
      - Ensure aircraft is available during proposed empty leg period
      - Calculate and validate pricing within operational constraints
      post_position_tracking:
      - Log API usage statistics and rate limit compliance
      - Monitor position data quality and source reliability
      - Alert on aircraft tracking failures or data gaps
      post_availability_update:
      - Validate availability periods do not conflict with existing bookings
      - Synchronize changes with dependent booking and quote systems
      - Update aircraft search indices for real-time discovery
      scheduled_maintenance:
      - Execute daily position data cleanup maintaining retention policies
      - Reset monthly API counters and usage tracking
      - Validate aircraft data integrity and repair inconsistencies
      security: null
      aircraft_data_access:
      - Restrict aircraft specification modifications to authorized operators
      - Limit sensitive operational data to authenticated users only
      - Implement role-based access for aircraft management functions
      external_api_security:
      - Secure storage and rotation of ADS-B Exchange API keys
      - Rate limiting protection to prevent API abuse
      - Audit logging for all external aircraft data requests
      aircraft_privacy:
      - Anonymize aircraft tracking data in public empty leg listings
      - Protect operator-specific aircraft operational details
      - Comply with aviation data sharing regulations and restrictions
      monitoring: null
      aircraft_system_health:
      - Track aircraft API response times and availability
      - Monitor ADS-B Exchange API usage and rate limit compliance
      - Measure aircraft search query performance and accuracy
      aircraft_data_quality:
      - Monitor position data accuracy and freshness
      - Track aircraft specification completeness and validity
      - Measure empty leg opportunity detection and conversion rates
      aircraft_utilization:
      - Track aircraft availability and booking conversion rates
      - Monitor empty leg opportunity generation and success rates
      - Measure aircraft search and discovery effectiveness
      error_handling: null
      adsb_api_failures:
      - Graceful degradation when ADS-B Exchange API is unavailable
      - Fallback to cached position data with staleness indicators
      - Alternative data source activation for critical operations
      aircraft_data_errors:
      - Handle aircraft specification validation failures with detailed errors
      - Resolve positioning conflicts through data source prioritization
      - Automated correction of minor aircraft data inconsistencies
      resource_exhaustion:
      - API rate limit exceeded handling with queuing and retry mechanisms
      - Storage limit management with automated data archival
      - Memory optimization for large fleet position tracking operations
      domain: aircraft
      subdomain: null
      path: aircraft/aircraft.yaml
      file_size_kb: 11.1
    availability_maintenance:
      system: aircraft_availability_maintenance
      description: Aircraft Availability and Maintenance Management - Real-time availability tracking, maintenance scheduling,
        conflict detection, and operational status management integrated with booking lifecycle
      intent_assertions:
      - Real-time aircraft availability tracking synchronized with booking system
      - Predictive maintenance scheduling with conflict avoidance
      - Automated availability conflict detection and resolution
      - Maintenance window management with operational impact assessment
      - Integration with operator schedules and owner preferences
      - Historical availability analytics for fleet optimization
      technical_assertions:
      - path: /app/db/models/aircraft.py
        desc: AvailabilityStatus model for tracking aircraft operational status
        critical: true
      - path: /app/db/models/availability.py
        desc: Availability patterns and maintenance scheduling models
        critical: true
      - path: /app/services/aircraft_service.py
        desc: Availability management within core aircraft service
        critical: true
      behavior: null
      availability_tracking:
      - Real-time status updates (available, booked, maintenance, reserved)
      - Time-based availability periods with start/end timestamps
      - Location-aware availability with departure/arrival airports
      - Integration with booking system for automatic status updates
      maintenance_management:
      - Scheduled maintenance tracking with due dates and intervals
      - Maintenance type categorization (routine, major, emergency)
      - Conflict detection with existing bookings and availability
      - Maintenance history tracking with performed dates and notes
      conflict_resolution:
      - Automated detection of availability conflicts
      - Booking vs maintenance schedule conflict alerts
      - Alternative aircraft suggestions for conflicted periods
      - Operator notification system for critical conflicts
      operational_analytics:
      - Aircraft utilization rate calculations
      - Maintenance schedule optimization recommendations
      - Availability pattern analysis for fleet planning
      - Downtime impact assessment and cost analysis
      invariants:
      - Availability periods cannot overlap for the same aircraft
      - Maintenance schedules must not conflict with confirmed bookings
      - Aircraft status must be consistent across all systems
      - Availability updates must include valid timestamps and locations
      forbidden_states:
      - Overlapping availability periods for the same aircraft
      - Maintenance scheduled during confirmed booking periods
      - Availability status without proper audit trail
      - Maintenance records without required safety documentation
      depends_on:
      - aircraft: Aircraft specifications and operational data
      - booking: Booking lifecycle and reservation management
      - operator: Operator schedules and maintenance requirements
      - airport: Airport references for location-based availability
      provides:
      - availability_status: Real-time aircraft availability for booking systems
      - maintenance_schedules: Predictive maintenance planning and tracking
      - conflict_alerts: Automated conflict detection and resolution suggestions
      - utilization_analytics: Aircraft utilization and performance metrics
      enforcement_hooks: null
      pre_availability_update:
      - Validate availability periods do not conflict with existing bookings
      - Check maintenance schedules for potential conflicts
      - Ensure location information is valid and accessible
      pre_maintenance_schedule:
      - Verify no confirmed bookings during proposed maintenance window
      - Validate maintenance requirements and safety compliance
      - Check operator approval for maintenance scheduling
      post_status_change:
      - Update dependent systems with availability changes
      - Notify relevant stakeholders of status updates
      - Log status changes for audit and analytics purposes
      monitoring: null
      availability_metrics:
      - Track aircraft availability percentage and utilization rates
      - Monitor maintenance schedule adherence and delays
      - Measure conflict resolution effectiveness and response times
      domain: aircraft
      subdomain: availability_maintenance
      path: aircraft/availability_maintenance.yaml
      file_size_kb: 3.98
    aircraft_tracking:
      system: aircraft_tracking
      description: Aircraft Real-time Tracking Subsystem - Advanced ADS-B integration with intelligent polling, position caching,
        and fleet monitoring for continuous aircraft location awareness
      intent_assertions:
      - Real-time aircraft position tracking with sub-10-minute accuracy
      - Intelligent API rate limiting to stay within ADS-B Exchange monthly limits
      - Priority-based tracking with active flights getting frequent updates
      - Comprehensive position history with intelligent data retention
      - Spatial indexing for efficient geographic queries
      - Integration with fleet management for operational insights
      technical_assertions:
      - path: /app/services/aircraft_tracking_service.py
        desc: Core tracking service with intelligent polling and fleet monitoring
        critical: true
      - path: /app/services/adsb_service.py
        desc: ADS-B Exchange API integration with rate limiting and caching
        critical: true
      - path: /app/db/models/aircraft_position.py
        desc: Position data models with spatial indexing and GeoJSON support
        critical: true
      - path: /app/tasks/aircraft_tracking.py
        desc: Scheduled tasks for position cleanup and API management
        critical: true
      behavior: null
      intelligent_polling:
      - Active flights tracked every 3 minutes for real-time updates
      - Inactive aircraft polled every 22 minutes to conserve API calls
      - Priority-based scheduling based on flight status and recency
      - Automatic rate limiting with 30-second minimum intervals
      data_management:
      - Position cache with 10-minute TTL for API optimization
      - 'Intelligent data retention: 24hr full, 7-day hourly, 30-day daily'
      - Automated cleanup of position data older than 1 year
      - Spatial indexing for efficient geographic searches
      invariants:
      - API calls must not exceed 8,000 per month (266 per day)
      - Position data must include valid coordinates and timestamps
      - Aircraft must have valid ICAO hex codes for tracking
      - Cache TTL must be respected to prevent stale data usage
      depends_on:
      - aircraft: Aircraft specifications and registration data
      - external_adsb_exchange: Real-time position data via ADS-B Exchange API
      provides:
      - real_time_positions: Current aircraft locations with accuracy metadata
      - position_history: Historical position data for analysis and reporting
      - fleet_status: Active/inactive flight status for entire fleet
      - spatial_queries: Geographic search capabilities for aircraft positioning
      domain: aircraft
      subdomain: aircraft_tracking
      path: aircraft/aircraft_tracking.yaml
      file_size_kb: 2.39
    positioning_empty_legs:
      system: aircraft_positioning_empty_legs
      description: Aircraft Positioning and Empty Leg Management - Intelligent repositioning cost calculations, movement tracking,
        and empty leg opportunity detection with dynamic pricing optimization
      intent_assertions:
      - Accurate aircraft positioning cost calculations for operational planning
      - Automated empty leg opportunity detection from aircraft movements
      - Dynamic pricing optimization for empty leg marketing
      - Real-time movement tracking with departure/arrival management
      - Integration with booking system for availability synchronization
      - Multi-source positioning data with fallback mechanisms
      technical_assertions:
      - path: /app/api/v1/endpoints/aircraft/positioning.py
        desc: Positioning API endpoints for movements and empty legs
        critical: true
      - path: /app/api/v1/endpoints/aircraft/repositioning.py
        desc: Repositioning API endpoints for empty leg opportunities
        critical: true
      - path: /app/services/positioning_service.py
        desc: Core positioning service with cost calculations and movement management
        critical: true
      - path: /app/db/models/positioning.py
        desc: Positioning models for movements, availability, and empty legs
        critical: true
      behavior: null
      positioning_cost_calculation:
      - Calculate positioning costs per nautical mile with operator-specific rates
      - Factor in fuel costs, crew expenses, and operational overhead
      - Provide minimum positioning cost thresholds
      - Calculate cost-to-home-base for repositioning decisions
      empty_leg_detection:
      - Automatically detect empty leg opportunities from scheduled movements
      - Apply dynamic discount pricing based on route popularity and timing
      - Validate empty leg periods against aircraft availability
      - Generate marketing-ready empty leg listings with anonymized details
      movement_tracking:
      - Track scheduled vs actual departure/arrival times
      - Calculate flight distances and estimated flight times
      - Monitor movement status (scheduled, in_progress, completed, cancelled)
      - Integrate with aircraft availability for conflict detection
      data_integration:
      - Multi-source positioning data with OpenSky and Aviation Stack fallback
      - Real-time synchronization with aircraft tracking systems
      - Integration with booking system for availability conflicts
      - Automated positioning data updates with configurable frequency
      invariants:
      - Empty leg opportunities must have valid departure and arrival airports
      - Positioning costs must be calculated using current operational rates
      - Movement times must be chronologically consistent
      - Aircraft availability must not conflict with scheduled movements
      forbidden_states:
      - Empty legs without valid route information
      - Movements with conflicting departure/arrival times
      - Positioning costs calculated without valid rate data
      - Availability periods overlapping with confirmed movements
      depends_on:
      - aircraft: Aircraft specifications and operational data
      - airport: Valid airport references for movements
      - booking: Availability synchronization and conflict detection
      - pricing: Current operational rates for cost calculations
      - external_opensky: Backup positioning data source
      - external_aviation_stack: Alternative positioning data source
      provides:
      - positioning_costs: Accurate cost calculations for aircraft repositioning
      - empty_leg_opportunities: Dynamically generated empty leg flight opportunities
      - movement_tracking: Real-time aircraft movement status and history
      - availability_data: Aircraft availability synchronized with movements
      domain: aircraft
      subdomain: positioning_empty_legs
      path: aircraft/positioning_empty_legs.yaml
      file_size_kb: 3.52
    aircraft_images:
      system: aircraft_images
      description: Aircraft Image and Media Management - Advanced image processing, compression, storage, and metadata management
        for aircraft visual assets with automated optimization and batch processing capabilities
      intent_assertions:
      - Automated image compression and optimization for web delivery
      - Comprehensive metadata extraction and management
      - Support for multiple image types (exterior, interior, floor plans, cabin)
      - Batch processing of PDF documents with image extraction
      - Storage optimization with configurable disk/database storage
      - Integration with aircraft specifications for visual asset management
      technical_assertions:
      - path: /app/services/aircraft_images_service.py
        desc: Core image management service with compression and storage
        critical: true
      - path: /app/db/models/aircraft.py
        desc: AircraftImage model with metadata and storage path management
        critical: true
      - path: /app/schemas/aircraft_image.py
        desc: Image API schemas for upload, metadata, and batch operations
        critical: true
      behavior: null
      image_processing:
      - Automatic image compression with quality optimization (85% JPEG, optimized PNG)
      - Metadata extraction including dimensions, format, and file size
      - Support for multiple formats (JPEG, PNG, WebP) with format conversion
      - Compression ratio tracking and optimization reporting
      storage_management:
      - Configurable storage (database BLOB vs file system)
      - Organized directory structure by aircraft registration
      - Unique filename generation with timestamps and image types
      - Storage path management with fallback mechanisms
      metadata_tracking:
      - Complete image metadata including source attribution
      - Primary image designation per image type
      - Original filename preservation and content type tracking
      - Source reference tracking (email, PDF, upload, etc.)
      batch_operations:
      - PDF document processing with automatic image extraction
      - Batch upload support with progress tracking
      - Bulk image optimization and reprocessing capabilities
      - Automated image categorization and tagging
      invariants:
      - Each aircraft can have only one primary image per image type
      - Image data must be compressed before storage
      - All images must have valid metadata including dimensions and format
      - Storage paths must be unique and properly organized
      forbidden_states:
      - Images stored without compression or optimization
      - Multiple primary images of the same type for one aircraft
      - Images without proper metadata or source attribution
      - Storage without proper error handling or fallback mechanisms
      depends_on:
      - aircraft: Aircraft specifications and registration data
      - external_storage: File system or cloud storage for image assets
      - external_pil: Python Imaging Library for image processing
      provides:
      - aircraft_images: Compressed and optimized aircraft visual assets
      - image_metadata: Complete image metadata and categorization
      - batch_processing: PDF and bulk image processing capabilities
      - storage_optimization: Efficient image storage and retrieval systems
      enforcement_hooks: null
      pre_image_upload:
      - Validate image format and size constraints
      - Check aircraft exists and is accessible
      - Ensure storage capacity and permissions
      post_image_processing:
      - Verify compression was successful and within quality thresholds
      - Update image metadata and database records
      - Generate thumbnails and optimized versions if required
      security: null
      image_access_control:
      - Restrict image modifications to authorized users
      - Validate image content for security threats
      - Implement proper access controls for sensitive aircraft images
      domain: aircraft
      subdomain: aircraft_images
      path: aircraft/aircraft_images.yaml
      file_size_kb: 3.62
  booking:
    main:
      system: booking
      description: Villiers.ai Comprehensive Booking Domain - World-Class Private Jet Booking Ecosystem with Complete Lifecycle
        Management, Multi-Leg Trip Planning, Shared Flight Capabilities, Empty Leg Optimization, Quote Orchestration, and
        Advanced Payment Processing. This unified domain encompasses all booking-related operations from initial trip planning
        through post-flight completion, including sophisticated revenue optimization through shared flights and empty leg
        marketplace.
      intent_assertions:
      - Complete booking ecosystem management spanning trip planning, quote orchestration, booking lifecycle, shared flights,
        and empty leg optimization
      - Multi-leg trip planning with intelligent route optimization, aircraft positioning, and seamless passenger experience
        coordination
      - Advanced shared flight marketplace enabling seat-level booking, revenue sharing, and community-driven travel optimization
      - Comprehensive empty leg management with automated operator solicitation, marketplace integration, and booking optimization
        strategies
      - Sophisticated quote orchestration with AI-powered request processing, operator coordination, and dynamic pricing optimization
      - Complete booking lifecycle management with multi-payment support, real-time tracking, and automated post-flight processing
      - Revenue optimization through intelligent empty leg conversion, shared flight creation, and dynamic pricing strategies
      - Zero booking failures through automated recovery workflows, alternative route suggestions, and proactive optimization
      - Advanced payment processing with multi-method support, cryptocurrency integration, and operator settlement automation
      - Comprehensive audit trails, performance analytics, and operator reliability scoring across all booking operations
      technical_assertions:
      - path: app/api/v1/endpoints/bookings/trips.py
        purpose: Multi-leg trip planning and itinerary management API
        lines: 454
        subdomain: trip_planning
        endpoints:
        - GET /api/v1/bookings/trips/ - List user trips with filtering
        - POST /api/v1/bookings/trips/ - Create trip with multi-leg support
        - GET /api/v1/bookings/trips/{trip_id} - Get trip details
        - PUT /api/v1/bookings/trips/{trip_id} - Update trip itinerary
        - DELETE /api/v1/bookings/trips/{trip_id} - Delete trip
        - GET /api/v1/bookings/trips/user/{user_id} - Get user trip history
      - path: app/api/v1/endpoints/bookings/quotes.py
        purpose: AI-powered quote request processing and operator coordination
        lines: 48
        subdomain: quotes
        endpoints:
        - POST /api/v1/quotes/request - AI-powered quote request processing
        - GET /api/v1/quotes/{quote_id} - Get quote details for booking
      - path: app/api/v1/endpoints/bookings/bookings.py
        purpose: Primary booking lifecycle management with comprehensive status tracking
        lines: 1129
        subdomain: booking_lifecycle
        endpoints:
        - POST /api/v1/bookings/ - Create booking with quote confirmation
        - POST /api/v1/bookings/bitcoin - Create booking with Bitcoin payment
        - GET /api/v1/bookings/{booking_id} - Get detailed booking information
        - GET /api/v1/bookings/user/{user_id} - Get user booking history
        - PATCH /api/v1/bookings/{booking_id}/depart - Mark flight departed
        - PATCH /api/v1/bookings/{booking_id}/arrive - Mark passenger arrival
        - POST /api/v1/bookings/{booking_id}/complete - Complete post-flight processing
        - GET /api/v1/bookings/payment/{invoice_id}/status - Check payment status
        - POST /api/v1/bookings/{booking_id}/refund - Process booking refund
      - path: app/api/v1/endpoints/bookings/flight_sharing.py
        purpose: Shared flight marketplace and seat-level booking management
        lines: 390
        subdomain: shared_flights
        endpoints:
        - POST /api/v1/shared-flights/empty-leg/{empty_leg_id} - Create shared flight
        - POST /api/v1/shared-flights/{shared_flight_id}/book - Book seats on shared flight
        - POST /api/v1/bookings/{booking_id}/cancel - Cancel shared flight booking
      - path: app/api/v1/endpoints/bookings/empty_legs.py
        purpose: Empty leg marketplace and booking optimization platform
        lines: 750
        subdomain: empty_legs
        endpoints:
        - GET /api/v1/bookings/empty-legs - List available empty legs
        - POST /api/v1/empty-legs/{booking_id}/optimize - Optimize booking for confirmation
        - POST /api/v1/empty-legs/{booking_id}/request-sharing - Request seat sharing
        - GET /api/v1/empty-legs/shared-from-booking/{booking_id} - Get shared flight details
      - path: app/services/trips_service.py
        purpose: Multi-leg trip planning and itinerary management service
        lines: 1087
        subdomain: trip_planning
        operations:
        - get_trips_for_user - Retrieve user trips with filtering
        - create_trip - Create multi-leg trip with route optimization
        - _booking_to_trip_response - Convert booking to trip format
        - _map_booking_status_to_trip_status - Status mapping for UI
        - _determine_payment_status - Payment status determination
      - path: app/utils/routing.py
        purpose: Advanced route calculation and flight time estimation
        lines: 194
        subdomain: trip_planning
        operations:
        - calculate_distance_nm_by_icao - Great circle distance calculation
        - estimate_flight_time - Multi-factor flight time estimation
        - get_airport_coordinates - Airport coordinate resolution
      - path: app/services/orchestration_service.py
        purpose: AI-powered quote orchestration with intelligent operator coordination
        lines: 457
        subdomain: quotes
        operations:
        - handle_quote_request - End-to-end AI-powered quote request processing
        - confirm_booking - Complete booking confirmation workflow
        - complete_post_flight - Post-flight processing and metrics
      - path: app/services/legacy_services/orchestration.py
        purpose: Legacy booking orchestration service (deprecated)
        lines: 932
        subdomain: quotes
        status: deprecated
      - path: app/services/booking_service.py
        purpose: Primary booking lifecycle management with database manager integration
        lines: 548
        subdomain: booking_lifecycle
        operations:
        - confirm_booking - Booking confirmation with quote selection
        - cancel_booking - Booking cancellation with refund processing
        - get_booking_quotes - Retrieve available quotes for booking
        - update_booking - Booking status and metadata updates
      - path: app/services/payment_service.py
        purpose: Multi-payment method processing with Stripe and Bitcoin support
        lines: 855
        subdomain: booking_lifecycle
        operations:
        - process_stripe_payment - Credit card payment processing
        - generate_btcpay_invoice - Bitcoin payment with 15% discount
        - verify_payment_status - Real-time payment status checking
        - refund_payment - Automated refund processing
      - path: app/services/flight_sharing_service.py
        purpose: Shared flight marketplace and seat-level booking management
        lines: 45
        subdomain: shared_flights
        operations:
        - create_shared_flight - Create shared flights from empty legs
        - book_shared_flight_seats - Individual seat booking with payment processing
        - cancel_seat_booking - Cancel shared flight booking with seat release
      - path: app/services/legacy_services/flight_sharing.py
        purpose: Advanced shared flight service with profitability validation
        lines: 700
        subdomain: shared_flights
        operations:
        - create_shared_flight - Create shared flights with dynamic pricing
        - book_shared_flight_seats - Seat booking with availability validation
        - add_passenger_to_shared_flight - Add passengers with profitability checks
      - path: app/services/empty_leg_processor_service.py
        purpose: Automated empty leg optimization and shared flight conversion
        lines: 224
        subdomain: empty_legs
        operations:
        - process_pending_bookings - Optimize bookings for confirmation
        - optimize_booking_for_confirmation - Multi-strategy booking optimization
        - _can_create_shared_flight - Shared flight eligibility checking
        - _create_shared_flight_for_booking - Convert booking to shared flight
      - path: app/services/legacy_services/empty_leg_processor.py
        purpose: Advanced empty leg processing with marketplace integration
        lines: 900
        subdomain: empty_legs
        operations:
        - optimize_booking_for_confirmation - Multi-strategy optimization
        - process_sharing_requests_from_clients - Client seat sharing requests
        - _create_shared_flight_for_client - Marketplace listing creation
        - _calculate_dynamic_commission - Commission rate optimization
      subdomains:
        trip_planning:
          description: Multi-leg charter journey orchestration and itinerary management
          purpose: Complex trip planning with route optimization, aircraft positioning, and passenger experience coordination
          system_definition: villiers_system_definitions/booking/trip_planning/trip_planning.yaml
          api_files:
          - app/api/v1/endpoints/bookings/trips.py
          service_files:
          - app/services/trips_service.py
          - app/utils/routing.py
          key_features:
          - Multi-leg trip planning with FlightLeg support
          - Advanced route optimization with Haversine algorithms
          - Intelligent aircraft positioning and efficiency optimization
          - Comprehensive trip modification and rebooking capabilities
        quotes:
          description: AI-powered quote request processing and operator coordination
          purpose: Intelligent quote orchestration with automated operator communication and dynamic pricing
          system_definition: villiers_system_definitions/booking/quotes/quotes.yaml
          api_files:
          - app/api/v1/endpoints/bookings/quotes.py
          service_files:
          - app/services/orchestration_service.py
          - app/services/legacy_services/orchestration.py
          key_features:
          - AI-powered natural language quote request processing
          - Automated operator email communication and coordination
          - Dynamic pricing optimization and competitiveness assessment
          - Intelligent quote aggregation and presentation
        booking_lifecycle:
          description: Complete booking lifecycle management with multi-payment support
          purpose: End-to-end booking management from creation through post-flight completion
          system_definition: villiers_system_definitions/booking/booking_lifecycle/booking_lifecycle.yaml
          api_files:
          - app/api/v1/endpoints/bookings/bookings.py
          service_files:
          - app/services/booking_service.py
          - app/services/payment_service.py
          key_features:
          - Complete booking status workflow (SOURCING → PENDING → CONFIRMED → COMPLETED)
          - Multi-payment method support (Stripe, Bitcoin with 15% discount)
          - Real-time flight tracking and passenger notifications
          - Comprehensive refund processing and audit trails
        shared_flights:
          description: Shared flight marketplace and seat-level booking management
          purpose: Revenue optimization through seat sharing and community-driven travel
          system_definition: villiers_system_definitions/booking/shared_flights/shared_flights.yaml
          api_files:
          - app/api/v1/endpoints/bookings/flight_sharing.py
          service_files:
          - app/services/flight_sharing_service.py
          - app/services/legacy_services/flight_sharing.py
          key_features:
          - Empty leg conversion to shared flights with dynamic pricing
          - Individual seat booking with real-time availability management
          - Commission-based revenue sharing for seat providers
          - Advanced profitability validation and confirmation thresholds
        empty_legs:
          description: Empty leg marketplace and booking optimization platform
          purpose: Automated empty leg management with operator solicitation and booking optimization
          system_definition: villiers_system_definitions/booking/empty_legs/empty_legs.yaml
          api_files:
          - app/api/v1/endpoints/bookings/empty_legs.py
          service_files:
          - app/services/empty_leg_processor_service.py
          - app/services/legacy_services/empty_leg_processor.py
          key_features:
          - Automated operator empty leg solicitation campaigns
          - Multi-strategy booking optimization for confirmation
          - Intelligent shared flight conversion from empty legs
          - Client seat sharing requests with marketplace integration
      schemas:
        database_schemas:
        - path: app/db/schemas/booking.py
          purpose: Booking API schemas with validation and relationships
          lines: 51
          schemas:
          - BookingBase/Create/Update - Core booking data transfer objects
          - BookingWithRelations - Booking with full relationship data
          - BookingInList - Simplified booking list format
        service_schemas:
        - path: app/schemas/booking.py
          purpose: Service-layer booking schemas for API responses
          schemas:
          - BookingResponse - Comprehensive booking API response
          - BookingListItem - Booking list item for user interfaces
          - PaymentStatusResponse - Payment status API response
          - RefundResponse - Refund processing response
          - BookingCompletionResponse - Post-flight completion response
      scheduler_tasks:
      - task: Booking Processing
        path: app/tasks/booking_processor.py
        purpose: Automated booking processing and optimization tasks
        lines: 84
        schedule: every 15 minutes
        operations:
        - process_bookings_task - Pending booking confirmation automation
        - process_sharing_requests_from_clients - Shared flight creation
        - process_bitcoin_settlement_incentives - Bitcoin payment incentives
      implementation_status:
        fully_implemented:
          booking_lifecycle:
            description: Complete booking lifecycle from creation to completion
            components:
            - Booking creation with quote confirmation and payment processing
            - Multi-payment support (Stripe credit cards, Bitcoin with 15% discount)
            - Real-time status tracking (SOURCING → PENDING → CONFIRMED → COMPLETED)
            - Flight departure and arrival tracking with notifications
            - Post-flight completion with operator performance metrics
            - Comprehensive refund processing with automated workflows
          orchestration_engine:
            description: Automated booking orchestration with intelligent optimization
            components:
            - Quote competitiveness assessment with dynamic pricing optimization
            - Payment processing integration with multiple providers
            - Contract generation and document management
            - Operator notification and communication workflows
            - Conversion tracking and analytics integration
            - Adaptive user behavior and operator performance updates
          shared_flight_system:
            description: Advanced shared flight and empty leg booking capabilities
            components:
            - Empty leg conversion to shared flights for revenue optimization
            - Individual seat booking on shared flights with dynamic pricing
            - Seat availability management with real-time updates
            - Shared flight confirmation based on minimum seat requirements
            - Commission tracking and revenue sharing calculations
          repository_layer:
            description: Comprehensive data access layer with advanced query capabilities
            components:
            - Booking CRUD operations with relationship management
            - User booking history with pagination and filtering
            - Operator booking statistics and performance metrics
            - Profitability analysis and financial reporting
            - Shared flight and seat management operations
        partially_implemented:
          contract_management:
            description: Contract generation and document workflow
            current_state:
            - Contract service integration in orchestration workflow
            - Contract file path storage in booking metadata
            - Document management structure defined
            missing_components:
            - Complete contract template system
            - Digital signature integration
            - Document version control and audit trail
            implementation_priority: medium
            estimated_effort: 2-3 weeks
          advanced_analytics:
            description: Comprehensive booking analytics and reporting
            current_state:
            - Basic conversion tracking and operator performance metrics
            - Booking status and payment analytics
            - Integration with analytics domain
            missing_components:
            - Advanced booking funnel analysis
            - Predictive booking success modeling
            - Revenue optimization recommendations
            implementation_priority: medium
            estimated_effort: 3-4 weeks
        planned_future:
          mobile_booking_api:
            description: Mobile-optimized booking API endpoints
            scope: Simplified booking workflows for mobile applications
            estimated_effort: 4-6 weeks
          international_payments:
            description: International payment method support
            scope: SEPA, wire transfers, and regional payment processors
            estimated_effort: 6-8 weeks
      behavior:
        booking_creation_workflow:
          quote_selection:
          - User selects preferred quote from available options
          - System validates quote availability and pricing
          - Quote competitiveness assessment with dynamic pricing optimization
          - Platform fee calculation with potential discounts applied
          payment_processing:
          - Multi-payment method support (Stripe, Bitcoin with 15% discount)
          - Real-time payment verification and confirmation
          - Payment failure handling with retry mechanisms
          - Secure payment data storage and PCI compliance
          booking_confirmation:
          - Atomic booking creation with quote validation
          - Booking status transition (SOURCING → PENDING → CONFIRMED)
          - Operator notification and communication workflows
          - Contract generation and document management initiation
          - User confirmation notifications with next steps
        flight_lifecycle_tracking:
          departure_processing:
          - Flight departure confirmation with timestamp recording
          - Passenger notification and tracking activation
          - Real-time flight status updates and communication
          - Emergency contact notification protocols
          arrival_processing:
          - Passenger arrival confirmation at destination
          - Arrival timestamp and location verification
          - Ground transportation coordination notifications
          - Return flight reminders for round-trip bookings
          completion_workflow:
          - Post-flight processing with operator performance metrics
          - Customer feedback collection and rating systems
          - Financial reconciliation and payment finalization
          - Marketing follow-up and future booking incentives
        shared_flight_management:
          empty_leg_conversion:
          - Automated empty leg identification and conversion eligibility
          - Shared flight creation with optimal seat pricing
          - Seat inventory management and availability tracking
          - Minimum seat requirement enforcement for flight confirmation
          seat_booking_process:
          - Individual seat booking with real-time availability
          - Dynamic pricing based on demand and flight proximity
          - Seat selection and passenger detail collection
          - Booking confirmation and flight status updates
        payment_and_refund_processing:
          payment_workflows:
          - Stripe credit card processing with secure tokenization
          - Bitcoin payment processing with 15% automatic discount
          - Payment status tracking and webhook integration
          - Failed payment recovery and retry mechanisms
          refund_processing:
          - Automated refund policy enforcement (24+ hours full refund)
          - Partial refund calculation for time-based policies
          - Multi-payment method refund processing
          - Refund status tracking and customer communication
      endpoints:
        booking_lifecycle:
        - path: /api/v1/bookings/
          methods:
          - POST
          description: Create booking with quote confirmation and payment
          response_time: <2000ms
          handler: bookings.py:create_booking
        - path: /api/v1/bookings/bitcoin
          methods:
          - POST
          description: Create booking with Bitcoin payment (15% discount)
          response_time: <3000ms
          handler: bookings.py:create_bitcoin_booking
        - path: /api/v1/bookings/{booking_id}
          methods:
          - GET
          description: Get comprehensive booking details
          response_time: <500ms
          handler: bookings.py:get_booking
        - path: /api/v1/bookings/user/{user_id}
          methods:
          - GET
          description: Get user booking history with pagination
          response_time: <1000ms
          handler: bookings.py:get_user_bookings
        flight_operations:
        - path: /api/v1/bookings/{booking_id}/depart
          methods:
          - PATCH
          description: Mark flight as departed with tracking activation
          response_time: <200ms
          handler: bookings.py:mark_flight_departed
        - path: /api/v1/bookings/{booking_id}/arrive
          methods:
          - PATCH
          description: Mark passenger arrival at destination
          response_time: <200ms
          handler: bookings.py:mark_passenger_arrival
        - path: /api/v1/bookings/{booking_id}/complete
          methods:
          - POST
          description: Complete post-flight processing and metrics
          response_time: <1000ms
          handler: bookings.py:complete_booking
        payment_management:
        - path: /api/v1/bookings/payment/{invoice_id}/status
          methods:
          - GET
          description: Real-time payment status verification
          response_time: <300ms
          handler: bookings.py:check_payment_status
        - path: /api/v1/bookings/{booking_id}/refund
          methods:
          - POST
          description: Process booking refund with policy enforcement
          response_time: <2000ms
          handler: bookings.py:refund_booking
        shared_flights:
        - path: /api/v1/shared-flights/empty-leg/{empty_leg_id}
          methods:
          - POST
          description: Create shared flight from empty leg
          response_time: <1000ms
          handler: flight_sharing.py:create_shared_flight_from_empty_leg
        - path: /api/v1/bookings/{booking_id}/cancel
          methods:
          - POST
          description: Cancel shared flight booking with seat release
          response_time: <500ms
          handler: flight_sharing.py:cancel_shared_flight_booking
      database_models:
      - model: Booking
        purpose: Primary booking entity with comprehensive payment and status tracking
        fields:
        - id
        - quote_id
        - user_id
        - aircraft_id
        - operator_id
        - trip_id
        - status
        - payment_status
        - platform_fee_amount
        - platform_fee_payment_id
        - platform_fee_payment_method
        - platform_fee_discount
        - operator_payment_amount
        - operator_payment_reference
        - operator_payment_instructions
        - contract_file
        - invoice_file
        - signed_contract_file
        - passenger_details
        - notes
        - booking_metadata
        - completed_at
        indexes:
        - user_id
        - quote_id
        - operator_id
        - status
        - payment_status
        relationships:
        - quote
        - user
        - aircraft
        - operator
        - trip
        - conversion_event
        - aircraft_status
        - notifications
        - payments
        - feedback
        - booking_notes
        - flights
        - conversations
        - messages
        - empty_legs
        - seat_tickets
      - model: BookingNote
        purpose: Booking communication and administrative notes tracking
        fields:
        - id
        - booking_id
        - content
        - note_type
        - is_internal
        - is_operator_visible
        - is_user_visible
        - created_by_id
        - created_by_name
        indexes:
        - booking_id
        - note_type
        - created_by_id
        relationships:
        - booking
        - created_by
      services:
      - service: BookingService
        path: app/services/booking_service.py
        purpose: Primary booking business logic with database manager integration
        methods:
        - confirm_booking
        - cancel_booking
        - get_booking_quotes
        - update_booking
        - get_booking_by_id
        - _booking_to_dict
        dependencies:
        - db_manager
        - notification_service
      - service: OrchestrationService
        path: app/services/orchestration_service.py
        purpose: End-to-end booking lifecycle orchestration
        methods:
        - handle_quote_request
        - confirm_booking
        - complete_post_flight
        dependencies:
        - db_manager
        - ai_engine
        - email_service
        - payment_service
        - contract_service
        - adaptive_service
        - pricing_optimizer
        - conversion_optimizer
      - service: PaymentService
        path: app/services/payment_service.py
        purpose: Multi-payment method processing with security compliance
        methods:
        - process_stripe_payment
        - generate_btcpay_invoice
        - verify_payment_status
        - refund_payment
        security:
        - PCI_compliance
        - cryptocurrency_support
      - service: FlightSharingService
        path: app/services/flight_sharing_service.py
        purpose: Shared flight and empty leg booking management
        methods:
        - create_shared_flight
        - book_seats
        - cancel_seat_booking
        dependencies:
        - db_manager
      - service: EmptyLegProcessorService
        path: app/services/empty_leg_processor_service.py
        purpose: Automated empty leg and shared flight optimization
        methods:
        - _can_create_shared_flight
        - _create_shared_flight_for_booking
        dependencies:
        - db_manager
      repositories:
      - repository: BookingRepository
        path: app/db/manager/repositories/booking_repository.py
        purpose: Comprehensive booking data access with advanced analytics
        methods:
        - get_booking_by_id
        - get_active_bookings_for_user
        - get_user_bookings
        - update_booking_status
        - validate_flight_profitability
        - confirm_booking
        - get_operator_booking_stats
        - get_pending_bookings_for_processing
        validation:
        - booking_ownership
        - status_transitions
        - payment_validation
      - repository: OrchestrationRepository
        path: app/db/manager/repositories/orchestration_repository.py
        purpose: Orchestration workflow data access and atomic operations
        methods:
        - create_booking
        - complete_booking
        - update_operator_reliability
        validation:
        - quote_ownership
        - atomic_transactions
      - repository: SharedFlightRepository
        path: app/db/manager/repositories/shared_flight_repository.py
        purpose: Shared flight and seat booking data management
        methods:
        - create_shared_flight_from_empty_leg
        - book_shared_flight_seats
        validation:
        - seat_availability
        - flight_timing
        - pricing_validation
      scheduler_integration:
        booking_automation:
        - task: Booking Processing
          schedule: every 15 minutes
          purpose: Process pending bookings and confirm profitable flights
          handler: booking_processor.py:process_bookings_task
        - task: Sharing Request Processing
          schedule: every 15 minutes
          purpose: Process client sharing requests and create shared flights
          handler: booking_processor.py:process_sharing_requests_from_clients
        - task: Bitcoin Settlement Incentives
          schedule: daily 3:00 AM
          purpose: Process Bitcoin payment incentives for operators
          handler: booking_processor.py:process_bitcoin_settlement_incentives
      invariants:
      - Booking status transitions must follow defined workflow (SOURCING → PENDING → CONFIRMED → COMPLETED)
      - Payment processing must be completed before booking confirmation
      - Quote ownership must be validated before booking creation
      - Refund amounts cannot exceed original payment amounts
      - Shared flight seat bookings cannot exceed aircraft capacity
      - Empty leg conversions must maintain flight timing and operator constraints
      - Booking metadata must maintain audit trail for all status changes
      - Payment sensitive data must be encrypted and PCI compliant
      - Operator notifications must be sent for all booking confirmations
      - Post-flight completion must update operator performance metrics
      forbidden_states:
      - Bookings confirmed without valid payment processing
      - Quote selection without user ownership validation
      - Refunds processed without proper authorization
      - Shared flight creation exceeding aircraft seat capacity
      - Payment data transmitted without encryption
      - Booking status changes without audit trail logging
      - Operator payments processed without booking confirmation
      - Flight tracking without proper booking status validation
      - Contract generation without booking confirmation
      - Post-flight completion without arrival confirmation
      depends_on:
      - authentication - User authentication and authorization
      - aircraft - Aircraft data and availability management
      - airport - Airport data and routing information
      - analytics - Booking metrics and performance tracking
      provides:
      - booking_lifecycle_management - Complete booking workflow orchestration
      - multi_payment_processing - Stripe and Bitcoin payment integration
      - shared_flight_capabilities - Empty leg and seat sharing functionality
      - booking_analytics_data - Performance metrics and conversion tracking
      - operator_performance_metrics - Booking-based operator reliability scoring
      - customer_booking_history - User booking management and tracking
      enforcement_hooks:
      - validate_booking_creation_requirements
      - verify_payment_processing_completion
      - ensure_operator_notification_delivery
      - confirm_audit_trail_completeness
      - validate_shared_flight_seat_availability
      error_handling:
        booking_creation_errors:
        - Quote not found or expired - Return 404 with quote refresh instructions
        - Payment processing failure - Return 400 with payment retry options
        - Insufficient aircraft availability - Return 409 with alternative suggestions
        - User authorization failure - Return 403 with authentication requirements
        payment_processing_errors:
        - Stripe payment declined - Provide alternative payment methods
        - Bitcoin payment timeout - Extend payment window with notification
        - Refund processing failure - Queue for manual review and processing
        - Payment verification timeout - Implement retry with exponential backoff
        operational_errors:
        - Flight departure tracking failure - Continue with manual operator notification
        - Operator notification delivery failure - Queue for retry with escalation
        - Contract generation failure - Allow manual contract upload workflow
        - Analytics tracking failure - Log error and continue booking processing
      monitoring:
        booking_metrics:
        - 'Booking creation success rate (target: >99%)'
        - 'Payment processing completion rate (target: >98%)'
        - 'Average booking confirmation time (target: <30 seconds)'
        - 'Refund processing time (target: <24 hours)'
        - 'Shared flight conversion rate (target: >15%)'
        performance_monitoring:
        - API endpoint response times with P95 tracking
        - Database query performance for booking operations
        - Payment processor integration latency monitoring
        - Operator notification delivery success rates
        business_metrics:
        - Booking conversion rates from quote requests
        - Revenue per booking with payment method breakdown
        - Customer satisfaction scores from post-flight feedback
        - Operator performance metrics and reliability scores
      security:
        payment_security:
        - PCI DSS compliance for credit card processing
        - Cryptocurrency transaction security and validation
        - Payment data encryption at rest and in transit
        - Secure payment tokenization and storage
        booking_security:
        - User authorization validation for all booking operations
        - Booking ownership verification for modifications
        - Operator data access control and permissions
        - Audit trail logging for all booking status changes
        api_security:
        - Rate limiting on booking creation endpoints
        - Input validation and sanitization for all booking data
        - Authentication required for all booking operations
        - Authorization checks for administrative functions
      performance:
        response_time_targets:
        - 'Booking creation: <2000ms (95th percentile)'
        - 'Booking retrieval: <500ms (95th percentile)'
        - 'Payment status check: <300ms (95th percentile)'
        - 'Status updates: <200ms (95th percentile)'
        throughput_targets:
        - Support 1000+ concurrent booking operations
        - Handle 10000+ booking status queries per minute
        - Process 500+ payment transactions per hour
        - Manage 100+ shared flight bookings simultaneously
        availability_targets:
        - 99.9% uptime for booking API endpoints
        - 99.95% uptime for payment processing systems
        - Real-time booking status synchronization
        - Automated failover for critical booking operations
      testing:
        integration_testing:
        - End-to-end booking workflow validation
        - Multi-payment method processing verification
        - Shared flight booking and cancellation testing
        - Operator notification and communication testing
        performance_testing:
        - Load testing for concurrent booking operations
        - Payment processing performance under load
        - Database performance with large booking datasets
        - API response time validation under stress
        security_testing:
        - Payment security and PCI compliance validation
        - User authorization and access control testing
        - Data encryption and secure transmission verification
        - Audit trail completeness and integrity testing
      consolidation_notes:
        booking_ownership:
        - Booking domain owns complete lifecycle from quote selection to completion
        - Integration with authentication for user validation and authorization
        - Coordination with aircraft domain for availability and specifications
        - Integration with analytics domain for performance tracking and metrics
        service_integration:
        - Primary booking service with database manager pattern
        - Orchestration service for complex workflow coordination
        - Payment service integration for multi-method processing
        - Shared flight service for revenue optimization capabilities
      files:
      - app/api/v1/endpoints/bookings/trips.py - Multi-leg trip planning API
      - app/api/v1/endpoints/bookings/quotes.py - Quote orchestration API
      - app/api/v1/endpoints/bookings/bookings.py - Core booking lifecycle API
      - app/api/v1/endpoints/bookings/flight_sharing.py - Shared flight marketplace API
      - app/api/v1/endpoints/bookings/empty_legs.py - Empty leg optimization API
      - app/services/trips_service.py - Trip planning and itinerary management
      - app/services/orchestration_service.py - Quote orchestration and workflow
      - app/services/booking_service.py - Core booking lifecycle management
      - app/services/payment_service.py - Multi-payment processing
      - app/services/flight_sharing_service.py - Shared flight management
      - app/services/empty_leg_processor_service.py - Empty leg optimization
      - app/services/legacy_services/orchestration.py - Legacy orchestration (deprecated)
      - app/services/legacy_services/flight_sharing.py - Advanced shared flight service
      - app/services/legacy_services/empty_leg_processor.py - Advanced empty leg processing
      - app/utils/routing.py - Route calculation and flight time estimation
      - app/db/models/booking.py - Booking and BookingNote models
      - app/db/schemas/booking.py - Database booking schemas
      - app/schemas/booking.py - Service booking schemas
      - app/db/manager/repositories/booking_repository.py - Booking data access
      - app/db/manager/repositories/orchestration_repository.py - Orchestration data access
      - app/db/manager/repositories/shared_flight_repository.py - Shared flight data access
      - app/tasks/booking_processor.py - Automated booking processing and optimization
      domain: booking
      subdomain: null
      path: booking/booking.yaml
      file_size_kb: 36.01
    trip_planning_trip_planning:
      system: trip_planning
      description: Comprehensive trip planning subdomain for multi-leg charter aviation journey orchestration, route optimization,
        and itinerary management within the villiers.ai booking ecosystem
      intent_assertions:
      - Orchestrate complex multi-leg charter journeys with seamless passenger experiences across multiple destinations
      - Optimize flight routing and aircraft positioning for maximum efficiency and cost-effectiveness
      - Manage comprehensive trip itineraries with real-time coordination and passenger communication
      - Enable dynamic trip modifications and rebooking with minimal disruption to travel plans
      - Provide intelligent route alternatives and empty leg integration for cost optimization
      - Ensure synchronized multi-leg timing with buffer management and contingency planning
      technical_assertions:
        api_endpoints:
        - path: /api/v1/bookings/trips/
          file: app/api/v1/endpoints/bookings/trips.py
          methods:
          - GET
          - POST
          - PUT
          - DELETE
          lines: 454
          description: Complete trip lifecycle management API with multi-leg journey support
        - path: /api/v1/bookings/trips/{trip_id}
          file: app/api/v1/endpoints/bookings/trips.py
          methods:
          - GET
          - PUT
          - DELETE
          description: Individual trip operations including detailed itinerary management
        - path: /api/v1/bookings/trips/user/{user_id}
          file: app/api/v1/endpoints/bookings/trips.py
          methods:
          - GET
          description: User trip history and status filtering with pagination support
        database_models:
        - model: Trip
          file: app/db/models/trips.py
          lines: 59
          description: Core trip entity with multi-leg support, airport relationships, and journey coordination
          key_fields:
          - origin_airport_id
          - destination_airport_id
          - departure_date
          - return_date
          - trip_type
          - requirements
        - model: TripStatusEnum
          file: app/db/models/enums.py
          lines: 354
          description: Comprehensive trip lifecycle status management
          values:
          - DRAFT
          - PENDING
          - CONFIRMED
          - SCHEDULED
          - IN_PROGRESS
          - COMPLETED
          - CANCELLED
          - DELAYED
        schemas:
        - schema: FlightLeg
          file: app/schemas/trip.py
          lines: 44
          description: Multi-leg flight segment definition with tracking and timing coordination
        - schema: TripCreate
          file: app/schemas/trip.py
          lines: 136
          description: Trip creation schema with multi-leg journey support and passenger coordination
        - schema: TripUpdate
          file: app/schemas/trip.py
          lines: 150
          description: Trip modification schema supporting itinerary changes and rebooking
        - schema: TripResponse
          file: app/schemas/trip.py
          lines: 186
          description: Comprehensive trip response with complete journey details and status tracking
        services:
        - service: TripsService
          file: app/services/trips_service.py
          lines: 1087
          description: Core trip planning service with multi-leg journey orchestration and itinerary management
          key_methods:
          - get_trips_for_user
          - create_trip
          - _booking_to_trip_response
          - _convert_trip_to_ui_response
        - service: FlightRouteEstimator
          file: app/utils/routing.py
          lines: 194
          description: Advanced route calculation and flight time estimation for journey optimization
          key_methods:
          - calculate_distance_nm_by_icao
          - estimate_flight_time
          - calculate_distance_nm
        route_optimization:
        - utility: FlightRouteEstimator
          file: app/utils/routing.py
          capabilities:
          - Great circle distance calculation
          - Flight time estimation
          - Cruise speed optimization
          algorithms:
          - Haversine formula
          - Multi-factor time estimation
          - Speed-based route calculation
        - integration: PricingService route optimization
          file: app/services/pricing_service.py
          lines: 1335
          description: Multi-leg itinerary cost calculation with route factor optimization
        templates:
        - template: passenger/itinerary.html
          file: app/templates/passenger/itinerary.html
          lines: 527
          description: Comprehensive itinerary presentation with multi-leg flight details
        - template: passenger/flight_update.html
          file: app/templates/passenger/flight_update.html
          lines: 400
          description: Trip modification and itinerary change notification template
      implementation_status:
        fully_implemented:
          basic_trip_management:
            description: Core trip planning functionality with multi-leg journey support
            components:
            - Trip creation with origin/destination airport selection and date management
            - User trip history with status filtering and pagination support
            - Trip modification capabilities with itinerary updates and rebooking
            - Trip-to-booking conversion with quote integration and payment processing
            - Multi-leg flight segment definition with FlightLeg schema support
            - Comprehensive trip response formatting with journey details
          route_calculation:
            description: Advanced route optimization and flight time estimation
            components:
            - Great circle distance calculation using Haversine formula for accurate routing
            - Flight time estimation with multi-factor analysis including cruise speeds
            - Airport coordinate resolution and ICAO code validation
            - Route optimization considering aircraft positioning and operational costs
            - Distance calculation supporting both nautical miles and kilometers
          ui_integration:
            description: Comprehensive user interface integration for trip planning
            components:
            - Detailed itinerary presentation with multi-leg flight information display
            - Trip modification notification system with passenger communication
            - Status mapping between booking and trip systems for consistent UI
            - Payment status determination and display for trip transactions
            - Trip response conversion for optimized UI consumption
        partially_implemented:
          advanced_optimization:
            description: Advanced multi-leg route optimization with AI-driven insights
            current_state:
            - Basic route calculation with distance and time estimation
            - Integration with pricing service for multi-leg cost calculations
            - Aircraft positioning awareness for route efficiency
            missing_components:
            - Machine learning-based route optimization algorithms
            - Dynamic weather-based routing adjustments and contingency planning
            - Real-time aircraft repositioning for optimal trip efficiency
            - Intelligent layover time management with buffer optimization
            implementation_priority: high
            estimated_effort: 4-6 weeks
          operator_coordination:
            description: Multi-operator coordination for complex journey execution
            current_state:
            - Basic operator communication through existing booking workflows
            - Trip status tracking with booking system integration
            - Operator notification for trip-related bookings
            missing_components:
            - Real-time multi-operator coordination platform
            - Automated operator scheduling for multi-leg journeys
            - Operator performance tracking specific to trip coordination
            - Conflict resolution system for operator scheduling conflicts
            implementation_priority: medium
            estimated_effort: 3-4 weeks
          analytics_reporting:
            description: Comprehensive trip planning analytics and performance insights
            current_state:
            - Basic trip creation and completion tracking
            - Integration with existing booking analytics system
            - Trip status progression monitoring
            missing_components:
            - Multi-leg trip performance analytics with optimization insights
            - Route efficiency metrics and cost-benefit analysis reporting
            - Passenger experience tracking across journey segments
            - Operator coordination effectiveness and timing accuracy metrics
            implementation_priority: medium
            estimated_effort: 2-3 weeks
        planned_future:
          ai_driven_optimization:
            description: Machine learning-powered trip planning and optimization
            scope: Predictive route optimization, demand forecasting, and intelligent rebooking
            estimated_effort: 8-12 weeks
          integrated_travel_services:
            description: Complete travel ecosystem integration
            scope: Ground transportation, accommodation, and concierge service coordination
            estimated_effort: 6-10 weeks
          carbon_optimization:
            description: Environmental impact optimization for charter journeys
            scope: Carbon footprint calculation, offset integration, and eco-friendly routing
            estimated_effort: 4-6 weeks
      implementation_gaps:
        critical_gaps:
        - gap: Real-time multi-operator coordination platform
          impact: Limited ability to coordinate complex multi-leg journeys across operators
          priority: high
          estimated_effort: 4 weeks
        - gap: Advanced route optimization algorithms
          impact: Suboptimal routing leading to higher costs and longer journey times
          priority: high
          estimated_effort: 6 weeks
        - gap: Intelligent layover management system
          impact: Manual buffer time management leading to passenger inconvenience
          priority: medium
          estimated_effort: 3 weeks
        technical_debt:
        - debt: Trip service method consolidation
          description: Multiple similar methods for trip response conversion need refactoring
          priority: low
          estimated_effort: 1 week
        - debt: Route calculation caching
          description: Frequent route calculations could benefit from intelligent caching
          priority: medium
          estimated_effort: 2 weeks
      behavior:
        trip_planning_lifecycle:
          1: Multi-destination request processing and initial route analysis
          2: Route optimization with aircraft positioning and operator coordination
          3: Itinerary assembly with timing coordination and buffer management
          4: Quote generation for optimized multi-leg journey with cost breakdown
          5: Trip confirmation with operator coordination and passenger communication
          6: Real-time journey monitoring with modification capability
          7: Post-trip completion with performance analysis and experience tracking
        journey_orchestration:
        - Seamless multi-leg coordination with timing optimization
        - Dynamic route adjustments based on aircraft availability and weather
        - Integrated empty leg opportunities for cost optimization
        - Passenger communication throughout journey with proactive updates
        - Operator coordination for complex multi-leg trip execution
        modification_handling:
        - Dynamic itinerary changes with minimal passenger disruption
        - Alternative route calculation for unexpected modifications
        - Real-time rebooking with operator coordination and cost impact analysis
        - Change history tracking with audit trail and communication log
      invariants:
      - Multi-leg trips must maintain logical timing sequence with adequate transfer buffers
      - Route optimization must consider aircraft positioning costs and efficiency
      - Trip modifications must preserve passenger preferences and minimize cost impact
      - Journey coordination must ensure operator availability for all flight segments
      - Itinerary changes must maintain audit trail for booking integrity
      - Multi-destination pricing must reflect route optimization and positioning costs
      - Trip status transitions must follow logical progression with proper validation
      - Passenger communication must be synchronized across all journey segments
      forbidden_states:
      - Multi-leg trips with overlapping or conflicting flight times
      - Route optimization that creates impossible aircraft positioning scenarios
      - Trip modifications without proper operator confirmation and availability
      - Itinerary changes without passenger notification and consent
      - Multi-leg journeys without adequate transfer time buffers
      - Trip creation without validated airport and route feasibility
      - Journey execution without proper coordination between multiple operators
      - Trip completion without performance tracking and experience validation
      primary_flows:
        multi_leg_trip_creation:
        - trigger: Customer multi-destination journey request
        - steps:
          - Route analysis
          - Optimization calculation
          - Itinerary assembly
          - Quote generation
          - Confirmation
        - result: Optimized multi-leg trip with coordinated itinerary
        journey_modification:
        - trigger: Trip change request or operational necessity
        - steps:
          - Impact analysis
          - Alternative calculation
          - Operator coordination
          - Passenger communication
          - Rebooking execution
        - result: Updated trip with minimal disruption and optimized routing
        real_time_coordination:
        - trigger: Journey in progress monitoring
        - steps:
          - Status tracking
          - Timing coordination
          - Proactive communication
          - Contingency activation
        - result: Seamless journey execution with real-time optimization
      core_principles:
      - Journey efficiency through intelligent routing and aircraft positioning optimization
      - Passenger experience excellence with seamless multi-leg coordination and communication
      - Route optimization that balances cost-effectiveness with travel time and convenience
      - Coordination excellence ensuring smooth transitions between journey segments
      - Dynamic adaptability enabling real-time trip modifications and rebooking
      - Performance optimization using data-driven insights for continuous improvement
      depends_on:
      - aircraft_positioning - Real-time aircraft location and availability tracking
      - operator_communication - Multi-operator coordination for complex journeys
      - quote_generation - Multi-leg pricing calculation and optimization
      - booking_lifecycle - Trip conversion to confirmed bookings with payment processing
      - airport_data - Airport information and operational constraints for route planning
      - pricing_optimization - Dynamic pricing for multi-leg journeys with route factors
      provides:
      - multi_leg_journey_orchestration - Complete journey planning from origin to final destination
      - route_optimization_platform - Intelligent routing with cost and efficiency optimization
      - itinerary_management_system - Comprehensive trip coordination and modification capabilities
      - journey_performance_analytics - Trip efficiency metrics and optimization insights
      - passenger_experience_coordination - Seamless communication and journey management
      - trip_planning_intelligence - AI-driven route optimization and journey recommendations
      integration_points:
        booking_system:
        - Trip to booking conversion with multi-leg coordination
        - Quote integration for complex journey pricing
        - Payment processing for multi-segment trips
        aircraft_management:
        - Aircraft positioning optimization for route efficiency
        - Fleet availability coordination for multi-leg journeys
        - Empty leg integration for cost optimization
        communication_platform:
        - Passenger itinerary delivery and journey updates
        - Operator coordination for multi-leg trip execution
        - Real-time modification notifications and confirmations
      scheduler_integration:
        trip_optimization:
        - task: Trip Route Optimization
          schedule: every 30 minutes
          purpose: Optimize existing trip routes based on aircraft positioning and weather updates
          handler: trip_optimizer.py:optimize_trip_routes
          optimization: Continuous route improvement with positioning cost analysis
        - task: Journey Coordination Processing
          schedule: every 15 minutes
          purpose: Coordinate multi-leg journey timing and operator communication
          handler: journey_coordinator.py:process_journey_coordination
          coordination: Real-time journey synchronization and timing optimization
        - task: Trip Modification Processing
          schedule: every 10 minutes
          purpose: Process pending trip modifications and rebooking requests
          handler: trip_modifier.py:process_trip_modifications
          modifications: Dynamic trip changes with minimal passenger disruption
      monitoring:
        trip_planning_metrics:
        - metric: multi_leg_coordination_success_rate
          description: Percentage of successful multi-leg trip coordination without timing conflicts
          target: '>99.2%'
        - metric: route_optimization_efficiency
          description: Average cost savings achieved through intelligent route optimization
          target: '>15%'
        - metric: trip_modification_speed
          description: Average time to process and confirm trip modifications
          target: <30 minutes
        - metric: passenger_journey_satisfaction
          description: Passenger satisfaction score for multi-leg journey coordination
          target: '>4.7/5'
      security:
      - Trip planning data protection with passenger privacy and journey confidentiality
      - Itinerary access control ensuring only authorized users can view journey details
      - Route optimization security preventing competitive intelligence exposure
      - Journey coordination security with encrypted operator communications
      - Trip modification authorization ensuring only valid users can change bookings
      - Performance data anonymization for analytics while protecting passenger identity
      error_handling:
        trip_planning_errors:
        - error_type: InvalidRouteConfiguration
          description: Route optimization failed due to impossible aircraft positioning
          response_code: 400
          recovery: Provide alternative route suggestions with feasible positioning
        - error_type: MultiLegTimingConflict
          description: Multi-leg journey has timing conflicts or insufficient transfer time
          response_code: 400
          recovery: Recalculate timing with adequate buffers and operator coordination
        - error_type: JourneyModificationFailure
          description: Trip modification could not be processed due to operator constraints
          response_code: 422
          recovery: Provide alternative modification options with impact analysis
      enforcement_hooks:
      - validate_multi_leg_timing_consistency
      - ensure_route_optimization_feasibility
      - confirm_operator_coordination_capacity
      - verify_trip_modification_authorization
      - validate_journey_performance_tracking
      endpoints:
        trip_management:
        - path: /api/v1/bookings/trips/
          methods:
          - GET
          - POST
          description: Create trip and list user trips with filtering
          response_time: <1000ms
          handler: trips.py:create_trip, get_user_trips
        - path: /api/v1/bookings/trips/{trip_id}
          methods:
          - GET
          - PUT
          - DELETE
          description: Individual trip operations with detailed itinerary management
          response_time: <500ms
          handler: trips.py:get_trip, update_trip, delete_trip
        - path: /api/v1/bookings/trips/user/{user_id}
          methods:
          - GET
          description: User trip history with status filtering and pagination
          response_time: <800ms
          handler: trips.py:get_user_trips_by_id
      database_models:
      - model: Trip
        purpose: Core trip entity with multi-leg journey support and airport relationships
        fields:
        - id
        - user_id
        - origin_airport_id
        - destination_airport_id
        - departure_date
        - return_date
        - trip_type
        - requirements
        - status
        - created_at
        - updated_at
        indexes:
        - user_id
        - status
        - departure_date
        - origin_airport_id
        - destination_airport_id
        relationships:
        - user
        - origin_airport
        - destination_airport
        - bookings
        - quotes
      services:
      - service: TripsService
        path: app/services/trips_service.py
        purpose: Core trip planning service with multi-leg journey orchestration
        methods:
        - get_trips_for_user
        - create_trip
        - _booking_to_trip_response
        - _convert_trip_to_ui_response
        - _map_booking_status_to_trip_status
        - _determine_payment_status
        dependencies:
        - db_manager
        - aircraft_service
        - pricing_service
      - service: FlightRouteEstimator
        path: app/utils/routing.py
        purpose: Advanced route calculation and flight time estimation
        methods:
        - calculate_distance_nm_by_icao
        - estimate_flight_time
        - calculate_distance_nm
        - get_airport_coordinates
        dependencies:
        - airport_data
      repositories:
      - repository: TripRepository
        path: app/db/manager/repositories/trip_repository.py
        purpose: Trip data access layer with multi-leg journey support
        methods:
        - get_trip_by_id
        - get_user_trips
        - create_trip
        - update_trip
        - delete_trip
        - get_trips_by_status
        validation:
        - trip_ownership
        - journey_timing
        - route_feasibility
      performance:
        response_time_targets:
        - 'Trip creation: <1000ms (95th percentile)'
        - 'Trip retrieval: <500ms (95th percentile)'
        - 'Route optimization: <2000ms (95th percentile)'
        - 'Trip modification: <800ms (95th percentile)'
        throughput_targets:
        - Support 500+ concurrent trip planning operations
        - Handle 2000+ trip status queries per minute
        - Process 100+ route optimizations per hour
        - Manage 50+ simultaneous multi-leg journey modifications
        availability_targets:
        - 99.8% uptime for trip planning API endpoints
        - 99.9% uptime for route optimization services
        - Real-time trip status synchronization
        - Automated failover for critical trip operations
      testing:
        integration_testing:
        - End-to-end multi-leg trip planning workflow validation
        - Route optimization algorithm accuracy testing
        - Trip modification and rebooking process verification
        - Operator coordination and communication testing
        performance_testing:
        - Load testing for concurrent trip planning operations
        - Route optimization performance under complex scenarios
        - Database performance with large trip datasets
        - API response time validation for trip operations
        security_testing:
        - Trip data privacy and access control validation
        - Itinerary confidentiality and secure transmission
        - User authorization for trip modifications
        - Journey data integrity and audit trail testing
      consolidation_notes:
        trip_planning_ownership:
        - Trip planning subdomain owns multi-leg journey orchestration and itinerary management
        - Integration with booking domain for trip-to-booking conversion workflows
        - Coordination with aircraft domain for positioning optimization and availability
        - Integration with communication domain for passenger and operator coordination
        service_integration:
        - Primary trips service with database manager pattern for data access
        - Route optimization utility for intelligent journey planning
        - Integration with pricing service for multi-leg cost calculations
        - Coordination with aircraft positioning for route efficiency optimization
      files:
      - app/api/v1/endpoints/bookings/trips.py - Primary trip planning API endpoints
      - app/services/trips_service.py - Core trip planning business logic
      - app/utils/routing.py - Route optimization and flight time estimation
      - app/db/models/trips.py - Trip data models
      - app/schemas/trip.py - Trip API schemas and validation
      - app/templates/passenger/itinerary.html - Itinerary presentation template
      - app/templates/passenger/flight_update.html - Trip modification notifications
      domain: booking
      subdomain: trip_planning_trip_planning
      path: booking/trip_planning/trip_planning.yaml
      file_size_kb: 23.47
    quotes_quotes:
      system: villiers_quotes
      description: Villiers.ai Quotes Subdomain - World-Class Charter Aviation Price Estimation with AI-Powered Quote Generation,
        Multi-Factor Pricing Engines, and Conversion Optimization. This subdomain owns all quote creation, pricing calculation,
        delivery, lifecycle management, and booking conversion workflows for private jet charter opportunities.
      intent_assertions:
      - Complete quote lifecycle management from flight request capture to booking conversion
      - AI-powered quote generation with natural language processing and intent recognition
      - Multi-factor pricing engines with aircraft, operator, route, and seasonal optimization
      - Real-time quote calculation with distance, flight time, and cost factor analysis
      - Intelligent quote ranking and conversion optimization based on historical performance
      - Automated quote expiration management with configurable validity periods
      - Comprehensive quote validation and admin approval workflows
      - Seamless quote-to-booking conversion with payment processing integration
      technical_assertions:
      - path: app/db/models/quote.py
        purpose: Complete quote data models with pricing, validation, and operator integration
        lines: 196
        models:
        - Quote
        - QuoteOperatorData
      - path: app/services/pricing_service.py
        purpose: Core pricing engine with multi-factor cost calculation and optimization
        lines: 900
        methods:
        - calculate_quote_price
        - estimate_cost
        - get_pricing_breakdown
      - path: app/services/price_estimation_service.py
        purpose: Advanced price estimation with tunable adjustment factors and market analysis
        lines: 300
        methods:
        - estimate_price
        - estimate_price_range
        - apply_adjustment_factors
      - path: app/api/v1/endpoints/bookings/quotes.py
        purpose: Quote API endpoints with creation, ranking, and conversion workflows
        lines: 115
        endpoints:
        - /quotes/
        - /quotes/{quote_id}/validate
        - /quotes/{quote_id}/convert
      - path: app/db/manager/repositories/quote_repository.py
        purpose: Quote data access layer with conversion analytics and similarity matching
        lines: 671
        operations:
        - get_quote_by_id
        - get_similar_quotes
        - update_quote_platform_fee
      - path: app/services/orchestration_service.py
        purpose: Quote orchestration with AI parsing and ranking optimization
        lines: 200
        methods:
        - handle_quote_request
        - rank_quotes_by_conversion
      - path: app/services/conversion_service.py
        purpose: Quote conversion optimization with historical performance analysis
        lines: 150
        methods:
        - rank_quotes_by_conversion
        - calculate_conversion_scores
      - path: app/services/ai_service.py
        purpose: AI-powered quote generation with natural language intent parsing
        lines: 400
        methods:
        - generate_quote_estimates
        - parse_flight_intent
      database_models:
      - model: Quote
        purpose: Primary quote entity with comprehensive pricing, validation, and lifecycle data
        fields:
        - id
        - user_id
        - aircraft_id
        - aircraft_type_id
        - operator_id
        - empty_leg_id
        - departure_airport_id
        - arrival_airport_id
        - departure_time
        - arrival_time
        - return_departure_time
        - return_arrival_time
        - status
        - is_request
        - departure_date
        - return_date
        - is_one_way
        - passengers
        - luggage
        - aircraft_type
        - aircraft_category
        - aircraft_preferences
        - price
        - currency
        - price_breakdown
        - price_expiry
        - platform_fee
        - platform_fee_percent
        - is_validated
        - validated_by
        - validated_at
        - notes
        - route_details
        - quote_metadata
        - decline_reason
        - decline_feedback
        indexes:
        - user_id
        - status
        - departure_airport_id
        - arrival_airport_id
        - departure_time
        relationships:
        - user
        - validated_by_user
        - booking
        - operator
        - aircraft
        - aircraft_type_obj
        - conversations
        - messages
        - conversion_events
        - operator_data
        - notifications
        - feedback
        - departure_airport
        - arrival_airport
        - empty_leg
      - model: QuoteOperatorData
        purpose: Operator-specific quote data with pricing, terms, and extraction metadata
        fields:
        - id
        - quote_id
        - operator_id
        - aircraft_id
        - price
        - currency
        - estimated_flight_time
        - notes
        - terms
        - expiration_time
        - is_confirmed
        - source_message_id
        - extracted_at
        - extraction_confidence
        tracking: Email extraction and operator response processing
      services:
      - service: PricingService
        path: app/services/pricing_service.py
        purpose: Core pricing engine with multi-factor cost calculation and breakdown
        methods:
        - calculate_quote_price
        - estimate_cost
        - get_pricing_breakdown
        - _calculate_flight_time
        integration: Aircraft data, operator pricing rules, seasonal multipliers, demand factors
      - service: PriceEstimationService
        path: app/services/price_estimation_service.py
        purpose: Advanced price estimation with tunable adjustment factors and market optimization
        methods:
        - estimate_price
        - estimate_price_range
        - apply_adjustment_factors
        - _get_base_hourly_rate
        automation: Market-based pricing adjustments and historical performance optimization
      - service: OrchestrationService
        path: app/services/orchestration_service.py
        purpose: Quote orchestration with AI parsing, generation, and ranking
        methods:
        - handle_quote_request
        - rank_quotes_by_conversion
        - calculate_flight_metrics
        orchestration: End-to-end quote workflow from request to ranked results
      - service: ConversionService
        path: app/services/conversion_service.py
        purpose: Quote conversion optimization with historical performance analysis
        methods:
        - rank_quotes_by_conversion
        - calculate_conversion_scores
        - track_conversion_events
        analytics: Conversion likelihood prediction and performance tracking
      repositories:
      - repository: QuoteRepository
        purpose: Quote data access with conversion analytics and similarity matching
        methods:
        - get_quote_by_id
        - get_quote_with_aircraft
        - get_quote_with_operator
        - get_similar_quotes
        - update_quote_platform_fee
        - update_quote
        - get_quotes_for_user
        - get_quotes_paginated
        - count_quotes_in_period
        analytics: Historical performance analysis, conversion tracking, similarity matching
      behavior:
        quote_generation:
          ai_powered_parsing:
          - Natural language processing of flight requests with intent recognition
          - Automatic extraction of departure, arrival, dates, passengers, and preferences
          - Aircraft category inference based on route, passenger count, and requirements
          - Flight time and distance calculation using route estimation algorithms
          pricing_calculation:
          - Multi-factor pricing engine with base hourly rates and adjustment factors
          - Route-specific pricing with distance, flight time, and positioning costs
          - Seasonal demand multipliers based on region and time of year
          - Operator-specific pricing rules and negotiated rate structures
          - Empty leg integration for discounted pricing opportunities
          quote_ranking:
          - Conversion likelihood scoring based on historical performance data
          - Price competitiveness analysis with market positioning
          - Operator reliability and performance metrics integration
          - Customer preference matching with aircraft and service requirements
        quote_lifecycle:
          creation_workflow:
          - AI-powered quote generation from natural language flight requests
          - Multiple quote creation with aircraft category diversity and pricing options
          - Automatic flight metrics calculation (distance, time, routing)
          - Platform fee calculation with configurable percentage and discount rules
          validation_process:
          - Admin validation workflow for quote accuracy and availability verification
          - Operator confirmation tracking with response monitoring
          - Aircraft availability validation with scheduling conflict detection
          - Price verification against market rates and operator agreements
          expiration_management:
          - Configurable quote validity periods with automatic expiration handling
          - Quote status updates and customer notifications for expiring quotes
          - Operator-specific expiration rules based on terms and agreements
          - Grace period handling for near-expired quotes with conversion potential
        conversion_optimization:
          performance_tracking:
          - Quote conversion rate analysis with customer and operator segmentation
          - Historical performance data collection for pricing optimization
          - A/B testing support for quote presentation and pricing strategies
          - Customer behavior analysis for quote acceptance patterns
          ranking_algorithms:
          - Machine learning-based conversion likelihood prediction
          - Historical success rate weighting for quote prioritization
          - Customer preference matching with past booking patterns
          - Real-time market demand integration for dynamic ranking
        operator_integration:
          quote_extraction:
          - Automated quote extraction from operator email responses
          - Natural language processing for pricing, terms, and availability parsing
          - Confidence scoring for extracted quote data accuracy
          - Manual validation workflow for low-confidence extractions
          response_tracking:
          - Operator response time monitoring and performance analytics
          - Quote confirmation workflow with operator approval tracking
          - Terms and conditions parsing with automated compliance checking
          - Operator preference learning for improved quote targeting
      primary_flows:
        quote_request_flow:
          steps:
          - 'Flight Request Capture: Customer submits flight requirements via natural language'
          - 'AI Intent Parsing: Extract flight details, preferences, and requirements'
          - 'Quote Generation: Create multiple quotes with diverse aircraft and pricing options'
          - 'Pricing Calculation: Apply multi-factor pricing with all cost components'
          - 'Quote Ranking: Rank quotes by conversion likelihood and customer fit'
          - 'Quote Delivery: Present ranked quotes to customer with detailed breakdowns'
          automation:
          - End-to-end automation from request to ranked quote delivery
          - Real-time pricing calculation with market-based adjustments
          - Automatic aircraft selection based on route and passenger requirements
        quote_validation_flow:
          steps:
          - 'Quote Review: Admin reviews quote accuracy and operator availability'
          - 'Aircraft Verification: Confirm aircraft availability for requested dates'
          - 'Price Validation: Verify pricing against market rates and agreements'
          - 'Operator Confirmation: Track operator response and quote confirmation'
          - 'Customer Notification: Notify customer of validated quote availability'
          quality_assurance:
          - Multi-level validation with automated checks and manual review
          - Operator reliability verification with performance history
          - Price accuracy validation against historical and market data
        conversion_tracking_flow:
          steps:
          - 'Quote Presentation: Track customer quote viewing and engagement'
          - 'Selection Monitoring: Monitor quote selection and comparison behavior'
          - 'Conversion Events: Track quote acceptance and booking conversion'
          - 'Performance Analysis: Analyze conversion rates and optimization opportunities'
          - 'Feedback Integration: Incorporate customer feedback for quote improvement'
          analytics:
          - Real-time conversion tracking with detailed funnel analysis
          - Customer behavior insights for quote optimization
          - Operator performance metrics for partnership optimization
      invariants:
      - Quote pricing must include all cost factors (base price, fees, positioning, fuel)
      - Quote expiration must be enforced with automatic status updates
      - Platform fee calculations must be consistent and auditable
      - Quote validation must be performed by authorized admin users only
      - Conversion tracking must maintain complete audit trail
      - Quote-to-booking conversion must validate quote ownership and validity
      - Operator quote data must include confidence scores for extraction accuracy
      - Similar quote analysis must use consistent route and timing criteria
      - Quote ranking must be based on measurable conversion likelihood factors
      - Price breakdown must account for all cost components transparently
      forbidden_states:
      - Quotes created without proper flight requirement validation
      - Pricing calculations missing mandatory cost factors
      - Quote expiration without customer notification
      - Conversion tracking without proper quote ownership validation
      - Quote validation by unauthorized users
      - Booking conversion from expired or invalid quotes
      - Operator quote extraction without confidence scoring
      - Platform fee application without proper calculation audit
      - Quote ranking without historical performance data
      - Price adjustments without admin authorization
      depends_on:
      - aircraft - Aircraft availability, specifications, and performance data
      - operator - Operator pricing rules, availability, and performance metrics
      - airport - Airport data for route calculation and fee estimation
      - authentication - User authentication and admin authorization
      - communication - Email processing and customer notification services
      provides:
      - quote_generation_engine - AI-powered quote creation and pricing calculation
      - conversion_optimization_platform - Quote ranking and performance analytics
      - pricing_calculation_service - Multi-factor pricing with market optimization
      - operator_integration_system - Quote extraction and response tracking
      - quote_lifecycle_management - Complete quote workflow from creation to conversion
      - customer_quote_experience - Ranked quote delivery with detailed breakdowns
      enforcement_hooks:
      - validate_quote_pricing_accuracy
      - ensure_quote_expiration_compliance
      - confirm_admin_validation_authorization
      - verify_conversion_tracking_completeness
      - validate_operator_quote_confidence_scores
      scheduler_integration:
        quote_automation:
        - task: Quote Expiration Processing
          schedule: every 30 minutes
          purpose: Process expired quotes and update status with customer notifications
          handler: quote_processor.py:process_expired_quotes
          notifications: Customer notifications for expiring and expired quotes
        - task: Conversion Analytics Processing
          schedule: daily 2:00 AM
          purpose: Process quote conversion analytics and update performance metrics
          handler: conversion_service.py:process_conversion_analytics
          analytics: Quote performance analysis and ranking algorithm optimization
        - task: Operator Response Monitoring
          schedule: every 15 minutes
          purpose: Monitor operator email responses and extract quote data
          handler: email_processor.py:process_operator_responses
          extraction: Automated quote extraction from operator communications
      endpoints:
        quote_creation:
        - path: /api/v1/bookings/quotes/
          methods:
          - POST
          description: Create quote request and get ranked quotes from operators
          response_time: <2000ms
          handler: quotes.py:create_quote
          access: User authentication required
        - path: /api/v1/admin/manual-quotes
          methods:
          - POST
          description: Create manual quote with admin privileges
          response_time: <1000ms
          handler: admin.py:create_manual_quote
          access: Admin-only for manual quote creation
        quote_management:
        - path: /api/v1/admin-ui/quotes
          methods:
          - GET
          description: Get paginated list of quotes for admin management
          response_time: <1000ms
          handler: admin_ui.py:get_admin_quotes
          access: Admin-only with filtering and pagination
        - path: /api/v1/admin-ui/quotes/{quote_id}/validate
          methods:
          - POST
          description: Validate quote for accuracy and availability
          response_time: <500ms
          handler: admin_ui.py:validate_quote
          access: Admin-only for quote validation
        quote_analytics:
        - path: /api/v1/quotes/{quote_id}/conversion-score
          methods:
          - GET
          description: Get conversion likelihood score for quote
          response_time: <300ms
          handler: quotes.py:get_conversion_score
          access: Admin access for conversion analytics
      error_handling:
        quote_errors:
        - error_type: QuoteExpiredError
          description: Quote has expired and is no longer valid for booking
          response_code: 410
          recovery: Generate new quote or extend existing quote validity
        - error_type: InvalidPricingParametersError
          description: Quote pricing calculation failed due to invalid parameters
          response_code: 400
          recovery: Validate flight parameters and retry pricing calculation
        - error_type: QuoteValidationError
          description: Quote validation failed due to availability or accuracy issues
          response_code: 422
          recovery: Review quote details and operator availability
        - error_type: ConversionTrackingError
          description: Quote conversion tracking failed
          response_code: 500
          recovery: Retry conversion tracking with manual fallback
      monitoring:
        quote_metrics:
        - metric: quote_conversion_rate
          description: Percentage of quotes converted to bookings
          target: '>15%'
        - metric: average_quote_response_time
          description: Average time from request to quote delivery
          target: <30 seconds
        - metric: quote_pricing_accuracy
          description: Accuracy of quote pricing vs final booking price
          target: '>90%'
        - metric: operator_quote_confirmation_rate
          description: Percentage of quotes confirmed by operators
          target: '>70%'
        - metric: quote_expiration_rate
          description: Percentage of quotes expiring without conversion
          target: <60%
      testing:
        quote_testing:
        - test_type: Unit Tests
          coverage: Quote pricing calculation, validation, and conversion logic
          location: tests/unit/services/test_pricing_service.py
        - test_type: Integration Tests
          coverage: Complete quote workflow from creation to conversion
          location: tests/integration/endpoints/bookings/test_quotes_endpoints.py
        - test_type: API Tests
          coverage: Quote API endpoints and response validation
          location: tests/integration/endpoints/admin/test_admin_quotes.py
      security:
        quote_security:
        - Quote validation requires admin authorization with audit trail
        - Customer quote access restricted to quote owner and admin users
        - Pricing calculations include audit trail for all cost factors
        - Operator quote data includes confidence scoring for accuracy verification
        - Quote conversion tracking maintains complete customer privacy
      performance:
        quote_performance_targets:
        - 'Quote generation: <2000ms from request to ranked results'
        - 'Pricing calculation: <500ms per quote with full breakdown'
        - 'Quote validation: <500ms per quote review'
        - 'Conversion analytics: <300ms per score calculation'
        - 'Quote search and filtering: <1000ms for paginated results'
      consolidation_notes:
        quote_ownership:
        - Complete quote lifecycle management consolidated in OrchestrationService
        - Pricing calculations centralized in PricingService with multiple engines
        - Conversion optimization unified in ConversionService
        - Quote validation standardized across admin interfaces
        - Operator integration consolidated in email processing workflows
      implementation_gaps:
        real_time_pricing_updates:
          status: enhancement_opportunity
          description: Real-time pricing updates based on market demand and availability
          current_state: Static pricing with periodic recalculation
          enhancement: Dynamic pricing with real-time market data integration
          priority: medium
          estimated_effort: 4-5 weeks
        advanced_conversion_prediction:
          status: enhancement_opportunity
          description: Machine learning-based conversion prediction with customer behavior analysis
          current_state: Rule-based conversion scoring with historical data
          enhancement: ML-powered prediction with behavioral pattern recognition
          priority: low
          estimated_effort: 6-8 weeks
        automated_quote_negotiation:
          status: planned_future
          description: Automated quote negotiation with operators for better pricing
          current_state: Manual operator communication and negotiation
          enhancement: AI-powered negotiation with operator preference learning
          priority: low
          estimated_effort: 8-10 weeks
      files:
      - app/db/models/quote.py - Complete quote data models and operator integration
      - app/services/pricing_service.py - Core pricing engine with multi-factor calculation
      - app/services/price_estimation_service.py - Advanced price estimation with market optimization
      - app/api/v1/endpoints/bookings/quotes.py - Quote API endpoints and workflows
      - app/db/manager/repositories/quote_repository.py - Quote data access and analytics
      - app/services/orchestration_service.py - Quote orchestration and AI integration
      - app/services/conversion_service.py - Quote conversion optimization and tracking
      - app/services/ai_service.py - AI-powered quote generation and intent parsing
      - app/db/schemas/quote.py - Quote database schemas and validation
      - app/schemas/quotes.py - Quote API response schemas
      - app/api/v1/endpoints/admin/admin_ui.py - Admin quote management interface
      - tests/integration/endpoints/bookings/test_quotes_endpoints.py - Quote integration tests
      domain: booking
      subdomain: quotes_quotes
      path: booking/quotes/quotes.yaml
      file_size_kb: 21.41
    empty_legs_empty_legs:
      system: villiers_empty_legs
      description: Villiers.ai Empty Legs Subdomain - World-Class Aircraft Repositioning Flight Opportunities with Automated
        Discovery, Dynamic Pricing, and Marketplace Integration. This subdomain owns all empty leg identification, publication,
        booking conversion, and revenue optimization workflows for discounted private jet charter opportunities.
      intent_assertions:
      - Complete empty legs lifecycle management from aircraft repositioning identification to booking conversion
      - Automated empty leg discovery from aircraft movements and scheduling gaps
      - Dynamic pricing optimization with 40-60% discounts from regular charter rates
      - Seamless empty leg to shared flight conversion for revenue maximization
      - Intelligent empty leg matching with customer route preferences and timing flexibility
      - Operator solicitation automation for proactive empty leg inventory management
      - Real-time empty leg availability tracking with expiration management
      - Integration with booking domain for seamless conversion to confirmed flights
      technical_assertions:
      - path: app/db/models/empty_leg.py
        purpose: Complete empty leg data models with positioning and pricing information
        lines: 177
        models:
        - EmptyLeg
        - EmptyLegRequest
        - EmptyLegSubscription
      - path: app/services/positioning_service.py
        purpose: Core empty leg discovery and matching service with aircraft positioning logic
        lines: 500
        methods:
        - find_empty_legs
        - list_empty_legs
        - create_empty_leg_from_movement
      - path: app/api/v1/endpoints/bookings/empty_legs.py
        purpose: Empty legs API endpoints with booking optimization and sharing workflows
        lines: 1275
        endpoints:
        - /empty-legs/{booking_id}/optimize
        - /empty-legs/{booking_id}/request-sharing
      - path: app/services/empty_leg_processor_service.py
        purpose: Automated empty leg processing with profitability optimization
        lines: 200
        methods:
        - process_pending_bookings
        - optimize_booking_for_confirmation
      - path: app/db/manager/repositories/empty_leg_repository.py
        purpose: Empty legs data access layer with route matching and availability queries
        lines: 900
        operations:
        - find_empty_legs
        - get_empty_legs_for_route
        - find_bitcoin_accepting_operator
      - path: app/services/legacy_services/empty_leg_processor.py
        purpose: Legacy empty leg processing with Bitcoin settlement optimization
        lines: 906
        methods:
        - process_pending_bookings
        - _create_shared_flight_for_booking
      - path: app/services/empty_leg_solicitation_service.py
        purpose: Operator solicitation service for proactive empty leg inventory management
        lines: 100
        methods:
        - start_weekly_solicitation
        - track_operator_responses
      - path: app/api/v1/endpoints/aircraft/positioning.py
        purpose: Public empty legs discovery API for marketing and customer access
        lines: 160
        endpoints:
        - /aircraft/positioning/empty-legs
      database_models:
      - model: EmptyLeg
        purpose: Primary empty leg entity with comprehensive positioning and pricing data
        fields:
        - id
        - operator_id
        - aircraft_id
        - movement_id
        - from_airport_id
        - to_airport_id
        - departure_time
        - arrival_time
        - price
        - currency
        - original_price
        - available_seats
        - is_flexible
        - date_flexibility_days
        - status
        - source
        - visibility
        - expires_at
        - booking_id
        - notes
        - emptyleg_metadata
        - departure_distance_nm
        - arrival_distance_nm
        - time_difference_hours
        - flight_time_hours
        - distance_nm
        indexes:
        - operator_id
        - aircraft_id
        - from_airport_id
        - to_airport_id
        - departure_time
        - status
        relationships:
        - operator
        - aircraft
        - booking
        - quotes
        - shared_flight
        - movement
        - from_airport
        - to_airport
      - model: EmptyLegRequest
        purpose: Operator solicitation tracking for proactive empty leg inventory management
        fields:
        - id
        - operator_id
        - request_date
        - response_date
        - has_responded
        - empty_legs_count
        - email_sent
        - email_sent_date
        - email_opened
        - email_opened_date
        - notes
        - request_metadata
        tracking: Email delivery and operator response monitoring
      - model: EmptyLegSubscription
        purpose: User subscription management for empty leg notifications and alerts
        fields:
        - id
        - user_id
        - from_airport_id
        - to_airport_id
        - preferred_aircraft_categories
        - min_seats
        - date_range_start
        - date_range_end
        - price_max
        - notify_by_email
        - notify_by_sms
        - notify_by_app
        - is_active
        - last_notified
        notifications: Multi-channel notification preferences and delivery tracking
      services:
      - service: PositioningService
        path: app/services/positioning_service.py
        purpose: Core empty leg discovery and aircraft repositioning logic
        methods:
        - find_empty_legs
        - list_empty_legs
        - create_empty_leg_from_movement
        integration: Aircraft movement tracking and empty leg opportunity identification
      - service: EmptyLegProcessorService
        path: app/services/empty_leg_processor_service.py
        purpose: Automated empty leg processing with booking optimization
        methods:
        - process_pending_bookings
        - optimize_booking_for_confirmation
        - process_sharing_requests
        automation: Scheduler-driven empty leg processing and profitability optimization
      - service: EmptyLegSolicitationService
        path: app/services/empty_leg_solicitation_service.py
        purpose: Operator solicitation automation for proactive inventory management
        methods:
        - start_weekly_solicitation
        - track_operator_responses
        - process_operator_opt_status
        outreach: Automated operator communication and response tracking
      repositories:
      - repository: EmptyLegRepository
        purpose: Empty legs data access with sophisticated route matching and availability queries
        methods:
        - find_empty_legs
        - get_empty_legs_for_route
        - find_bitcoin_accepting_operator
        - get_empty_legs_for_aircraft
        - find_matching_empty_legs_by_route
        queries: Geographic proximity matching, time window filtering, aircraft category matching
      behavior:
        empty_leg_discovery:
          aircraft_movement_tracking:
          - Monitor aircraft movements and positioning schedules for repositioning opportunities
          - Identify gaps between charter bookings that require aircraft repositioning
          - Calculate optimal routes and timing for repositioning flights
          - Generate empty leg opportunities from scheduled aircraft movements
          operator_solicitation:
          - Weekly automated solicitation emails to operators requesting empty leg inventory
          - Track operator response rates and empty leg submission patterns
          - Manage operator opt-in/opt-out preferences for solicitation campaigns
          - Monitor email delivery, open rates, and response tracking metrics
          dynamic_pricing:
          - Calculate empty leg pricing at 40-60% discount from regular charter rates
          - Apply dynamic pricing based on route demand, timing, and market conditions
          - Optimize pricing for maximum revenue while maintaining competitive advantage
          - Factor in aircraft positioning costs and operator profit margins
        empty_leg_marketplace:
          availability_management:
          - Real-time empty leg availability tracking with automatic expiration handling
          - Geographic proximity matching for flexible departure/arrival locations
          - Time window flexibility management with date range preferences
          - Aircraft category matching based on passenger count and route requirements
          booking_conversion:
          - Seamless empty leg to booking conversion with quote generation
          - Automated booking creation from empty leg selection and payment processing
          - Integration with booking lifecycle for confirmed flight management
          - Empty leg status updates upon booking to prevent double-booking
          shared_flight_integration:
          - Empty leg to shared flight conversion for revenue optimization
          - Individual seat booking creation from empty leg opportunities
          - Seat availability management with real-time updates and confirmations
          - Commission calculation and revenue sharing for seat sales
        automated_processing:
          scheduler_integration:
          - Background processing every 15 minutes for pending booking optimization
          - Empty leg matching and booking confirmation automation
          - Shared flight creation from empty leg opportunities
          - Bitcoin settlement incentive processing for operator payments
          profitability_optimization:
          - Multi-strategy booking optimization including empty leg matching
          - Alternative aircraft identification for cost-effective empty leg options
          - Route optimization and fuel efficiency calculations
          - Operator negotiation automation for better empty leg rates
        subscription_management:
          user_notifications:
          - Multi-channel notification system (email, SMS, app) for empty leg alerts
          - Personalized empty leg matching based on user route preferences
          - Flexible date range and pricing threshold management
          - Notification frequency control and delivery optimization
          preference_management:
          - Aircraft category preferences with seat count requirements
          - Geographic flexibility settings for departure/arrival airports
          - Price threshold management with automatic alert triggers
          - Notification channel preferences and delivery timing
      primary_flows:
        empty_leg_discovery_flow:
          steps:
          - 'Aircraft Movement Monitoring: Track scheduled flights and positioning requirements'
          - 'Opportunity Identification: Identify repositioning flights and scheduling gaps'
          - 'Empty Leg Creation: Generate empty leg records with pricing and availability'
          - 'Marketplace Publication: Publish empty legs to customer-facing marketplace'
          - 'Availability Management: Monitor expiration and booking status updates'
          automation:
          - Continuous aircraft movement monitoring with real-time opportunity detection
          - Automated empty leg creation from positioning requirements
          - Dynamic pricing updates based on market conditions and demand
        booking_conversion_flow:
          steps:
          - 'Empty Leg Selection: Customer selects empty leg from marketplace'
          - 'Quote Generation: Convert empty leg to bookable quote with pricing'
          - 'Payment Processing: Process payment with empty leg discount pricing'
          - 'Booking Creation: Create confirmed booking from empty leg conversion'
          - 'Status Updates: Update empty leg status to prevent double-booking'
          integration:
          - Seamless integration with booking domain for quote and payment processing
          - Real-time availability validation to prevent booking conflicts
          - Automatic empty leg status updates upon successful booking
        operator_solicitation_flow:
          steps:
          - 'Weekly Solicitation: Send automated emails to operators requesting empty legs'
          - 'Response Tracking: Monitor operator responses and empty leg submissions'
          - 'Inventory Processing: Process submitted empty legs and validate data'
          - 'Marketplace Integration: Publish validated empty legs to customer marketplace'
          - 'Performance Analytics: Track operator response rates and inventory quality'
          automation:
          - Automated email campaigns with personalized operator messaging
          - Response tracking with email open rates and submission monitoring
          - Opt-in/opt-out management for operator communication preferences
      invariants:
      - Empty leg pricing must be 40-60% below regular charter rates for the same route
      - Empty leg availability must be validated in real-time to prevent double-booking
      - Aircraft positioning requirements must be verified before empty leg creation
      - Empty leg expiration must be enforced to maintain marketplace accuracy
      - Operator solicitation must respect opt-out preferences and communication limits
      - Empty leg to booking conversion must update status atomically
      - Shared flight creation from empty legs must maintain seat availability constraints
      - Geographic proximity matching must use accurate distance calculations
      - Time window flexibility must respect aircraft scheduling constraints
      - Subscription notifications must honor user preferences and delivery settings
      forbidden_states:
      - Empty legs published without verified aircraft availability
      - Double-booking of empty legs without status synchronization
      - Empty leg pricing above regular charter rates
      - Expired empty legs remaining in active marketplace
      - Operator solicitation without opt-in consent
      - Empty leg conversion without payment validation
      - Shared flight creation exceeding aircraft capacity
      - Subscription notifications without user consent
      - Empty leg data without proper operator attribution
      - Geographic matching without distance validation
      depends_on:
      - aircraft - Aircraft positioning data and movement tracking
      - booking - Booking conversion and payment processing integration
      - operator - Operator management and communication preferences
      - airport - Airport data for route matching and geographic calculations
      - communication - Email and notification services for operator solicitation
      provides:
      - empty_leg_marketplace - Complete empty leg discovery and booking platform
      - aircraft_positioning_optimization - Repositioning flight opportunity identification
      - discounted_charter_access - 40-60% discounted private jet charter opportunities
      - operator_inventory_management - Proactive empty leg solicitation and tracking
      - shared_flight_creation - Empty leg to shared flight conversion capabilities
      - subscription_notification_system - Personalized empty leg alert and matching service
      enforcement_hooks:
      - validate_empty_leg_pricing_discounts
      - ensure_aircraft_availability_verification
      - confirm_operator_solicitation_consent
      - verify_booking_conversion_atomicity
      - validate_geographic_proximity_calculations
      scheduler_integration:
        empty_leg_automation:
        - task: Empty Leg Processing
          schedule: every 15 minutes
          purpose: Process pending bookings and optimize with empty leg matching
          handler: empty_leg_processor_service.py:process_pending_bookings
          optimization: Multi-strategy booking optimization including empty leg alternatives
        - task: Sharing Request Processing
          schedule: every 15 minutes
          purpose: Convert empty legs to shared flights for revenue optimization
          handler: empty_leg_processor_service.py:process_sharing_requests_from_clients
          conversion: Empty leg to shared flight conversion with seat availability management
        - task: Bitcoin Settlement Incentives
          schedule: daily 3:00 AM
          purpose: Process Bitcoin payment incentives for operators with empty legs
          handler: empty_leg_processor_service.py:process_bitcoin_settlement_incentives
          payments: Operator payment optimization with Bitcoin settlement bonuses
        - task: Weekly Operator Solicitation
          schedule: weekly Monday 9:00 AM
          purpose: Send automated empty leg solicitation emails to operators
          handler: empty_leg_solicitation_service.py:start_weekly_solicitation
          outreach: Proactive operator communication for empty leg inventory management
      endpoints:
        empty_leg_marketplace:
        - path: /api/v1/aircraft/positioning/empty-legs
          methods:
          - GET
          description: Public empty leg search and discovery
          response_time: <500ms
          handler: positioning.py:find_empty_legs
          access: Public access for marketing and customer discovery
        - path: /api/v1/bookings/empty-legs
          methods:
          - GET
          description: List available empty legs with filtering
          response_time: <500ms
          handler: tracking.py:list_empty_legs
          access: User access with optional authentication
        booking_optimization:
        - path: /api/v1/empty-legs/{booking_id}/optimize
          methods:
          - POST
          description: Optimize booking with empty leg alternatives and strategies
          response_time: <3000ms
          handler: empty_legs.py:optimize_booking_for_confirmation
          access: Admin-only for booking optimization workflows
        - path: /api/v1/empty-legs/{booking_id}/request-sharing
          methods:
          - POST
          description: Request seat sharing from confirmed booking
          response_time: <1000ms
          handler: empty_legs.py:request_seat_sharing
          access: User access for own bookings
        shared_flight_conversion:
        - path: /api/v1/shared-flights/empty-leg/{empty_leg_id}
          methods:
          - POST
          description: Create shared flight from empty leg opportunity
          response_time: <1000ms
          handler: flight_sharing.py:create_shared_flight_from_empty_leg
          access: Admin and operator access for shared flight creation
        operator_solicitation:
        - path: /api/v1/empty-legs/solicitation/start
          methods:
          - POST
          description: Start weekly operator solicitation campaign
          response_time: <1000ms
          handler: empty_legs.py:start_weekly_solicitation
          access: Admin-only for solicitation management
        - path: /api/v1/empty-legs/solicitation/operators
          methods:
          - GET
          description: Get operator solicitation status and metrics
          response_time: <500ms
          handler: empty_legs.py:get_solicitation_operators
          access: Admin-only for solicitation monitoring
      error_handling:
        empty_leg_errors:
        - error_type: EmptyLegNotAvailable
          description: Empty leg no longer available for booking
          response_code: 409
          recovery: Suggest alternative empty legs or regular booking options
        - error_type: InvalidPricingDiscount
          description: Empty leg pricing validation failed
          response_code: 400
          recovery: Recalculate pricing with valid discount parameters
        - error_type: AircraftPositioningConflict
          description: Aircraft positioning conflict prevents empty leg creation
          response_code: 409
          recovery: Validate aircraft availability and positioning requirements
      monitoring:
        empty_leg_metrics:
        - metric: empty_leg_conversion_rate
          description: Percentage of empty legs converted to bookings
          target: '>25%'
        - metric: average_empty_leg_discount
          description: Average discount percentage for empty leg pricing
          target: 40-60%
        - metric: operator_response_rate
          description: Percentage of operators responding to solicitation
          target: '>40%'
        - metric: empty_leg_marketplace_accuracy
          description: Percentage of empty legs with accurate availability
          target: '>95%'
      testing:
        empty_leg_testing:
        - test_type: Unit Tests
          coverage: Empty leg discovery, pricing, and conversion logic
          location: tests/unit/services/test_empty_leg_processor.py
        - test_type: Integration Tests
          coverage: Complete empty leg workflow from discovery to booking
          location: tests/integration/endpoints/bookings/test_empty_legs_endpoints.py
        - test_type: API Tests
          coverage: Empty leg API endpoints and response validation
          location: tests/integration/endpoints/bookings/test_empty_legs_endpoints.py
      security:
        empty_leg_security:
        - Operator solicitation requires explicit opt-in consent
        - Empty leg data access controlled by visibility settings (public/private/vip)
        - Booking conversion requires proper user authentication and payment validation
        - Admin-only access for empty leg optimization and solicitation management
        - Subscription management requires user ownership validation
      performance:
        empty_leg_performance_targets:
        - 'Empty leg search queries: <500ms response time'
        - 'Booking optimization processing: <3000ms per booking'
        - 'Shared flight conversion: <1000ms per empty leg'
        - 'Operator solicitation processing: <5000ms per campaign'
        - 'Real-time availability validation: <100ms per check'
      consolidation_notes:
        empty_leg_ownership:
        - Complete empty leg lifecycle management consolidated in PositioningService
        - Booking optimization centralized in EmptyLegProcessorService
        - Operator solicitation unified in EmptyLegSolicitationService
        - Marketplace access standardized across multiple API endpoints
        - Shared flight integration consolidated in FlightSharingService
      implementation_gaps:
        enhanced_matching_algorithms:
          status: enhancement_opportunity
          description: Machine learning-based empty leg matching could improve conversion rates
          current_state: Geographic proximity and time window matching
          enhancement: AI-powered matching with user behavior and preference learning
          priority: medium
          estimated_effort: 3-4 weeks
        real_time_pricing_optimization:
          status: enhancement_opportunity
          description: Dynamic pricing based on real-time demand and market conditions
          current_state: Static discount percentages (40-60%)
          enhancement: Real-time pricing optimization with demand-based adjustments
          priority: low
          estimated_effort: 2-3 weeks
      files:
      - app/db/models/empty_leg.py - Complete empty leg data models and relationships
      - app/services/positioning_service.py - Core empty leg discovery and positioning logic
      - app/api/v1/endpoints/bookings/empty_legs.py - Empty legs API endpoints and workflows
      - app/services/empty_leg_processor_service.py - Automated empty leg processing service
      - app/db/manager/repositories/empty_leg_repository.py - Empty legs data access layer
      - app/services/legacy_services/empty_leg_processor.py - Legacy empty leg processing
      - app/services/empty_leg_solicitation_service.py - Operator solicitation automation
      - app/api/v1/endpoints/aircraft/positioning.py - Public empty legs discovery API
      - app/db/schemas/empty_leg.py - Empty leg database schemas
      - app/schemas/empty_legs.py - Empty legs API response schemas
      - tests/integration/endpoints/bookings/test_empty_legs_endpoints.py - Empty legs integration tests
      domain: booking
      subdomain: empty_legs_empty_legs
      path: booking/empty_legs/empty_legs.yaml
      file_size_kb: 21.75
    booking_lifecycle_booking_lifecycle:
      system: villiers_booking_lifecycle
      description: Villiers.ai Booking Lifecycle Management Subdomain - World-Class State-Driven Booking Orchestration with
        Automated Transitions, Real-Time Status Tracking, and Complete Audit Trail Management. This subdomain owns all booking
        state management, lifecycle transitions, flight operations tracking, and post-completion processing workflows.
      intent_assertions:
      - Complete booking state management with enforced transition rules (SOURCING → PENDING → CONFIRMED → COMPLETED)
      - Automated booking lifecycle orchestration with intelligent status progression and validation
      - Real-time flight operations tracking from departure through arrival to completion
      - Comprehensive audit trail maintenance for all booking state changes and transitions
      - Automated post-flight processing with operator performance metrics and customer feedback collection
      - Error-resistant state management with rollback capabilities and transaction safety
      - Integration with payment processing for status-dependent booking confirmations
      - Scheduler-driven automated booking processing with profitability optimization
      technical_assertions:
      - path: app/db/models/enums.py
        purpose: Booking status enumeration with complete lifecycle state definitions
        lines: 107
        states:
        - SOURCING
        - PENDING
        - CONFIRMED
        - CANCELED
        - COMPLETED
        - OPERATOR_DECLINED
        - OPERATOR_AOG
      - path: app/services/booking_service.py
        purpose: Core booking lifecycle business logic with state transition management
        lines: 586
        methods:
        - confirm_booking
        - cancel_booking
        - update_booking
        - get_booking_by_id
      - path: app/api/v1/endpoints/bookings/bookings.py
        purpose: Booking lifecycle API endpoints with state management operations
        lines: 1129
        endpoints:
        - /bookings/{id}/depart
        - /bookings/{id}/arrive
        - /bookings/{id}/complete
      - path: app/db/schemas/booking.py
        purpose: Booking lifecycle database schemas with metadata and status tracking
        lines: 210
        schemas:
        - BookingBase
        - BookingCreate
        - BookingUpdate
        - BookingWithRelations
      - path: app/tasks/booking_processor.py
        purpose: Automated booking lifecycle processing with scheduler integration
        lines: 84
        tasks:
        - process_bookings_task
        - process_sharing_requests_from_clients
      - path: app/services/trips_service.py
        purpose: Booking-to-trip status mapping with lifecycle state translation
        lines: 500
        mappings:
        - _map_booking_status_to_trip_status
        - _map_trip_status_to_booking_status
      - path: app/db/manager/repositories/booking_repository.py
        purpose: Booking lifecycle data access with status-based queries and updates
        lines: 847
        operations:
        - get_pending_bookings_for_processing
        - confirm_booking
        - update_booking_status
      database_models:
      - model: Booking
        purpose: Primary booking entity with comprehensive lifecycle state management
        fields:
        - id
        - status
        - payment_status
        - booking_metadata
        - completed_at
        - confirmed_at
        status_field: status (BookingStatusEnum)
        metadata_field: booking_metadata (JSONType)
        audit_fields:
        - created_at
        - updated_at
        - completed_at
        - confirmed_at
        relationships:
        - user
        - quote
        - operator
        - aircraft
        - trip
      - model: BookingStatusEnum
        purpose: Enumeration defining all valid booking lifecycle states
        values:
        - SOURCING
        - PENDING
        - CONFIRMED
        - CANCELED
        - COMPLETED
        - OPERATOR_DECLINED
        - OPERATOR_AOG
        transitions: Enforced workflow progression with validation rules
      services:
      - service: BookingService
        path: app/services/booking_service.py
        purpose: Core booking lifecycle orchestration with state management
        methods:
        - confirm_booking
        - cancel_booking
        - update_booking
        - get_booking_by_id
        state_management: Enforces valid state transitions with business rule validation
      - service: TripsService
        path: app/services/trips_service.py
        purpose: Booking lifecycle to trip status mapping and translation
        methods:
        - _map_booking_status_to_trip_status
        - _map_trip_status_to_booking_status
        integration: Provides UI-friendly status representation from booking states
      repositories:
      - repository: BookingRepository
        purpose: Booking lifecycle data access with status-based operations
        methods:
        - get_pending_bookings_for_processing
        - confirm_booking
        - update_booking_status
        queries: Status-based filtering and lifecycle state management
      behavior:
        booking_state_transitions:
          sourcing_to_pending:
          - Initial booking creation starts in SOURCING status
          - Quote selection and validation triggers transition to PENDING
          - Payment initiation required for PENDING status confirmation
          - User quote selection metadata recorded in booking_metadata
          pending_to_confirmed:
          - Payment completion triggers automatic transition to CONFIRMED
          - Platform fee payment validation ensures payment status alignment
          - Operator notification sent upon confirmation
          - Quote status updated to ACCEPTED, declined quotes marked DECLINED
          - Booking metadata updated with confirmation details and timestamps
          confirmed_to_completed:
          - Flight departure tracking initiated upon confirmation
          - Passenger arrival confirmation triggers completion preparation
          - Post-flight processing includes operator performance metrics
          - Customer feedback collection and rating system activation
          - Financial reconciliation and payment finalization
        flight_operations_tracking:
          departure_processing:
          - Flight departure confirmation with timestamp recording in booking_metadata
          - Booking status remains CONFIRMED but flight_status metadata updated to 'in_progress'
          - Passenger notification and real-time tracking activation
          - Emergency contact notification protocols initiated
          - 'API endpoint: PATCH /bookings/{id}/depart validates CONFIRMED status'
          arrival_processing:
          - Passenger arrival confirmation at destination with location verification
          - Arrival timestamp and location recorded in booking_metadata
          - Flight_status metadata updated to 'arrived' with arrival_confirmed flag
          - Ground transportation coordination notifications triggered
          - Return flight reminders for round-trip bookings
          - 'API endpoint: PATCH /bookings/{id}/arrive updates flight tracking'
          completion_workflow:
          - Post-flight processing triggered by POST /bookings/{id}/complete endpoint
          - Booking status transition from CONFIRMED to COMPLETED with completed_at timestamp
          - Operator performance metrics calculation and database updates
          - Customer feedback collection system activation with email notifications
          - Financial reconciliation and payment finalization processing
          - Marketing follow-up and future booking incentives scheduling
        automated_lifecycle_processing:
          scheduler_integration:
          - Booking processor task runs every 15 minutes via app/tasks/booking_processor.py
          - Pending bookings processed for profitability and automatic confirmation
          - Sharing requests from clients processed for shared flight creation
          - Bitcoin settlement incentives processed daily at 3:00 AM
          state_validation:
          - All state transitions validated against business rules and payment status
          - Invalid state transitions blocked with appropriate error responses
          - Audit trail maintained for all state changes with user and timestamp tracking
          - Rollback capabilities for failed transitions with transaction safety
        error_handling_and_recovery:
          invalid_transitions:
          - Departure marking blocked for non-CONFIRMED bookings with 400 error response
          - Completion processing blocked without arrival confirmation
          - Cancellation blocked for COMPLETED bookings with appropriate error messaging
          transaction_safety:
          - All state transitions wrapped in database transactions
          - Rollback capabilities for failed multi-step operations
          - Consistent state maintenance across related entities (quotes, payments)
      primary_flows:
        complete_booking_lifecycle:
          steps:
          - 'Booking Creation: User selects quote → Booking created in SOURCING status'
          - 'Payment Processing: Payment initiated → Status transitions to PENDING'
          - 'Confirmation: Payment completed → Status transitions to CONFIRMED'
          - 'Flight Operations: Departure tracking → Flight_status metadata updated'
          - 'Arrival Processing: Arrival confirmation → Arrival metadata recorded'
          - 'Completion: Post-flight processing → Status transitions to COMPLETED'
          state_persistence:
          - All state changes recorded in booking_metadata with timestamps
          - Audit trail maintained for compliance and debugging
          - Status history preserved for analytics and reporting
        cancellation_flow:
          steps:
          - 'Cancellation Request: User/operator initiates cancellation'
          - 'Validation: Booking status checked for cancellation eligibility'
          - 'Metadata Update: Cancellation reason and feedback recorded'
          - 'Status Transition: Booking status updated to CANCELED'
          - 'Notifications: Operator and user notifications sent'
          restrictions:
          - COMPLETED bookings cannot be cancelled
          - Cancellation metadata must include reason and user_id
          - Refund processing triggered based on cancellation timing
      invariants:
      - 'Booking status transitions must follow defined workflow: SOURCING → PENDING → CONFIRMED → COMPLETED'
      - Flight departure tracking can only be activated for CONFIRMED bookings
      - Passenger arrival confirmation requires prior departure confirmation
      - Booking completion requires both departure and arrival confirmation
      - All state transitions must be recorded in booking_metadata with timestamps
      - Payment status must align with booking status for confirmation transitions
      - Cancelled bookings cannot transition to any other status
      - Completed bookings cannot be cancelled or modified
      - Booking metadata must maintain complete audit trail for all changes
      - Scheduler processing must respect booking state transition rules
      forbidden_states:
      - Booking confirmation without completed payment processing
      - Flight departure marking for non-CONFIRMED bookings
      - Arrival confirmation without prior departure confirmation
      - Booking completion without both departure and arrival metadata
      - State transitions without proper audit trail recording
      - Direct status updates bypassing business rule validation
      - Booking cancellation after completion
      - Payment status inconsistency with booking status
      - Metadata updates without timestamp recording
      - Scheduler processing overriding manual state management
      depends_on:
      - authentication - User authentication for booking lifecycle operations
      - payment - Payment processing integration for status-dependent confirmations
      - communication - Notification services for status change communications
      - analytics - Booking lifecycle metrics and performance tracking
      provides:
      - booking_state_management - Complete booking lifecycle state orchestration
      - flight_operations_tracking - Real-time flight status and passenger tracking
      - automated_lifecycle_processing - Scheduler-driven booking automation
      - booking_audit_trail - Comprehensive state change tracking and compliance
      - operator_performance_metrics - Booking-based operator reliability scoring
      - customer_lifecycle_data - Booking progression analytics and insights
      enforcement_hooks:
      - validate_booking_state_transitions
      - ensure_payment_status_alignment
      - confirm_audit_trail_completeness
      - verify_flight_operations_sequence
      - validate_scheduler_processing_rules
      scheduler_integration:
        booking_automation:
        - task: Booking Processing
          schedule: every 15 minutes
          purpose: Process pending bookings and confirm profitable flights
          handler: booking_processor.py:process_bookings_task
          state_impact: Transitions PENDING bookings to CONFIRMED based on profitability
        - task: Sharing Request Processing
          schedule: every 15 minutes
          purpose: Process client sharing requests and create shared flights
          handler: booking_processor.py:process_sharing_requests_from_clients
          state_impact: Creates new bookings in SOURCING status for shared flights
        - task: Bitcoin Settlement Incentives
          schedule: daily 3:00 AM
          purpose: Process Bitcoin payment incentives for operators
          handler: booking_processor.py:process_bitcoin_settlement_incentives
          state_impact: No direct booking state changes, payment processing optimization
      error_handling:
        state_transition_errors:
        - error_type: InvalidStateTransition
          description: Booking state transition not allowed by business rules
          response_code: 400
          recovery: Return current booking state with allowed transitions
        - error_type: PaymentStatusMismatch
          description: Payment status inconsistent with requested booking state
          response_code: 400
          recovery: Validate payment status before allowing state transition
        - error_type: MissingFlightOperations
          description: Flight operations sequence not properly followed
          response_code: 400
          recovery: Enforce departure confirmation before arrival processing
      monitoring:
        booking_lifecycle_metrics:
        - metric: state_transition_success_rate
          description: Percentage of successful booking state transitions
          target: '>99.5%'
        - metric: average_lifecycle_duration
          description: Average time from SOURCING to COMPLETED
          target: <7 days
        - metric: automated_processing_efficiency
          description: Percentage of bookings processed automatically by scheduler
          target: '>80%'
        - metric: state_transition_error_rate
          description: Rate of failed state transitions
          target: <0.5%
      testing:
        state_transition_testing:
        - test_type: Unit Tests
          coverage: All state transition methods and validation rules
          location: tests/unit/services/test_booking_service.py
        - test_type: Integration Tests
          coverage: Complete booking lifecycle workflows
          location: tests/integration/test_booking_flow.py
        - test_type: State Machine Tests
          coverage: Invalid state transition prevention
          location: tests/integration/test_booking_flow.py:test_booking_state_transitions
      security:
        state_management_security:
        - All state transitions require proper user authentication and authorization
        - Booking ownership validation enforced for all lifecycle operations
        - Audit trail immutability with tamper-proof timestamp recording
        - Payment status validation prevents unauthorized booking confirmations
        - Operator notification security prevents unauthorized status updates
      performance:
        lifecycle_performance_targets:
        - 'State transition processing: <100ms per operation'
        - 'Booking status queries: <50ms response time'
        - 'Scheduler processing: <5 minutes per batch'
        - 'Audit trail queries: <200ms for full booking history'
        - 'Flight operations tracking: <100ms per status update'
      consolidation_notes:
        booking_lifecycle_ownership:
        - Complete booking state management consolidated in BookingService
        - Flight operations tracking centralized in booking lifecycle endpoints
        - Automated processing unified in booking_processor.py scheduler task
        - Audit trail management standardized across all state transitions
        - Status mapping consolidated in TripsService for UI consistency
      implementation_gaps:
        enhanced_state_validation:
          status: enhancement_opportunity
          description: State machine validation could be more robust with formal state machine implementation
          current_state: Business rule validation in service methods
          enhancement: Formal state machine with transition guards and automated validation
          priority: medium
          estimated_effort: 1-2 weeks
        real_time_status_updates:
          status: enhancement_opportunity
          description: Real-time status updates for frontend could be improved with WebSocket integration
          current_state: Polling-based status updates via REST API
          enhancement: WebSocket-based real-time status broadcasting
          priority: low
          estimated_effort: 2-3 weeks
      files:
      - app/db/models/enums.py - Booking status enumeration definitions
      - app/services/booking_service.py - Core booking lifecycle business logic
      - app/api/v1/endpoints/bookings/bookings.py - Booking lifecycle API endpoints
      - app/db/schemas/booking.py - Booking lifecycle database schemas
      - app/tasks/booking_processor.py - Automated booking lifecycle processing
      - app/services/trips_service.py - Booking-to-trip status mapping
      - app/db/manager/repositories/booking_repository.py - Booking lifecycle data access
      - tests/integration/test_booking_flow.py - Booking lifecycle integration tests
      - tests/integration/test_booking_flow_proof.py - Booking workflow proof tests
      domain: booking
      subdomain: booking_lifecycle_booking_lifecycle
      path: booking/booking_lifecycle/booking_lifecycle.yaml
      file_size_kb: 16.91
    shared_flights_shared_flights:
      system: villiers_shared_flights
      description: Villiers.ai Shared Flights Subdomain - Charter Aviation Cost-Sharing Platform with Multi-Passenger Flight
        Matching, Dynamic Pricing Optimization, and Collaborative Booking Management. This subdomain enables multiple passengers
        to share charter flights, reducing individual costs while maintaining charter aviation's convenience and flexibility
        through intelligent passenger matching, cost distribution, and flight coordination.
      intent_assertions:
      - Complete shared flight lifecycle from empty leg conversion to multi-passenger booking coordination
      - Cost-sharing optimization enabling charter aviation accessibility through passenger matching
      - Dynamic pricing algorithms with demand-based optimization and profitability analysis
      - Intelligent passenger matching with preference compatibility and flight coordination
      - Real-time seat availability management with booking confirmation thresholds
      - Automated flight confirmation based on minimum seat fill requirements and profitability
      - Comprehensive cancellation handling with seat redistribution and flight status management
      - Revenue optimization through dynamic pricing and demand-based seat pricing adjustments
      technical_assertions:
      - path: app/db/models/shared_flight.py
        purpose: Complete shared flight data models with seat management and passenger coordination
        lines: 93
        models:
        - SharedFlight
        - SharedSeat
      - path: app/services/flight_sharing_service.py
        purpose: Core flight sharing service with booking coordination and status management
        lines: 410
        methods:
        - create_shared_flight
        - list_available_shared_flights
        - book_seats
        - cancel_seat_booking
      - path: app/services/legacy_services/flight_sharing.py
        purpose: Legacy flight sharing implementation with profitability optimization
        lines: 805
        methods:
        - create_shared_flight
        - book_shared_flight_seats
        - add_passenger_to_shared_flight
      - path: app/api/v1/endpoints/bookings/flight_sharing.py
        purpose: Shared flight API endpoints with search, booking, and management workflows
        lines: 390
        endpoints:
        - /shared-flights
        - /shared-flights/{id}
        - /shared-flights/empty-leg/{id}
        - /shared-flights/{id}/book
      - path: app/db/manager/repositories/shared_flight_repository.py
        purpose: Shared flight data access with complex seat management and profitability analysis
        lines: 1026
        operations:
        - create_shared_flight_from_empty_leg
        - book_shared_flight_seats
        - validate_flight_profitability
      - path: app/schemas/flight_sharing.py
        purpose: Comprehensive shared flight API schemas with detailed validation and response models
        lines: 450
        schemas:
        - SharedFlightListResponse
        - BookSharedFlightRequest
        - CreateSharedFlightResponse
      - path: app/services/ticket_management_service.py
        purpose: Flight ticket management with profitability validation and pricing optimization
        lines: 400
        methods:
        - validate_flight_profitability
        - optimize_flight_pricing
        - generate_ticket
      database_models:
      - model: SharedFlight
        purpose: Primary shared flight entity with comprehensive flight, pricing, and coordination data
        fields:
        - id
        - empty_leg_id
        - aircraft_id
        - from_airport_id
        - to_airport_id
        - from_airport_code
        - to_airport_code
        - departure_time
        - arrival_time
        - total_seats
        - min_seats_required
        - per_seat_price
        - status
        - is_confirmed
        - confirmed_at
        - distance_nm
        - flight_time_hours
        - flight_metadata
        - original_booking_id
        indexes:
        - empty_leg_id
        - aircraft_id
        - departure_time
        - status
        - is_confirmed
        relationships:
        - empty_leg
        - aircraft
        - seats
        - from_airport
        - to_airport
        - original_booking
      - model: SharedSeat
        purpose: Individual seat entity with passenger assignment and booking coordination
        fields:
        - id
        - shared_flight_id
        - booking_id
        - user_id
        - seat_number
        - status
        - price
        - passenger_details
        tracking: Individual seat status, passenger assignment, and booking association
        relationships:
        - shared_flight
        - booking
        - user
      services:
      - service: FlightSharingService
        path: app/services/flight_sharing_service.py
        purpose: Core shared flight management with booking coordination and status tracking
        methods:
        - create_shared_flight
        - list_available_shared_flights
        - book_seats
        - cancel_seat_booking
        - get_shared_flight_status
        - add_passenger_to_shared_flight
        integration: Empty leg conversion, seat management, passenger coordination
      - service: TicketManagementService
        path: app/services/ticket_management_service.py
        purpose: Flight profitability validation and dynamic pricing optimization
        methods:
        - validate_flight_profitability
        - optimize_flight_pricing
        - generate_ticket
        optimization: Revenue maximization through demand-based pricing and seat fill analysis
      repositories:
      - repository: SharedFlightRepository
        purpose: Shared flight data access with complex seat management and analytics
        methods:
        - get_shared_flight_by_id
        - create_shared_flight_from_empty_leg
        - list_available_shared_flights
        - book_shared_flight_seats
        - cancel_seat_booking
        - get_shared_flight_status
        - add_passenger_to_shared_flight
        - create_shared_flight_from_booking
        - validate_flight_profitability
        analytics: Profitability analysis, seat utilization tracking, revenue optimization
      behavior:
        cost_sharing_workflow:
          empty_leg_conversion:
          - Convert available empty legs into shared flights with calculated per-seat pricing
          - Apply 20% markup over base charter cost divided by available seats
          - Set minimum seat requirements (typically 50% capacity) for flight confirmation
          - Create individual seat records with availability status and pricing
          pricing_calculation:
          - Multi-factor pricing based on empty leg cost, aircraft category, and route distance
          - Distance-based fallback pricing using category-specific rates per nautical mile
          - Dynamic pricing optimization based on demand, time to departure, and seat fill rate
          - Round pricing to nearest $50 for clean customer presentation
          passenger_matching:
          - Intelligent seat allocation based on booking order and passenger requirements
          - Passenger detail collection for regulatory compliance and flight coordination
          - Preference matching for compatible passenger grouping when possible
          - Real-time seat availability updates with booking deadline enforcement
        booking_coordination:
          seat_reservation:
          - Individual seat booking with quote generation and platform fee calculation
          - Automatic seat assignment with booking confirmation and passenger association
          - Platform fee calculation at 10% of total booking value
          - 24-hour quote validity with booking deadline enforcement (6 hours before departure)
          flight_confirmation:
          - Automatic flight confirmation when minimum seat threshold is reached
          - Profitability validation before flight confirmation to ensure cost coverage
          - Dynamic pricing optimization triggered upon confirmation for revenue maximization
          - Passenger notification system for flight status changes and confirmations
          cancellation_management:
          - Seat release back to available inventory with immediate rebooking capability
          - Flight status recalculation if cancellation drops below minimum seat requirement
          - Cancellation policy enforcement (no refunds within 48 hours of departure)
          - Automatic passenger notifications for flight status changes due to cancellations
        profitability_optimization:
          revenue_analysis:
          - Real-time profitability calculation comparing total revenue to flight costs
          - Minimum profit margin enforcement (15%) before flight confirmation
          - Dynamic seat pricing based on demand patterns and booking velocity
          - Revenue optimization through time-based and demand-based pricing adjustments
          pricing_optimization:
          - Time-sensitive pricing with increased rates as departure approaches
          - Demand-based pricing with premium rates for high seat fill percentages
          - Seat fill rate analysis with pricing adjustments to optimize revenue
          - Cost-per-seat calculation ensuring minimum profitability thresholds
      primary_flows:
        shared_flight_creation_flow:
          steps:
          - 'Empty Leg Selection: Identify available empty legs suitable for shared flight conversion'
          - 'Aircraft Analysis: Validate aircraft capacity and calculate available seats for sharing'
          - 'Pricing Calculation: Calculate per-seat pricing with markup and cost optimization'
          - 'Seat Creation: Generate individual seat records with availability status'
          - 'Flight Publication: Make shared flight available for passenger booking'
          automation:
          - Automated empty leg monitoring for shared flight conversion opportunities
          - Dynamic pricing calculation based on multiple cost and demand factors
          - Real-time seat inventory management with availability tracking
        passenger_booking_flow:
          steps:
          - 'Flight Search: Customer searches available shared flights by route and date'
          - 'Seat Selection: Customer selects desired number of seats with pricing display'
          - 'Passenger Details: Collection of passenger information for regulatory compliance'
          - 'Quote Generation: Create quote with platform fee and booking terms'
          - 'Booking Confirmation: Process payment and assign seats to customer'
          - 'Flight Status Update: Update flight confirmation status based on seat fill'
          validation:
          - Seat availability validation before booking confirmation
          - Booking deadline enforcement preventing last-minute bookings
          - Passenger detail validation for regulatory compliance
          - Payment processing integration with platform fee collection
        profitability_validation_flow:
          steps:
          - 'Revenue Calculation: Sum all confirmed seat bookings and platform fees'
          - 'Cost Analysis: Compare total revenue to original flight cost and operational expenses'
          - 'Profit Margin Validation: Ensure minimum 15% profit margin before confirmation'
          - 'Flight Confirmation: Confirm flight when profitability and seat thresholds are met'
          - 'Pricing Optimization: Apply dynamic pricing adjustments for revenue maximization'
          optimization:
          - Real-time profitability monitoring with automatic status updates
          - Dynamic pricing adjustments based on demand and time factors
          - Revenue maximization through intelligent seat pricing strategies
      invariants:
      - Shared flights must maintain minimum seat fill requirements for confirmation
      - Per-seat pricing must ensure flight profitability with minimum 15% margin
      - Seat assignments must be unique and properly associated with bookings
      - Flight confirmation must validate both seat count and profitability thresholds
      - Booking deadlines must be enforced (6 hours before departure)
      - Cancellation policies must be consistently applied across all bookings
      - Platform fee calculations must be accurate and auditable
      - Passenger details must be collected for regulatory compliance
      - Seat availability must be updated in real-time across all booking channels
      - Flight status changes must trigger appropriate passenger notifications
      forbidden_states:
      - Shared flights created without proper profitability validation
      - Seat bookings accepted after booking deadline
      - Flight confirmation without meeting minimum seat or profitability requirements
      - Duplicate seat assignments for the same flight
      - Booking cancellations processed without proper seat release
      - Pricing calculations without cost coverage validation
      - Passenger bookings without required regulatory information
      - Flight status changes without passenger notification
      - Seat availability displayed without real-time inventory validation
      - Platform fee calculations without proper audit trail
      depends_on:
      - empty_legs - Empty leg inventory and conversion opportunities
      - aircraft - Aircraft capacity, specifications, and availability data
      - booking - Booking creation, payment processing, and lifecycle management
      - quotes - Quote generation, pricing calculation, and validation workflows
      - authentication - User authentication and authorization for booking access
      - payment - Payment processing, platform fees, and financial transaction management
      - communication - Passenger notifications, flight updates, and coordination messaging
      provides:
      - cost_sharing_platform - Multi-passenger charter cost distribution and coordination
      - passenger_matching_system - Intelligent passenger grouping and compatibility management
      - dynamic_pricing_engine - Demand-based pricing optimization and revenue maximization
      - seat_inventory_management - Real-time seat availability and booking coordination
      - profitability_validation_service - Flight cost coverage and profit margin enforcement
      - flight_confirmation_automation - Automated flight status management based on thresholds
      - shared_flight_marketplace - Customer-facing shared flight search and booking platform
      enforcement_hooks:
      - validate_shared_flight_profitability
      - ensure_minimum_seat_requirements
      - confirm_booking_deadline_compliance
      - verify_seat_assignment_uniqueness
      - validate_passenger_detail_completeness
      endpoints:
        shared_flight_search:
        - path: /api/v1/bookings/shared-flights
          methods:
          - GET
          description: Search and list available shared flights with filtering and pagination
          response_time: <1000ms
          handler: flight_sharing.py:list_shared_flights
          access: Public access with optional user authentication
        - path: /api/v1/bookings/shared-flights/{shared_flight_id}
          methods:
          - GET
          description: Get detailed shared flight information including real-time seat availability
          response_time: <500ms
          handler: flight_sharing.py:get_shared_flight
          access: Public access for flight details
        shared_flight_management:
        - path: /api/v1/bookings/shared-flights/empty-leg/{empty_leg_id}
          methods:
          - POST
          description: Create shared flight from empty leg with admin authorization
          response_time: <2000ms
          handler: flight_sharing.py:create_shared_flight_from_empty_leg
          access: Admin-only for shared flight creation
        - path: /api/v1/bookings/shared-flights/{shared_flight_id}/book
          methods:
          - POST
          description: Book seats on shared flight with passenger details and payment processing
          response_time: <3000ms
          handler: flight_sharing.py:book_shared_flight_seats
          access: User authentication required for booking
        booking_management:
        - path: /api/v1/bookings/bookings/{booking_id}/cancel
          methods:
          - POST
          description: Cancel shared flight booking with seat release and status updates
          response_time: <1000ms
          handler: flight_sharing.py:cancel_shared_flight_booking
          access: User can only cancel own bookings
      error_handling:
        shared_flight_errors:
        - error_type: InsufficientSeatsError
          description: Requested number of seats not available on shared flight
          response_code: 409
          recovery: Display available seat count and suggest alternative booking
        - error_type: BookingDeadlineExceededError
          description: Booking attempted within 6 hours of departure
          response_code: 410
          recovery: Inform customer of booking deadline and suggest alternative flights
        - error_type: FlightNotProfitableError
          description: Flight cannot be confirmed due to insufficient profitability
          response_code: 422
          recovery: Continue accepting bookings until profitability threshold is met
        - error_type: SharedFlightCreationError
          description: Failed to create shared flight from empty leg
          response_code: 422
          recovery: Validate empty leg availability and aircraft capacity
      monitoring:
        shared_flight_metrics:
        - metric: shared_flight_fill_rate
          description: Average percentage of seats filled per shared flight
          target: '>60%'
        - metric: shared_flight_confirmation_rate
          description: Percentage of shared flights that reach confirmation threshold
          target: '>70%'
        - metric: average_booking_response_time
          description: Average time from seat selection to booking confirmation
          target: <30 seconds
        - metric: shared_flight_profitability_margin
          description: Average profit margin for confirmed shared flights
          target: '>20%'
        - metric: passenger_satisfaction_score
          description: Customer satisfaction rating for shared flight experience
          target: '>4.2/5.0'
      testing:
        shared_flight_testing:
        - test_type: Unit Tests
          coverage: Shared flight creation, seat booking, pricing calculation, and profitability validation
          location: tests/unit/services/test_flight_sharing_service.py
        - test_type: Integration Tests
          coverage: Complete shared flight workflow from creation to booking confirmation
          location: tests/integration/endpoints/bookings/test_flight_sharing_endpoints.py
        - test_type: Validation Tests
          coverage: API response schema validation and business rule enforcement
          location: tests/integration/endpoints/bookings/test_flight_sharing_validation.py
      security:
        shared_flight_security:
        - Shared flight creation restricted to admin users with proper authorization
        - Passenger booking restricted to authenticated users with booking ownership validation
        - Seat assignment uniqueness enforced to prevent double-booking
        - Payment processing integration with secure platform fee collection
        - Passenger personal information protected with privacy compliance
      performance:
        shared_flight_performance_targets:
        - 'Shared flight search: <1000ms for filtered results with pagination'
        - 'Seat booking: <3000ms including payment processing and confirmation'
        - 'Flight status updates: <500ms for real-time availability changes'
        - 'Profitability calculation: <200ms for real-time validation'
        - 'Pricing optimization: <1000ms for dynamic pricing adjustments'
      consolidation_notes:
        shared_flight_architecture:
        - Complete shared flight lifecycle managed through FlightSharingService
        - Profitability validation centralized in TicketManagementService
        - Seat inventory management handled by SharedFlightRepository
        - Dynamic pricing optimization integrated with booking confirmation workflow
        - Passenger coordination unified across booking and communication systems
      implementation_gaps:
        advanced_passenger_matching:
          status: enhancement_opportunity
          description: AI-powered passenger compatibility matching based on preferences and travel patterns
          current_state: Basic seat assignment based on booking order
          enhancement: Intelligent passenger grouping with preference analysis and compatibility scoring
          priority: medium
          estimated_effort: 6-8 weeks
        dynamic_pricing_optimization:
          status: partially_implemented
          description: Real-time pricing adjustments based on market demand and booking velocity
          current_state: Basic demand-based pricing with time factors
          enhancement: Machine learning-powered pricing with market analysis and competitive intelligence
          priority: high
          estimated_effort: 8-10 weeks
        group_booking_coordination:
          status: planned_future
          description: Coordinated group bookings with split payment and passenger management
          current_state: Individual seat bookings only
          enhancement: Group booking workflows with coordinator roles and split payment options
          priority: low
          estimated_effort: 4-6 weeks
      files:
      - app/db/models/shared_flight.py - Shared flight and seat data models
      - app/services/flight_sharing_service.py - Core shared flight service implementation
      - app/services/legacy_services/flight_sharing.py - Legacy implementation with profitability features
      - app/api/v1/endpoints/bookings/flight_sharing.py - Shared flight API endpoints
      - app/db/manager/repositories/shared_flight_repository.py - Shared flight data access layer
      - app/schemas/flight_sharing.py - API request/response schemas with validation
      - app/services/ticket_management_service.py - Profitability validation and pricing optimization
      - app/db/schemas/shared_flight.py - Database schema definitions
      - tests/unit/services/test_flight_sharing_service.py - Unit test coverage
      - tests/integration/endpoints/bookings/test_flight_sharing_endpoints.py - Integration tests
      - tests/integration/endpoints/bookings/test_flight_sharing_validation.py - Schema validation tests
      - tests/unit/repositories/test_shared_flight_repository.py - Repository test coverage
      domain: booking
      subdomain: shared_flights_shared_flights
      path: booking/shared_flights/shared_flights.yaml
      file_size_kb: 20.7
  airport:
    main:
      system: airport
      description: Villiers.ai Airport Domain - Comprehensive airport data management system encompassing global airport database,
        geographical search, distance calculations, fee management, slot availability, and operational metadata for private
        jet charter route planning and optimization
      intent_assertions:
      - Must provide comprehensive global airport database with ICAO/IATA code validation
      - Real-time geographical search with intelligent ranking by relevance and proximity
      - Accurate distance calculations between airports with caching for performance optimization
      - Dynamic fee management with peak hour and weekend multipliers for cost estimation
      - Slot availability tracking with operational restrictions and noise regulations
      - Comprehensive airport metadata including facilities, customs, fuel types, and VIP services
      - Integration with aircraft positioning for nearest airport detection and routing
      - Support for alternative airport suggestions and route optimization
      - Automated data enrichment from multiple sources with quality validation
      - Seamless integration with booking lifecycle for departure/arrival validation
      technical_assertions:
      - path: /app/api/v1/endpoints/aircraft/airports.py
        desc: Comprehensive airport API endpoints (390 lines) - search, distance, fees, slot availability
        critical: true
        lines: 390
        key_endpoints:
        - GET /airports - Search airports with intelligent ranking
        - GET /airports/{icao_code} - Get detailed airport information
        - POST /airports/distance - Calculate distance between airports
        - GET /airports/radius/{lat}/{lon} - Find airports within radius
        - GET /airports/slots/{icao_code} - Check slot availability
        - GET /airports/fees/{icao_code} - Get airport fees and charges
      - path: /app/services/airport_service.py
        desc: Core airport business logic service (447 lines) using centralized database manager pattern
        critical: true
      - path: /app/db/models/airport.py
        desc: Airport database models (201 lines) - Airport, AirportDistance with comprehensive metadata
        critical: true
        lines: 201
        key_fields:
        - ICAO/IATA codes with validation and indexing
        - Geographical coordinates with spatial indexing
        - Comprehensive facility metadata (customs, fuel, VIP)
        - Dynamic fee structure with multipliers
        - Operational data (slots, noise restrictions, hours)
        - Relationships with aircraft, flights, and movements
      - path: /app/db/schemas/airport.py
        desc: Airport API schemas (158 lines) with validation and relationship handling
        critical: true
        lines: 158
        key_schemas:
        - AirportBase with comprehensive field validation
        - AirportSearchResult for optimized search responses
        - AirportDistanceWithDetails for route calculations
      - path: /app/services/airport_service.py
        desc: Core airport business logic service with centralized database management
      - path: /app/db/manager/repositories/airport_repository.py
        desc: Centralized airport data access layer (706 lines) with comprehensive operations
        critical: true
      - path: /app/services/legacy_services/airports.py
        desc: Legacy airport service (504 lines) with direct database access patterns
        critical: false
        status: deprecated - migrating to centralized service
      - path: /app/utils/airports.py
        desc: Airport utility functions and caching (148 lines) for performance optimization
        critical: true
      - path: /app/data/airports.json
        desc: Static airport data (202 lines) for fallback and initial seeding
        critical: false
      - path: /app/db/seeders/airport_seeder.py
        desc: Airport database seeder (320 lines) with essential global airport data
        critical: true
      behavior: null
      airport_search_workflow:
      - Intelligent airport search with ICAO/IATA code prioritization
      - Fuzzy matching for airport names, cities, and countries
      - Geographical proximity-based ranking for location-aware results
      - Region-based filtering for localized search results
      - Multi-criteria search supporting partial matches and typo tolerance
      distance_calculation_workflow:
      - Geodesic distance calculation using geopy library with nautical mile conversion
      - Distance caching in AirportDistance table for frequently requested routes
      - Batch distance calculations for route optimization and planning
      - Spatial indexing for efficient geographical queries and radius searches
      - Alternative airport suggestions based on proximity and capabilities
      fee_management_workflow:
      - Comprehensive fee structure (landing, handling, parking, passenger, security)
      - Dynamic fee calculation with peak hour and weekend multipliers
      - Fee caching and optimization for rapid quote generation
      - Integration with pricing service for total cost calculations
      - Historical fee tracking for pricing trend analysis
      slot_availability_workflow:
      - Real-time slot availability checking with operational context
      - Noise restrictions and operational hour validation
      - Peak season and time-based availability assessment
      - Slot requirement validation for different aircraft types
      - Integration with booking system for conflict detection
      data_enrichment_workflow:
      - Automated airport metadata enrichment from multiple sources
      - Data quality validation and consistency checking
      - Enrichment tracking with source attribution and timestamps
      - Continuous data updates and synchronization processes
      - Fallback mechanisms for data source failures
      geographical_operations:
      - Radius-based airport discovery with distance calculations
      - Nearest airport detection for aircraft positioning
      - Spatial queries with PostgreSQL spatial extensions
      - Coordinate validation and normalization
      - Regional airport clustering and optimization
      invariants:
      - All airports must have valid ICAO codes (4 characters, uppercase)
      - All airports must have valid geographical coordinates within valid ranges
      - IATA codes must be 3 characters when present and unique
      - Airport names and cities must be non-empty strings
      - Distance calculations must be cached for performance optimization
      - Airport fees must be non-negative values
      - Slot availability must consider operational restrictions and noise regulations
      - Search results must be ranked by relevance and proximity
      - Geographical queries must use proper spatial indexing
      - Airport metadata must be validated before storage
      - Distance cache hit ratio must exceed 90% for common routes
      - Airport search response time must be under 1000ms for 95th percentile
      - Geographical queries must complete within 800ms
      - Fee retrieval must be under 200ms for cached data
      forbidden_states:
      - Airport with invalid ICAO code format (not 4 uppercase characters)
      - Airport with coordinates outside valid range (-90 to 90 lat, -180 to 180 lon)
      - Duplicate ICAO codes in the database
      - Airport fees with negative values
      - Airport without proper geographical coordinates
      - Distance calculations without proper caching mechanism
      - Search queries without proper validation and sanitization
      - Geographical queries without spatial optimization
      - Airport data modifications without proper audit logging
      - Exposing sensitive airport operational data to unauthorized users
      - Allowing unauthorized modifications to airport fee structures
      - Airport search without proper rate limiting protection
      depends_on:
      - aircraft: Aircraft positioning and home base relationships
      - booking: Departure and arrival airport validation for trips and quotes
      - pricing: Airport fee integration for comprehensive cost calculations
      - data_enrichment: Automated airport metadata updates and quality management
      - external_geodesic: Distance calculation library (geopy) for accurate measurements
      - external_spatial_db: PostgreSQL spatial extensions for geographical queries
      provides:
      - airport_search: Intelligent airport search with relevance ranking
      - distance_calculation: Accurate inter-airport distance calculations with caching
      - fee_management: Comprehensive airport fee structure and dynamic pricing
      - slot_availability: Real-time slot and operational restriction checking
      - geographical_queries: Spatial airport discovery and radius-based searches
      - airport_metadata: Comprehensive airport operational data and facilities
      - route_optimization: Airport alternatives and routing support for trip planning
      - data_validation: Airport data quality and validation services
      enforcement_hooks: null
      pre_airport_create:
      - validate_icao_code_format - Ensure ICAO code is 4 uppercase characters
      - validate_geographical_coordinates - Check coordinates are within valid ranges
      - check_duplicate_codes - Prevent duplicate ICAO/IATA codes
      - validate_fee_structure - Ensure fees are non-negative and reasonable
      pre_airport_update:
      - validate_updated_fields - Ensure all updates maintain data integrity
      - maintain_referential_integrity - Preserve relationships with aircraft and flights
      - update_enrichment_tracking - Track data modification sources and timestamps
      pre_search:
      - validate_search_parameters - Sanitize and validate search inputs
      - optimize_query_performance - Ensure efficient database queries
      - apply_rate_limiting - Prevent search abuse and system overload
      post_distance_calculation:
      - cache_distance_result - Store calculated distances for future use
      - update_usage_statistics - Track calculation frequency and patterns
      - monitor_cache_performance - Ensure cache hit ratios meet targets
      post_search:
      - log_search_analytics - Track search patterns and performance
      - update_search_rankings - Improve relevance based on usage patterns
      data_quality_checks:
      - continuous_data_validation - Regular validation of airport data integrity
      - enrichment_status_monitoring - Track data enrichment success and failures
      - geographical_accuracy_checks - Validate coordinate accuracy and consistency
      scheduled_maintenance:
      - cleanup_expired_cache - Remove old distance calculations and cache entries
      - update_airport_metadata - Refresh airport operational information
      - validate_system_integrity - Check for data inconsistencies and repair
      security: null
      airport_data_access:
      - Restrict airport creation/update to admin users only
      - Limit sensitive operational data access to authenticated users
      - Implement role-based access for airport management functions
      - Control access to fee information based on user permissions
      api_security:
      - Rate limiting on all airport search and query endpoints
      - Input validation and sanitization for all search parameters
      - SQL injection prevention in geographical and search queries
      - Authentication required for all airport modification operations
      data_protection:
      - Airport operational data encrypted at rest in database
      - Sensitive fee information access logged and monitored
      - Geographical queries rate limited to prevent abuse
      - Airport metadata access controlled by user authorization levels
      audit_logging:
      - Airport data modifications logged with user context and timestamps
      - Search queries logged for analytics and security monitoring
      - Distance calculations tracked for usage monitoring and optimization
      - Fee access logged for billing and compliance purposes
      - Geographical queries monitored for unusual patterns
      performance_contracts:
        airport_search: <1000ms response time for 95th percentile
        distance_calculation: <500ms with caching, <2000ms without cache
        geographical_queries: <800ms with spatial indexing
        fee_retrieval: <200ms for cached data, <500ms for database queries
        slot_availability: <300ms with operational context
        database_operations: <200ms for single airport queries
        batch_operations: <2000ms for up to 100 airports
        cache_hit_ratio: '>90% for distance calculations'
        search_accuracy: '>95% relevance for code-based searches'
        data_freshness: <24 hours for enrichment updates
      monitoring:
        metrics:
        - Airport search query volume, performance, and success rates
        - Distance calculation cache hit rates and performance
        - Geographical query response times and spatial index efficiency
        - Fee retrieval frequency, patterns, and cache performance
        - Slot availability check volume and operational accuracy
        - Data enrichment success rates and source reliability
        - API endpoint usage, error rates, and rate limiting effectiveness
        - Database query performance and spatial index utilization
        alerts:
        - Airport search response time > 1500ms
        - Distance calculation cache miss rate > 20%
        - Geographical query errors > 5%
        - Airport data enrichment failures > 10%
        - Invalid airport code submissions > 50/hour
        - Fee calculation errors or inconsistencies detected
        - Spatial index performance degradation
        - Rate limiting threshold breaches
        dashboards:
        - Airport domain performance overview with key metrics
        - Search and discovery usage patterns and optimization opportunities
        - Distance calculation efficiency and cache performance metrics
        - Data quality and enrichment status with source reliability
        - Geographical coverage, accuracy, and spatial query performance
        - Fee management and pricing trends analysis
        - API usage patterns and security monitoring
      domain: airport
      subdomain: null
      path: airport/airport.yaml
      file_size_kb: 13.73
    search_discovery:
      system: airport_search_discovery
      description: Airport Search and Discovery Subsystem - Intelligent airport search with relevance ranking, geographical
        proximity, fuzzy matching, and multi-criteria filtering for optimal route planning and user experience
      intent_assertions:
      - Intelligent airport search with ICAO/IATA code prioritization and exact match handling
      - Fuzzy matching for airport names, cities, and countries with typo tolerance
      - Geographical proximity-based ranking for location-aware search results
      - Region-based filtering for localized and targeted search results
      - Multi-criteria search supporting partial matches and advanced filtering
      - Real-time search performance with sub-1000ms response times
      - Integration with booking system for departure/arrival validation
      technical_assertions:
      - path: /app/api/v1/endpoints/aircraft/airports.py
        desc: Airport search API endpoints with intelligent ranking and filtering
        critical: true
        key_endpoints:
        - GET /airports - Search airports with intelligent ranking
        - GET /airports/{icao_code} - Get detailed airport information
      - path: /app/db/manager/repositories/airport_repository.py
        desc: Airport search repository with optimized queries and ranking
        critical: true
        key_methods:
        - search_airports with intelligent ranking and filtering
        - get_airport with multiple search criteria
      - path: /app/services/airport_service.py
        desc: Airport search service with business logic and validation
        critical: true
        key_methods:
        - search_airports with relevance ranking
        - get_airport with validation and error handling
      behavior: null
      intelligent_search_workflow:
      - Prioritize exact ICAO/IATA code matches for precise airport identification
      - Apply fuzzy matching for airport names with typo tolerance and partial matching
      - Rank results by geographical proximity when location context is available
      - Filter by region for localized search results and geographical constraints
      - Support multi-criteria search with advanced filtering options
      search_optimization:
      - Cache frequently searched airports for improved performance
      - Use database indexing for ICAO/IATA codes and geographical coordinates
      - Implement intelligent query optimization based on search patterns
      - Provide search suggestions and autocomplete functionality
      invariants:
      - Search queries must be validated and sanitized before processing
      - ICAO/IATA code searches must prioritize exact matches
      - Geographical searches must use proper spatial indexing
      - Search results must be ranked by relevance and proximity
      - Response times must be under 1000ms for 95th percentile
      forbidden_states:
      - Search queries without proper input validation
      - ICAO/IATA code searches without exact match prioritization
      - Geographical searches without spatial optimization
      - Search results without proper ranking and relevance scoring
      - Search operations without proper rate limiting
      depends_on:
      - airport: Airport data and metadata for search operations
      - external_spatial_db: PostgreSQL spatial extensions for geographical searches
      provides:
      - intelligent_search: Smart airport search with relevance ranking
      - geographical_discovery: Location-based airport discovery and proximity search
      - search_optimization: Optimized search performance and caching
      - search_validation: Input validation and sanitization for search queries
      enforcement_hooks: null
      pre_search:
      - validate_search_parameters - Sanitize and validate all search inputs
      - optimize_search_query - Ensure efficient database queries and indexing
      - apply_rate_limiting - Prevent search abuse and system overload
      post_search:
      - log_search_analytics - Track search patterns and performance metrics
      - update_search_cache - Cache frequently searched results
      - monitor_search_performance - Track response times and optimization opportunities
      security: null
      search_security:
      - Input validation and sanitization for all search parameters
      - Rate limiting on search endpoints to prevent abuse
      - SQL injection prevention in search queries
      - Search query logging for security monitoring
      performance_contracts:
        search_response_time: <1000ms for 95th percentile
        exact_match_search: <200ms for ICAO/IATA code searches
        fuzzy_search: <800ms for name-based searches
        geographical_search: <800ms with spatial indexing
        search_accuracy: '>95% relevance for code-based searches'
      domain: airport
      subdomain: search_discovery
      path: airport/search_discovery.yaml
      file_size_kb: 4.36
    distance_routing:
      system: airport_distance_routing
      description: Airport Distance and Routing Subsystem - Accurate inter-airport distance calculations with caching optimization,
        geographical queries, route planning support, and spatial indexing for efficient private jet route optimization
      intent_assertions:
      - Accurate geodesic distance calculations between airports using geopy library
      - Distance caching in AirportDistance table for frequently requested routes
      - Batch distance calculations for route optimization and planning
      - Spatial indexing for efficient geographical queries and radius searches
      - Alternative airport suggestions based on proximity and capabilities
      - Integration with pricing service for comprehensive cost calculations
      - Support for route optimization and multi-leg trip planning
      technical_assertions:
      - path: /app/api/v1/endpoints/aircraft/airports.py
        desc: Distance calculation API endpoints with caching and optimization
        critical: true
        key_endpoints:
        - POST /airports/distance - Calculate distance between airports
        - GET /airports/radius/{lat}/{lon} - Find airports within radius
      - path: /app/db/manager/repositories/airport_repository.py
        desc: Distance calculation repository with caching and spatial queries
        critical: true
        key_methods:
        - calculate_distance with geodesic calculations and caching
        - find_airports_in_radius with spatial indexing
      - path: /app/services/airport_service.py
        desc: Distance calculation service with business logic and optimization
        critical: true
        key_methods:
        - calculate_distance with caching optimization
        - find_airports_in_radius with spatial queries
      - path: /app/db/models/airport.py
        desc: AirportDistance model for caching distance calculations
        critical: true
        key_features:
        - Distance caching with origin/destination UUID references
        - Spatial indexing for geographical coordinates
      behavior: null
      distance_calculation_workflow:
      - Calculate geodesic distance using geopy library with nautical mile conversion
      - Check AirportDistance cache before performing new calculations
      - Store calculated distances in cache for future use and performance optimization
      - Support batch distance calculations for route optimization scenarios
      - Validate airport existence before performing distance calculations
      geographical_queries:
      - Find airports within specified radius using spatial indexing
      - Calculate distances to multiple airports for proximity-based searches
      - Support coordinate-based searches for geographical route planning
      - Provide nearest airport detection for aircraft positioning
      route_optimization:
      - Generate alternative airport suggestions based on proximity
      - Support multi-leg trip planning with distance calculations
      - Integration with pricing service for cost-optimized route planning
      - Provide route alternatives for operational flexibility
      invariants:
      - Distance calculations must use geodesic formulas for accuracy
      - All calculated distances must be cached for performance optimization
      - Geographical queries must use proper spatial indexing
      - Distance cache hit ratio must exceed 90% for common routes
      - Airport existence must be validated before distance calculations
      forbidden_states:
      - Distance calculations without proper caching mechanism
      - Geographical queries without spatial optimization
      - Distance calculations between non-existent airports
      - Cache storage without proper indexing and retrieval
      - Batch operations without performance optimization
      depends_on:
      - airport: Airport data and coordinates for distance calculations
      - external_geodesic: Geopy library for accurate distance calculations
      - external_spatial_db: PostgreSQL spatial extensions for geographical queries
      provides:
      - distance_calculation: Accurate inter-airport distance calculations with caching
      - geographical_queries: Spatial airport discovery and radius-based searches
      - route_optimization: Airport alternatives and routing support for trip planning
      - proximity_search: Nearest airport detection and proximity-based discovery
      enforcement_hooks: null
      pre_distance_calculation:
      - validate_airport_existence - Ensure both airports exist before calculation
      - check_distance_cache - Look for existing cached distance first
      - validate_coordinates - Ensure airport coordinates are valid
      post_distance_calculation:
      - cache_distance_result - Store calculated distance for future use
      - update_usage_statistics - Track calculation frequency and patterns
      - monitor_cache_performance - Ensure cache hit ratios meet targets
      pre_geographical_query:
      - validate_coordinates - Ensure query coordinates are within valid ranges
      - optimize_spatial_query - Use proper spatial indexing for performance
      - apply_radius_limits - Enforce reasonable radius limits
      post_geographical_query:
      - log_query_performance - Track spatial query response times
      - update_spatial_statistics - Monitor spatial index efficiency
      security: null
      distance_security:
      - Validate all coordinate inputs to prevent injection attacks
      - Rate limiting on distance calculation endpoints
      - Authentication required for batch distance operations
      - Audit logging for distance calculation usage
      geographical_security:
      - Coordinate validation for geographical queries
      - Radius limits to prevent resource abuse
      - Rate limiting on spatial query endpoints
      - Query logging for security monitoring
      performance_contracts:
        distance_calculation: <500ms with caching, <2000ms without cache
        geographical_queries: <800ms with spatial indexing
        batch_distance_calculations: <2000ms for up to 100 airport pairs
        cache_hit_ratio: '>90% for frequently requested routes'
        spatial_query_efficiency: <800ms for radius searches
      domain: airport
      subdomain: distance_routing
      path: airport/distance_routing.yaml
      file_size_kb: 5.68
  communication:
    main:
      system: communication
      description: Multi-channel customer and operator communication, notification orchestration, and message delivery across
        the charter aviation platform
      intent_assertions:
      - Enable reliable multi-channel communication between customers, operators, and internal systems
      - Provide real-time notification orchestration with delivery tracking and webhook integration
      - Maintain comprehensive message threading and conversation history for all stakeholder interactions
      - Support intelligent template management with personalization and A/B testing capabilities
      - Ensure automated workflow triggers for booking lifecycle, flight operations, and customer service
      - Deliver consistent agent persona management across all operator communications
      - Provide robust email delivery infrastructure with fallback providers and error recovery
      - Enable real-time chat interface with AI-powered intent parsing and response generation
      technical_assertions:
        communication_channels:
          email_infrastructure:
          - Primary email delivery via Mailgun API with webhook-based delivery tracking
          - Fallback email providers (Mailtrap, SMTP) for development and redundancy
          - Template-based email rendering with Jinja2 engine and database template storage
          - Email delivery logging with comprehensive webhook event processing
          - 'Performance monitoring: <3s email send time, >99% delivery rate'
          real_time_messaging:
          - WebSocket-based chat interface with session management and message persistence
          - AI-powered intent parsing with confidence scoring and follow-up generation
          - Agent persona rotation for consistent operator communication styles
          - Message threading with conversation context and attachment support
          - Real-time typing indicators and message status updates
          notification_orchestration:
          - Multi-channel notification delivery (email, SMS, push, in-app, Nostr)
          - User preference management with granular notification type controls
          - Scheduled notification processing with retry logic and failure handling
          - Priority-based notification queuing with immediate delivery for critical alerts
          - Notification analytics with open rates, click tracking, and engagement metrics
          webhook_integration:
          - Mailgun webhook processing for delivery, open, click, bounce, and complaint events
          - Gmail webhook integration for inbound email processing and thread management
          - BTCPay webhook processing for payment notification integration
          - Webhook signature verification and security enforcement
          - Event-driven architecture with real-time status updates
        message_threading:
          conversation_management:
          - Thread-based conversation tracking with operator and customer contexts
          - Message direction tracking (inbound/outbound) with metadata preservation
          - Quote extraction from operator emails using AI-powered content analysis
          - Attachment handling with file storage and metadata tracking
          - Thread status management (active, archived, completed) with lifecycle automation
          operator_communication:
          - Persona-driven communication with formal, friendly, and technical styles
          - Automated follow-up generation based on thread activity and response patterns
          - Quote request orchestration with template-based operator outreach
          - Response tracking with confidence scoring and quote extraction
          - Reliability scoring based on response time and communication quality
          customer_engagement:
          - Booking lifecycle notifications with status updates and flight information
          - Personalized communication based on user preferences and travel history
          - Emergency notification protocols for flight operations and schedule changes
          - Marketing communication with unsubscribe management and preference controls
          - Support ticket integration with escalation and resolution tracking
        template_management:
          email_templates:
          - Database-driven template storage with version control and A/B testing
          - Jinja2 template rendering with context data validation and error handling
          - Template performance analytics with open rates, click rates, and conversion tracking
          - Fallback template system for reliability during template errors
          - Template variable validation and dynamic content personalization
          content_personalization:
          - Dynamic content insertion based on user profile and booking context
          - Agent persona integration for consistent communication tone and style
          - Multilingual template support with locale-based content selection
          - Brand consistency enforcement with standardized styling and messaging
          - Template usage analytics with performance optimization recommendations
      behavior:
        notification_lifecycle:
          creation_and_scheduling:
          - Notification creation with type classification and priority assignment
          - Scheduled delivery processing with timezone awareness and user preferences
          - Batch notification processing for system-wide announcements and updates
          - Template selection and context data preparation for personalized content
          - Channel selection based on user preferences and notification urgency
          delivery_and_tracking:
          - Multi-provider email delivery with automatic failover and retry logic
          - Real-time delivery status tracking via webhook integration and event processing
          - Delivery confirmation with timestamp recording and status updates
          - Bounce handling with automatic list management and sender reputation protection
          - Engagement tracking with open rates, click tracking, and user interaction analytics
          error_handling_and_recovery:
          - Failed notification retry with exponential backoff and maximum attempt limits
          - Provider failover for email delivery issues with automatic provider switching
          - Error logging with detailed failure analysis and resolution recommendations
          - Dead letter queue management for permanently failed notifications
          - Performance monitoring with alerting for delivery rate degradation
        communication_workflows:
          booking_lifecycle_integration:
          - Automated booking confirmation emails with flight details and payment information
          - Status update notifications for booking modifications, cancellations, and confirmations
          - Pre-flight reminders with check-in instructions and contact information
          - Post-flight follow-up with feedback collection and future booking incentives
          - Emergency communication protocols for flight delays, cancellations, and weather events
          operator_coordination:
          - Quote request automation with template-based operator outreach and follow-up
          - Response processing with AI-powered quote extraction and confidence scoring
          - Performance tracking with response time analytics and reliability scoring
          - Escalation protocols for non-responsive operators with alternative sourcing
          - Relationship management with communication history and preference tracking
          customer_service_integration:
          - Support ticket creation from inbound emails with automatic categorization
          - Escalation workflows with priority-based routing and response time tracking
          - Knowledge base integration with automated response suggestions
          - Satisfaction surveys with feedback collection and service improvement analytics
          - Live chat integration with agent handoff and conversation continuity
        real_time_communication:
          chat_interface:
          - WebSocket connection management with session persistence and reconnection handling
          - Message queuing with offline message delivery and synchronization
          - Typing indicators and read receipts for enhanced user experience
          - File sharing capabilities with security scanning and storage management
          - Chat history preservation with search functionality and export capabilities
          ai_integration:
          - Intent parsing with natural language understanding and confidence scoring
          - Automated response generation with context awareness and personalization
          - Escalation detection with human agent handoff and conversation transfer
          - Learning capabilities with conversation analysis and response optimization
          - Multi-language support with automatic translation and locale detection
      invariants:
      - All email delivery must be tracked with webhook-based status updates and comprehensive logging
      - Message threads must maintain conversation continuity with proper direction tracking and metadata
      - Template rendering must have fallback mechanisms to prevent communication failures
      - User notification preferences must be respected across all communication channels
      - Webhook signatures must be verified for all external service integrations
      - Communication workflows must integrate with booking lifecycle and operational events
      - Agent persona consistency must be maintained across all operator communications
      - Real-time chat sessions must persist conversation history and context
      forbidden_states:
      - Email delivery without proper webhook tracking and status monitoring
      - Template rendering failures without fallback content delivery
      - Notification delivery that ignores user preference settings
      - Webhook processing without signature verification and security validation
      - Communication workflows that operate independently of booking lifecycle events
      - Message threading without proper conversation context and metadata preservation
      - Operator communication without persona consistency and relationship tracking
      - Chat sessions without proper session management and message persistence
      depends_on:
      - booking
      - authentication
      - core
      - operator
      - aircraft
      provides:
      - Multi-channel notification delivery infrastructure
      - Template-based email communication system
      - Real-time chat interface with AI integration
      - Webhook-based delivery tracking and analytics
      - Message threading and conversation management
      - Agent persona management for operator communications
      - Communication workflow automation and integration
      - Performance monitoring and analytics dashboard
      files:
        api_endpoints:
          communication_endpoints:
          - file: app/api/v1/endpoints/communication/notifications.py
            lines: 200
            description: Notification management API with user preferences and delivery tracking
            endpoints: 6
            features:
            - notification creation
            - preference management
            - delivery status
            - analytics
          - file: app/api/v1/endpoints/communication/chat.py
            lines: 381
            description: Real-time chat API with WebSocket support and AI integration
            endpoints: 8
            features:
            - message processing
            - session management
            - history retrieval
            - persona management
          - file: app/api/v1/endpoints/communication/chat_ws.py
            lines: 245
            description: WebSocket chat interface with real-time messaging capabilities
            endpoints: 1
            features:
            - WebSocket connections
            - real-time messaging
            - session persistence
          - file: app/api/v1/endpoints/communication/email_providers.py
            lines: 215
            description: Email provider management and testing API
            endpoints: 4
            features:
            - provider status
            - configuration testing
            - failover management
          - file: app/api/v1/endpoints/communication/mailgun_webhook.py
            lines: 205
            description: Mailgun webhook processing for email delivery tracking
            endpoints: 3
            features:
            - delivery tracking
            - event processing
            - batch processing
          - file: app/api/v1/endpoints/communication/gmail_webhook.py
            lines: 141
            description: Gmail webhook integration for inbound email processing
            endpoints: 2
            features:
            - inbound email processing
            - thread management
          - file: app/api/v1/endpoints/communication/feedback.py
            lines: 33
            description: Customer feedback collection and management
            endpoints: 1
            features:
            - feedback submission
            - rating collection
        core_services:
          communication_services:
          - file: app/services/communication_service.py
            lines: 486
            description: Primary communication orchestration service
            methods: 12
            features:
            - passenger notifications
            - operator notifications
            - system notifications
            - history management
          - file: app/services/email_service.py
            lines: 1476
            description: Email delivery service with multi-provider support
            methods: 25
            features:
            - multi-provider delivery
            - template integration
            - webhook tracking
            - fallback handling
          - file: app/services/notifications_service.py
            lines: 386
            description: Multi-channel notification delivery service
            methods: 15
            features:
            - channel management
            - preference handling
            - delivery tracking
            - analytics
          - file: app/services/template_service.py
            lines: 1200
            description: Template management and rendering service
            methods: 20
            features:
            - template rendering
            - database storage
            - performance analytics
            - fallback handling
          - file: app/services/email_threads_service.py
            lines: 613
            description: Email thread management with AI-powered conversation tracking
            methods: 18
            features:
            - thread management
            - follow-up generation
            - quote extraction
            - persona rotation
          - file: app/services/operator_communication_service.py
            lines: 302
            description: Specialized operator communication with persona management
            methods: 8
            features:
            - quote requests
            - persona management
            - response tracking
            - reliability scoring
          - file: app/services/mailgun_webhook_service.py
            lines: 342
            description: Mailgun webhook event processing service
            methods: 12
            features:
            - event processing
            - signature verification
            - analytics updates
            - error handling
        email_providers:
          provider_implementations:
          - file: app/services/email_providers/mailgun_provider.py
            lines: 245
            description: Production Mailgun email provider implementation
            features:
            - API integration
            - webhook support
            - delivery tracking
            - error handling
          - file: app/services/email_providers/mailtrap_provider.py
            lines: 180
            description: Development Mailtrap email provider for testing
            features:
            - testing environment
            - email capture
            - debugging tools
          - file: app/services/email_providers/smtp_provider.py
            lines: 165
            description: Generic SMTP provider for fallback delivery
            features:
            - SMTP protocol
            - fallback delivery
            - configuration flexibility
          - file: app/services/email_providers/__init__.py
            lines: 95
            description: Email provider factory and management
            features:
            - provider selection
            - configuration management
            - failover logic
        database_models:
          communication_models:
          - file: app/db/models/message_thread.py
            lines: 180
            description: Message thread and conversation tracking models
            models:
            - MessageThread
            - ThreadMessage
            features:
            - conversation tracking
            - metadata storage
            - status management
            - persona tracking
          - file: app/db/models/notification.py
            lines: 113
            description: Notification and preference management models
            models:
            - Notification
            - NotificationPreference
            features:
            - multi-channel notifications
            - user preferences
            - delivery tracking
            - analytics
          - file: app/db/models/email_template.py
            lines: 156
            description: Email template storage and management model
            models:
            - EmailTemplate
            features:
            - template versioning
            - performance analytics
            - variable tracking
          - file: app/db/models/email_sent_log.py
            lines: 98
            description: Email delivery logging and tracking model
            models:
            - EmailSentLog
            features:
            - delivery tracking
            - webhook events
            - performance metrics
          - file: app/db/models/chat.py
            lines: 187
            description: Chat session and message models
            models:
            - ChatSession
            - ChatMessage
            features:
            - session management
            - message persistence
            - AI integration
        schemas:
          communication_schemas:
          - file: app/db/schemas/communication.py
            lines: 58
            description: Communication service request/response schemas
            schemas:
            - NotificationRequest
            - NotificationResponse
            - CommunicationHistory
            features:
            - request validation
            - response formatting
            - type safety
          - file: app/db/schemas/message_thread.py
            lines: 245
            description: Message thread and conversation schemas
            schemas:
            - MessageThread
            - ThreadMessage
            - MessageThreadCreate
            features:
            - thread management
            - message tracking
            - relationship handling
          - file: app/db/schemas/notification.py
            lines: 178
            description: Notification and preference schemas
            schemas:
            - Notification
            - NotificationPreference
            - NotificationCreate
            features:
            - notification management
            - preference handling
            - validation
          - file: app/db/schemas/email_template.py
            lines: 198
            description: Email template management schemas
            schemas:
            - EmailTemplate
            - EmailTemplateCreate
            - EmailTemplateUpdate
            features:
            - template management
            - version control
            - analytics tracking
          - file: app/db/schemas/email_sent_log.py
            lines: 138
            description: Email delivery tracking schemas
            schemas:
            - EmailSentLog
            - EmailEventUpdate
            - EmailSentLogWithStatus
            features:
            - delivery tracking
            - webhook processing
            - status management
        repositories:
          communication_repositories:
          - file: app/db/manager/repositories/message_thread_repository.py
            lines: 445
            description: Message thread data access with conversation management
            methods: 18
            features:
            - thread CRUD
            - message management
            - search functionality
            - analytics
          - file: app/db/manager/repositories/notification_repository.py
            lines: 312
            description: Notification management repository
            methods: 14
            features:
            - notification CRUD
            - preference management
            - delivery tracking
            - analytics
          - file: app/db/manager/repositories/email_template_repository.py
            lines: 298
            description: Email template management repository
            methods: 12
            features:
            - template CRUD
            - performance tracking
            - version management
            - analytics
          - file: app/db/manager/repositories/email_sent_log_repository.py
            lines: 267
            description: Email delivery tracking repository
            methods: 15
            features:
            - delivery logging
            - webhook processing
            - analytics
            - reporting
          - file: app/db/manager/repositories/chat_repository.py
            lines: 234
            description: Chat session and message management repository
            methods: 11
            features:
            - session management
            - message persistence
            - search functionality
        templates:
          email_templates:
          - directory: app/templates/passenger/
            description: Customer-facing email templates
            templates:
            - booking_confirmation
            - status_updates
            - pre_flight_reminders
            - post_flight_follow_up
            features:
            - personalization
            - branding
            - responsive design
          - directory: app/templates/operator/
            description: Operator communication templates
            templates:
            - quote_requests
            - booking_notifications
            - performance_updates
            - relationship_management
            features:
            - persona-driven content
            - professional formatting
            - attachment support
          - directory: app/templates/system/
            description: System notification templates
            templates:
            - alerts
            - maintenance_notifications
            - performance_reports
            - error_notifications
            features:
            - technical formatting
            - severity indicators
            - action items
          - directory: app/templates/auth/
            description: Authentication and security templates
            templates:
            - login_codes
            - registration_welcome
            - password_reset
            - security_alerts
            features:
            - security compliance
            - clear instructions
            - brand consistency
        legacy_services:
          legacy_communication:
          - file: app/services/legacy_services/communication.py
            lines: 416
            description: Legacy communication service implementation
            methods: 10
            features:
            - backward compatibility
            - migration support
            - legacy API integration
          - file: app/services/legacy_services/notifications.py
            lines: 373
            description: Legacy notification service implementation
            methods: 12
            features:
            - legacy notification handling
            - migration utilities
            - compatibility layer
          - file: app/services/legacy_services/email_threads.py
            lines: 710
            description: Legacy email thread management implementation
            methods: 15
            features:
            - thread migration
            - legacy data handling
            - compatibility support
      endpoints:
        notification_management:
        - path: /api/v1/communication/notifications
          methods:
          - GET
          - POST
          description: User notification management and creation
          response_time: <500ms
          authentication: required
          features:
          - notification listing
          - preference management
          - creation
        - path: /api/v1/communication/notifications/count
          methods:
          - GET
          description: Notification count and unread status
          response_time: <200ms
          authentication: required
          features:
          - count retrieval
          - unread tracking
        - path: /api/v1/communication/notifications/mark-read/{notification_id}
          methods:
          - POST
          description: Mark notification as read
          response_time: <300ms
          authentication: required
          features:
          - status updates
          - read tracking
        - path: /api/v1/communication/notifications/preferences
          methods:
          - POST
          description: Update notification preferences
          response_time: <400ms
          authentication: required
          features:
          - preference management
          - channel configuration
        chat_interface:
        - path: /api/v1/communication/chat
          methods:
          - POST
          description: Process chat messages with AI integration
          response_time: <2s
          authentication: optional
          features:
          - intent parsing
          - AI responses
          - session management
        - path: /api/v1/communication/chat/history/{session_key}
          methods:
          - GET
          description: Retrieve chat conversation history
          response_time: <500ms
          authentication: optional
          features:
          - history retrieval
          - message filtering
        - path: /api/v1/communication/chat/clear/{session_key}
          methods:
          - POST
          description: Clear chat session and start fresh
          response_time: <300ms
          authentication: optional
          features:
          - session reset
          - history clearing
        - path: /api/v1/communication/chat/persona/{session_key}
          methods:
          - POST
          description: Set AI agent persona for chat session
          response_time: <400ms
          authentication: optional
          features:
          - persona management
          - conversation style
        email_management:
        - path: /api/v1/communication/email-providers/status
          methods:
          - GET
          description: Email provider status and configuration
          response_time: <300ms
          authentication: required
          features:
          - provider status
          - configuration validation
        - path: /api/v1/communication/email-providers/test
          methods:
          - POST
          description: Test email provider configuration
          response_time: <5s
          authentication: required
          features:
          - provider testing
          - configuration validation
        webhook_processing:
        - path: /api/v1/communication/mailgun/events
          methods:
          - POST
          description: Process Mailgun delivery tracking webhooks
          response_time: <1s
          authentication: webhook_signature
          features:
          - delivery tracking
          - event processing
          - analytics updates
        - path: /api/v1/communication/mailgun/webhook
          methods:
          - POST
          description: Process inbound email webhooks from Mailgun
          response_time: <3s
          authentication: api_key
          features:
          - inbound email processing
          - thread management
        - path: /api/v1/communication/gmail/webhook
          methods:
          - POST
          description: Process Gmail webhook notifications
          response_time: <2s
          authentication: oauth
          features:
          - Gmail integration
          - thread synchronization
      database_models:
        message_threading:
          message_threads:
            table: message_threads
            description: Conversation tracking with operator and customer contexts
            key_fields:
            - id
            - subject
            - recipient_email
            - thread_type
            - status
            - agent_persona
            relationships:
            - messages
            - booking
            - operator
            - user
            indexes:
            - recipient_email
            - thread_type
            - status
            - last_message_at
            features:
            - conversation management
            - persona tracking
            - status workflow
          thread_messages:
            table: thread_messages
            description: Individual messages within conversation threads
            key_fields:
            - id
            - thread_id
            - content
            - direction
            - from_email
            - to_email
            relationships:
            - thread
            indexes:
            - thread_id
            - direction
            - received_at
            - message_id
            features:
            - message tracking
            - attachment support
            - quote extraction
        notification_system:
          notifications:
            table: notifications
            description: Multi-channel notification tracking and delivery
            key_fields:
            - id
            - user_id
            - title
            - body
            - notification_type
            - channel
            - status
            relationships:
            - user
            - quote
            - booking
            indexes:
            - user_id
            - notification_type
            - status
            - scheduled_for
            features:
            - multi-channel delivery
            - scheduling
            - tracking
            - analytics
          notification_preferences:
            table: notification_preferences
            description: User preferences for notification delivery
            key_fields:
            - id
            - user_id
            - notification_type
            - channel
            - enabled
            relationships:
            - user
            indexes:
            - user_id
            - notification_type
            - channel
            constraints:
            - unique(user_id, notification_type, channel)
            features:
            - preference management
            - channel configuration
            - granular controls
        email_infrastructure:
          email_templates:
            table: email_templates
            description: Template storage with performance analytics
            key_fields:
            - id
            - name
            - slug
            - subject
            - file_path
            - template_type
            - is_active
            relationships:
            - email_sent_logs
            indexes:
            - name
            - slug
            - template_type
            - is_active
            features:
            - template management
            - version control
            - performance tracking
          email_sent_logs:
            table: email_sent_logs
            description: Email delivery tracking with webhook event processing
            key_fields:
            - id
            - template_id
            - mailgun_message_id
            - recipient_email
            - subject
            relationships:
            - template
            indexes:
            - mailgun_message_id
            - template_id
            - sent_at
            - recipient_email
            features:
            - delivery tracking
            - webhook processing
            - analytics
            - performance metrics
        chat_system:
          chat_sessions:
            table: chat_sessions
            description: Chat session management with AI integration
            key_fields:
            - id
            - session_key
            - user_id
            - status
            - agent_persona
            relationships:
            - messages
            - user
            indexes:
            - session_key
            - user_id
            - status
            - created_at
            features:
            - session persistence
            - persona management
            - user association
          chat_messages:
            table: chat_messages
            description: Chat message persistence with AI metadata
            key_fields:
            - id
            - session_id
            - content
            - role
            - intent_type
            - confidence
            relationships:
            - session
            indexes:
            - session_id
            - created_at
            - role
            - intent_type
            features:
            - message persistence
            - AI integration
            - intent tracking
      services:
        communication_orchestration:
          communication_service:
            class: CommunicationService
            file: app/services/communication_service.py
            description: Primary communication orchestration with multi-channel delivery
            methods:
            - 'send_passenger_notification(notification: PassengerNotificationRequest) -> NotificationResponse'
            - 'send_operator_notification(notification: OperatorNotificationRequest) -> NotificationResponse'
            - 'send_system_notification(notification: SystemNotificationRequest) -> NotificationResponse'
            - 'get_communication_history(request: CommunicationHistoryRequest) -> CommunicationHistoryResponse'
            features:
            - notification orchestration
            - template integration
            - delivery tracking
            - history management
          email_service:
            class: EmailService
            file: app/services/email_service.py
            description: Multi-provider email delivery with fallback and tracking
            methods:
            - send_email(to_email, subject, html_content, **kwargs) -> Dict[str, Any]
            - send_template_email(to_email, template_identifier, context, subject, **kwargs) -> Dict[str, Any]
            - send_login_code_email(to_email, login_code) -> Dict[str, Any]
            - send_registration_welcome_email(to_email, first_name, last_name, **kwargs) -> Dict[str, Any]
            - 'process_inbound_email(webhook_data: Dict[str, Any]) -> Dict[str, Any]'
            features:
            - multi-provider delivery
            - template integration
            - webhook tracking
            - fallback handling
          notifications_service:
            class: NotificationService
            file: app/services/notifications_service.py
            description: Multi-channel notification delivery with preference management
            methods:
            - create_notification(user_id, notification_type, title, body, **kwargs) -> NotificationSchema
            - 'send_notification(notification_id: UUID) -> Dict[str, Any]'
            - 'process_pending_notifications(max_notifications: int = 50) -> Dict[str, Any]'
            - get_user_notifications(user_id, limit, offset, include_read) -> List[NotificationSchema]
            - set_notification_preferences(user_id, notification_type, channel, enabled, settings) -> NotificationPreference
            features:
            - multi-channel delivery
            - preference management
            - batch processing
            - analytics
        template_management:
          template_service:
            class: TemplateService
            file: app/services/template_service.py
            description: Template rendering and management with database storage
            methods:
            - 'render_template(template_name: str, context: Dict[str, Any]) -> str'
            - create_email_template(name, subject, template_type, file_path, **kwargs) -> Dict[str, Any]
            - 'get_email_templates(page: int = 1, per_page: int = 20) -> Tuple[List[Dict], int]'
            - 'get_email_template(template_id: UUID) -> Optional[Dict[str, Any]]'
            - send_template_email(template_name, recipient_email, context_data, **kwargs) -> Dict[str, Any]
            - 'preview_template(template_name: str, context_data: Dict[str, Any]) -> Dict[str, Any]'
            features:
            - template rendering
            - database storage
            - performance analytics
            - fallback handling
        message_threading:
          email_threads_service:
            class: EmailThreadService
            file: app/services/email_threads_service.py
            description: Email thread management with AI-powered conversation tracking
            methods:
            - 'create_thread(thread_data: MessageThreadCreate) -> Dict[str, Any]'
            - add_message(thread_id, content, direction, from_email, to_email, **kwargs) -> Dict[str, Any]
            - 'get_thread_with_messages(thread_id: UUID) -> Tuple[Dict, List[Dict]]'
            - 'generate_follow_up(thread_id: UUID, context: Optional[Dict] = None) -> Dict[str, Any]'
            - process_scheduled_followups() -> int
            - find_threads_needing_followup(**kwargs) -> List[MessageThread]
            features:
            - thread management
            - AI integration
            - follow-up automation
            - quote extraction
          operator_communication_service:
            class: OperatorCommunicationService
            file: app/services/operator_communication_service.py
            description: Specialized operator communication with persona management
            methods:
            - send_quote_request(operator_id, quote_id, request_details) -> Dict[str, Any]
            - track_operator_response(thread_id, response_data) -> Dict[str, Any]
            - update_operator_reliability(operator_id, response_metrics) -> Dict[str, Any]
            - get_operator_communication_history(operator_id) -> List[Dict[str, Any]]
            features:
            - quote request automation
            - persona management
            - response tracking
            - reliability scoring
        webhook_processing:
          mailgun_webhook_service:
            class: MailgunWebhookService
            file: app/services/mailgun_webhook_service.py
            description: Mailgun webhook event processing with signature verification
            methods:
            - 'process_webhook_event(webhook_data: Dict[str, Any]) -> Dict[str, Any]'
            - update_email_template_stats(template_id, event_type, event_data) -> None
            - update_email_sent_log(message_id, event_type, event_data) -> None
            - 'process_delivery_event(event_data: Dict[str, Any]) -> Dict[str, Any]'
            - 'process_engagement_event(event_data: Dict[str, Any]) -> Dict[str, Any]'
            features:
            - webhook processing
            - signature verification
            - analytics updates
            - event handling
      repositories:
        communication_data_access:
          message_thread_repository:
            class: MessageThreadRepository
            file: app/db/manager/repositories/message_thread_repository.py
            description: Message thread data access with conversation management
            methods:
            - 'create_thread(thread_data: MessageThreadCreate) -> MessageThread'
            - 'add_message(message_data: ThreadMessageCreate) -> ThreadMessage'
            - 'get_thread_by_id(thread_id: UUID) -> Optional[MessageThread]'
            - 'get_thread_with_messages(thread_id: UUID) -> Optional[Tuple[MessageThread, List[ThreadMessage]]]'
            - find_thread(operator_id, booking_id, thread_type) -> Optional[MessageThread]
            - 'update_thread(thread_id: UUID, update_data: Dict) -> MessageThread'
            - 'update_message(message_id: UUID, update_data: Dict) -> ThreadMessage'
            - get_threads_for_entity(entity_type, entity_id, **kwargs) -> List[Tuple[MessageThread, List[ThreadMessage]]]
            features:
            - thread CRUD
            - message management
            - search functionality
            - relationship handling
          notification_repository:
            class: NotificationRepository
            file: app/db/manager/repositories/notification_repository.py
            description: Notification management with preference handling
            methods:
            - 'create(notification_data: NotificationCreate) -> Notification'
            - 'get_by_id(notification_id: UUID) -> Optional[Notification]'
            - get_user_notifications(user_id, limit, offset, include_read) -> List[Notification]
            - 'get_pending_notifications(limit: int = 50) -> List[Notification]'
            - 'update_status(notification_id: UUID, status: NotificationStatusEnum) -> Notification'
            - 'create_preference(preference_data: NotificationPreferenceCreate) -> NotificationPreference'
            - 'get_user_preferences(user_id: UUID) -> List[NotificationPreference]'
            - 'update_preference(preference_id: UUID, preference_data: NotificationPreferenceUpdate) -> NotificationPreference'
            features:
            - notification CRUD
            - preference management
            - status tracking
            - batch operations
          email_template_repository:
            class: EmailTemplateRepository
            file: app/db/manager/repositories/email_template_repository.py
            description: Email template management with performance tracking
            methods:
            - 'create(template_data: EmailTemplateCreate) -> EmailTemplate'
            - 'get_by_id(template_id: UUID) -> Optional[EmailTemplate]'
            - 'get_by_name(name: str) -> Optional[EmailTemplate]'
            - 'get_by_slug(slug: str) -> Optional[EmailTemplate]'
            - list_templates(page, per_page, filters) -> Tuple[List[EmailTemplate], int]
            - 'update(template_id: UUID, template_data: EmailTemplateUpdate) -> EmailTemplate'
            - 'update_stats(template_id: UUID, stat_type: str, increment: int = 1) -> None'
            - 'get_template_analytics(template_id: UUID) -> Dict[str, Any]'
            features:
            - template CRUD
            - performance tracking
            - analytics
            - search functionality
      scheduler_integration:
        communication_automation:
        - task: Process Pending Notifications
          schedule: every 5 minutes
          purpose: Process queued notifications and deliver via appropriate channels
          handler: notifications_service.py:process_pending_notifications
          batch_size: 50
          features:
          - batch processing
          - channel routing
          - delivery tracking
          - error handling
        - task: Retry Failed Notifications
          schedule: every 30 minutes
          purpose: Retry failed notification deliveries with exponential backoff
          handler: notifications_service.py:retry_failed_notifications
          max_retries: 3
          features:
          - retry logic
          - exponential backoff
          - failure analysis
          - dead letter queue
        - task: Process Email Followups
          schedule: every 60 minutes
          purpose: Generate and send automated follow-up emails for operator threads
          handler: email_threads_service.py:process_scheduled_followups
          max_threads: 50
          features:
          - follow-up automation
          - AI generation
          - persona consistency
          - timing optimization
        - task: Update Template Analytics
          schedule: every 15 minutes
          purpose: Process webhook events and update email template performance metrics
          handler: mailgun_webhook_service.py:process_analytics_updates
          batch_size: 100
          features:
          - analytics processing
          - performance tracking
          - webhook integration
          - metrics calculation
        - task: Clean Message History
          schedule: daily 2:00 AM
          purpose: Archive old messages and clean up conversation threads
          handler: message_thread_repository.py:cleanup_old_threads
          retention_days: 365
          features:
          - data cleanup
          - archival
          - performance optimization
          - storage management
      implementation_status:
        completed_features:
          core_infrastructure: 95%
          email_delivery: 90%
          template_management: 85%
          notification_system: 88%
          webhook_integration: 92%
          message_threading: 87%
          chat_interface: 83%
          operator_communication: 89%
        current_gaps:
          sms_integration: SMS delivery not implemented - placeholder methods exist
          push_notifications: Push notification infrastructure not configured
          advanced_analytics: Advanced analytics dashboard and reporting incomplete
          internationalization: Multi-language template support partially implemented
          a_b_testing: Template A/B testing framework not implemented
        planned_features:
          enhanced_ai_integration: Advanced AI conversation analysis and optimization
          marketing_automation: Comprehensive marketing campaign management
          social_media_integration: Social platform communication channels
          voice_communication: Voice call integration and management
          video_communication: Video conferencing integration
        performance_metrics:
          email_delivery_rate: '>99%'
          average_delivery_time: <3 seconds
          webhook_processing_time: <1 second
          template_rendering_time: <500ms
          notification_queue_processing: <5 minutes
          chat_response_time: <2 seconds
        monitoring_and_alerting:
          delivery_rate_monitoring: Real-time monitoring with alerts for <95% delivery rate
          provider_health_checks: Continuous monitoring of email provider availability
          webhook_processing_alerts: Alerts for webhook processing failures or delays
          template_performance_tracking: Performance analytics with optimization recommendations
          conversation_quality_metrics: AI-powered conversation analysis and improvement suggestions
      error_handling:
        communication_failures:
        - error_type: EmailDeliveryFailure
          description: Email delivery failed through primary provider
          recovery: Automatic failover to backup provider with retry logic
          monitoring: Real-time alerts and provider health monitoring
        - error_type: TemplateRenderingError
          description: Template rendering failed due to syntax or data issues
          recovery: Fallback to generic template with error logging and notification
          monitoring: Template error tracking with developer notifications
        - error_type: WebhookSignatureVerificationFailure
          description: Webhook signature verification failed for security
          recovery: Reject webhook with security alert and logging
          monitoring: Security event monitoring with immediate alerts
        - error_type: NotificationPreferenceViolation
          description: Attempted notification delivery violates user preferences
          recovery: Block delivery and log preference violation
          monitoring: Preference compliance monitoring and reporting
        - error_type: ConversationThreadCorruption
          description: Message thread data integrity issues detected
          recovery: Thread reconstruction from message history with data validation
          monitoring: Data integrity monitoring with automatic repair mechanisms
      integration_points:
        booking_lifecycle_integration:
        - trigger: booking_status_change
          action: send_passenger_notification
          template: booking_status_update
          channels:
          - email
          - push
        - trigger: flight_departure
          action: send_tracking_notification
          template: flight_tracking
          channels:
          - email
          - sms
        - trigger: booking_completion
          action: send_feedback_request
          template: post_flight_feedback
          channels:
          - email
        operator_workflow_integration:
        - trigger: quote_request_created
          action: send_operator_notification
          template: quote_request
          persona: formal
        - trigger: operator_response_received
          action: process_quote_extraction
          service: email_threads_service
          ai_integration: true
        - trigger: operator_non_responsive
          action: send_follow_up
          template: follow_up_reminder
          escalation: true
        authentication_integration:
        - trigger: login_code_requested
          action: send_login_code_email
          template: login_code
          priority: high
        - trigger: user_registration
          action: send_welcome_email
          template: registration_welcome
          scheduling: immediate
        - trigger: security_alert
          action: send_security_notification
          template: security_alert
          channels:
          - email
          - sms
      domain: communication
      subdomain: null
      path: communication/communication.yaml
      file_size_kb: 42.93
    chat_chat:
      system: chat
      type: subdomain
      parent_domain: communication
      purpose: Real-time customer support and booking assistance through interactive messaging for charter aviation services
      description: "The chat subdomain provides real-time customer support for booking inquiries, flight modifications, \n\
        and operational questions in the charter aviation industry. It enables instant communication between \ncustomers and\
        \ support teams, facilitates booking assistance, handles urgent flight-related communications, \nand provides seamless\
        \ coordination between passengers, operators, and customer service representatives.\n"
      intent_assertions:
        real_time_support:
        - Provide instant customer support for booking inquiries and flight-related questions
        - Enable real-time communication between customers and support teams
        - Facilitate immediate response to urgent aviation-related requests
        - Support seamless conversation flow with context preservation across sessions
        booking_assistance:
        - Assist customers with booking creation, modification, and status inquiries
        - Provide intelligent booking preferences capture and processing
        - Enable booking status tracking and updates through conversational interface
        - Support multi-step booking workflows with conversation state management
        operational_coordination:
        - Coordinate communication between passengers, operators, and customer service
        - Handle urgent flight-related communications and emergency protocols
        - Provide operational updates and notifications through chat interface
        - Enable escalation workflows for complex operational issues
        ai_powered_assistance:
        - Leverage AI for intent recognition and automated response generation
        - Provide contextual assistance based on conversation history and user preferences
        - Enable intelligent escalation detection and human agent handoff
        - Support multi-language communication and automatic translation capabilities
      technical_assertions:
        websocket_infrastructure:
        - file: app/api/v1/endpoints/communication/chat_ws.py
          lines: 245
          description: WebSocket endpoints for real-time chat events and connection management
          features:
          - connection management
          - event broadcasting
          - session handling
          - real-time messaging
        chat_api_endpoints:
        - file: app/api/v1/endpoints/communication/chat.py
          lines: 381
          description: REST API endpoints for chat session and message management
          features:
          - message processing
          - session management
          - history retrieval
          - persona setting
        chat_services:
        - file: app/services/chat_interface_service.py
          lines: 563
          description: Primary chat interface service with AI integration and session management
          features:
          - message processing
          - intent parsing
          - AI integration
          - session management
        - file: app/services/chat_service.py
          lines: 671
          description: Enhanced chat service with booking workflow integration
          features:
          - booking assistance
          - preference handling
          - workflow integration
          - adaptive learning
        - file: app/services/legacy_services/chat_interface.py
          lines: 542
          description: Legacy chat interface service for backward compatibility
          features:
          - legacy support
          - basic chat functionality
          - session management
        database_models:
        - file: app/db/models/chat.py
          lines: 187
          description: Chat session and message database models
          models:
          - ChatSession
          - ChatMessage
          features:
          - session persistence
          - message storage
          - metadata tracking
          - relationship management
        repository_layer:
        - file: app/db/manager/repositories/chat_session_repository.py
          lines: 315
          description: Chat session data access repository
          features:
          - session CRUD operations
          - activity tracking
          - session lifecycle management
        - file: app/db/manager/repositories/chat_message_repository.py
          lines: 376
          description: Chat message data access repository
          features:
          - message CRUD operations
          - conversation history
          - intent tracking
          - analytics
        schema_definitions:
        - file: app/db/schemas/chat.py
          lines: 89
          description: Database schema definitions for chat entities
          schemas:
          - ChatSession
          - ChatMessage
          - ChatSessionWithMessages
          features:
          - data validation
          - relationship mapping
          - schema evolution
        - file: app/schemas/chat.py
          lines: 547
          description: API schema definitions for chat requests and responses
          schemas:
          - ChatMessageRequest
          - ChatResponse
          - ChatEvent
          - ProcessMessageRequest
          features:
          - API validation
          - request/response modeling
          - event schemas
      behavior:
        real_time_messaging:
          websocket_connection:
            1: Client establishes WebSocket connection with optional authentication token
            2: Server accepts connection and adds to active connections registry
            3: Connection establishment event sent to client with session confirmation
            4: Heartbeat mechanism maintains connection with ping/pong events
          message_processing:
            1: Client sends message via WebSocket with session key and content
            2: Server validates message format and extracts intent using AI parsing
            3: Message stored in database with metadata and intent classification
            4: Response generated using AI service with persona consistency
            5: Response sent to client with attachments and suggested actions
          session_management:
            1: Chat session created or retrieved based on session key
            2: Session context updated with conversation state and user preferences
            3: Message count and activity timestamps maintained automatically
            4: Session persistence ensures conversation continuity across reconnections
        booking_assistance:
          preference_capture:
            1: User expresses booking preferences through natural language
            2: Intent parser extracts structured preference data with confidence scoring
            3: Preferences stored in session context with priority levels
            4: System validates preferences against available options and constraints
            5: Confirmation provided with next steps for booking completion
          booking_workflow:
            1: User initiates booking inquiry through chat interface
            2: System captures flight details (origin, destination, dates, passengers)
            3: Booking preferences collected through conversational flow
            4: System provides aircraft options matching user requirements
            5: Booking creation facilitated with real-time status updates
          status_tracking:
            1: User requests booking status through chat interface
            2: System validates user authentication and booking access
            3: Current booking status retrieved with detailed information
            4: Updates and notifications provided through chat channel
            5: Follow-up actions suggested based on booking state
        operational_coordination:
          escalation_detection:
            1: AI monitors conversation for escalation triggers (urgency, sentiment)
            2: Escalation confidence score calculated based on message analysis
            3: Human agent notification triggered for high-priority cases
            4: Conversation context transferred to agent with full history
            5: Seamless handoff maintained with conversation continuity
          emergency_protocols:
            1: Emergency keywords detected in chat messages
            2: Immediate escalation triggered with priority routing
            3: Operations team notified with real-time alerts
            4: Emergency response procedures initiated automatically
            5: Customer kept informed with regular status updates
        ai_integration:
          intent_recognition:
            1: Message content analyzed using natural language processing
            2: Intent classification performed with confidence scoring
            3: Context-aware intent refinement based on conversation history
            4: Intent data stored for analytics and conversation improvement
          response_generation:
            1: AI service generates contextual responses based on intent and history
            2: Agent persona applied for consistent communication style
            3: Response personalized based on user preferences and booking context
            4: Quality assurance checks performed before delivery
          learning_optimization:
            1: Conversation patterns analyzed for response optimization
            2: User feedback incorporated into AI model improvements
            3: Intent accuracy monitored and refined continuously
            4: Persona effectiveness tracked and optimized
      database_models:
        chat_sessions:
          table: chat_sessions
          description: Chat session management with AI integration and user association
          key_fields:
          - id
          - session_key
          - user_id
          - active
          - current_persona
          - last_activity
          relationships:
          - messages
          - user
          indexes:
          - session_key
          - user_id
          - active
          - last_activity
          features:
          - session persistence
          - persona management
          - activity tracking
          - user association
        chat_messages:
          table: chat_messages
          description: Chat message persistence with AI metadata and intent tracking
          key_fields:
          - id
          - session_id
          - content
          - role
          - intent_type
          - confidence_score
          relationships:
          - session
          indexes:
          - session_id
          - created_at
          - role
          - intent_type
          - requires_followup
          features:
          - message persistence
          - AI integration
          - intent tracking
          - followup management
      services:
        chat_interface_service:
          file: app/services/chat_interface_service.py
          description: Primary chat interface service with AI integration
          methods:
          - process_user_message
          - get_or_create_session
          - add_message
          - clear_chat_session
          features:
          - intent parsing
          - AI response generation
          - session management
          - adaptive learning
          integrations:
          - intent_parser_service
          - agent_persona_service
          - ai_service
          - adaptive_service
        chat_service:
          file: app/services/chat_service.py
          description: Enhanced chat service with booking workflow integration
          methods:
          - create_session
          - _handle_booking_preferences
          - _handle_booking_status
          features:
          - booking assistance
          - preference handling
          - workflow integration
          - context management
          integrations:
          - booking_service
          - template_service
          - communication_service
      repositories:
        chat_session_repository:
          file: app/db/manager/repositories/chat_session_repository.py
          description: Chat session data access and lifecycle management
          methods:
          - create_session
          - get_session_by_key
          - update_session
          - get_session_with_messages
          features:
          - session CRUD
          - activity tracking
          - message relationships
          - cleanup operations
        chat_message_repository:
          file: app/db/manager/repositories/chat_message_repository.py
          description: Chat message data access and conversation management
          methods:
          - create_message
          - update_message
          - get_session_messages
          - get_conversation_history
          features:
          - message CRUD
          - conversation tracking
          - intent analytics
          - history management
      schemas:
        api_schemas:
          file: app/schemas/chat.py
          description: API request and response schemas for chat functionality
          schemas:
          - ChatMessageRequest
          - ChatResponse
          - ChatEvent
          - ProcessMessageRequest
          features:
          - request validation
          - response formatting
          - event modeling
          - attachment support
        database_schemas:
          file: app/db/schemas/chat.py
          description: Database entity schemas for chat models
          schemas:
          - ChatSession
          - ChatMessage
          - ChatSessionWithMessages
          features:
          - data validation
          - relationship mapping
          - schema evolution
          - type safety
      api_endpoints:
        websocket_endpoints:
        - endpoint: ws/chat/ws/{session_key}
          method: WebSocket
          description: Real-time chat WebSocket connection
          parameters:
          - session_key
          - token (optional)
          features:
          - real-time messaging
          - event broadcasting
          - connection management
        rest_endpoints:
        - endpoint: /chat/message
          method: POST
          description: Process user message and generate response
          schema: ProcessMessageRequest -> ProcessMessageResponse
          features:
          - message processing
          - intent recognition
          - AI response generation
        - endpoint: /chat/history/{session_key}
          method: GET
          description: Retrieve chat conversation history
          parameters:
          - session_key
          - limit (optional)
          features:
          - history retrieval
          - pagination
          - message formatting
        - endpoint: /chat/session
          method: POST
          description: Create new chat session
          schema: ChatSessionCreate -> ChatSessionResponse
          features:
          - session creation
          - user association
          - context initialization
        - endpoint: /chat/session/{session_key}/persona
          method: PUT
          description: Set agent persona for chat session
          schema: SetPersonaRequest -> ActionResponse
          features:
          - persona management
          - conversation style
          - agent consistency
        - endpoint: /chat/session/{session_key}/clear
          method: POST
          description: Clear chat session and start fresh
          schema: ClearSessionRequest -> ActionResponse
          features:
          - session reset
          - conversation cleanup
          - fresh start
      integration_points:
        booking_system_integration:
        - trigger: booking_inquiry_intent
          action: initiate_booking_workflow
          service: booking_service
          context: flight_details_capture
        - trigger: booking_status_request
          action: retrieve_booking_information
          service: booking_service
          validation: user_authorization_check
        - trigger: booking_modification_request
          action: process_booking_changes
          service: booking_service
          workflow: modification_approval_flow
        authentication_integration:
        - trigger: authenticated_user_message
          action: associate_session_with_user
          service: auth_service
          context: user_profile_enhancement
        - trigger: login_request_via_chat
          action: initiate_authentication_flow
          service: auth_service
          method: passwordless_login_code
        communication_integration:
        - trigger: escalation_required
          action: notify_human_agents
          service: notification_service
          priority: high
        - trigger: chat_session_summary
          action: generate_conversation_summary
          service: email_service
          template: chat_session_summary
        analytics_integration:
        - trigger: conversation_completed
          action: track_conversation_metrics
          service: analytics_service
          metrics:
          - duration
          - intent_accuracy
          - satisfaction
        - trigger: intent_classification
          action: update_intent_analytics
          service: analytics_service
          data:
          - confidence_scores
          - classification_accuracy
      performance_metrics:
        response_times:
          message_processing: <2 seconds for intent recognition and response generation
          websocket_latency: <100ms for real-time message delivery
          session_creation: <500ms for new session establishment
          history_retrieval: <1 second for conversation history loading
        throughput:
          concurrent_sessions: 1000+ active chat sessions simultaneously
          messages_per_minute: 5000+ messages processed per minute
          websocket_connections: 2000+ concurrent WebSocket connections
        ai_performance:
          intent_accuracy: '>90% intent classification accuracy'
          response_quality: '>85% user satisfaction with AI responses'
          escalation_precision: '>95% accuracy in escalation detection'
          persona_consistency: '>90% consistency in agent persona application'
      monitoring_metrics:
        operational_metrics:
        - Active chat sessions count and duration distribution
        - Message processing rate and queue depth
        - WebSocket connection health and reconnection rates
        - Session creation and termination rates
        quality_metrics:
        - Intent classification accuracy and confidence scores
        - Response generation time and quality ratings
        - Escalation trigger accuracy and false positive rates
        - User satisfaction scores and feedback analysis
        performance_metrics:
        - Message delivery latency and throughput
        - AI service response times and availability
        - Database query performance for chat operations
        - Memory usage and garbage collection in chat services
      automation_workflows:
        session_management:
        - workflow: inactive_session_cleanup
          trigger: daily_maintenance
          action: archive_inactive_sessions
          retention: 30_days_inactive
        - workflow: session_context_optimization
          trigger: session_size_threshold
          action: compress_conversation_context
          threshold: 100_messages
        conversation_analytics:
        - workflow: intent_accuracy_analysis
          trigger: hourly_analysis
          action: analyze_intent_classification_performance
          metrics:
          - accuracy
          - confidence
          - false_positives
        - workflow: conversation_quality_assessment
          trigger: daily_analysis
          action: assess_conversation_outcomes
          metrics:
          - resolution_rate
          - escalation_rate
          - satisfaction
        ai_optimization:
        - workflow: response_quality_improvement
          trigger: weekly_optimization
          action: analyze_response_effectiveness
          optimization: persona_tuning
        - workflow: intent_model_retraining
          trigger: monthly_retraining
          action: retrain_intent_classification_model
          data_source: conversation_history
      implementation_status:
        completed_features:
          core_infrastructure: 90%
          websocket_implementation: 85%
          message_persistence: 95%
          intent_recognition: 80%
          session_management: 90%
          api_endpoints: 85%
        current_gaps:
          advanced_ai_integration: Enhanced conversation AI not fully implemented
          multi_language_support: Translation capabilities not configured
          voice_chat_integration: Voice communication not implemented
          advanced_analytics: Comprehensive conversation analytics incomplete
          mobile_optimization: Mobile-specific chat features not optimized
        planned_features:
          enhanced_ai_capabilities: Advanced conversation AI with improved context understanding
          voice_and_video_chat: Voice and video communication integration
          advanced_personalization: Deep user personalization and preference learning
          conversation_summarization: Automatic conversation summarization and insights
          multi_modal_communication: Image, document, and rich media support
      security_considerations:
        data_protection:
        - All chat messages encrypted in transit and at rest
        - User authentication validated for sensitive operations
        - Session tokens validated and refreshed automatically
        - Personal information masked in logs and analytics
        access_control:
        - Role-based access control for chat administration
        - User authorization verified for session access
        - Agent permissions validated for escalation handling
        - Audit trails maintained for all chat operations
        privacy_compliance:
        - GDPR compliance for conversation data retention
        - User consent managed for conversation analytics
        - Data anonymization for training and optimization
        - Right to deletion honored for conversation history
      scalability_design:
        horizontal_scaling:
        - Chat services can be horizontally scaled independently
        - WebSocket connections distributed across multiple servers
        - Message processing can be load balanced across instances
        - Database read replicas support high-volume chat operations
        performance_optimization:
        - Connection pooling optimized for chat database operations
        - Message caching reduces database load for active conversations
        - Intent classification results cached for similar queries
        - Session state optimized for memory efficiency
        resource_management:
        - Memory usage monitored and optimized for chat services
        - CPU utilization balanced across chat processing tasks
        - Network bandwidth optimized for WebSocket communications
        - Storage growth managed with automated archival policies
      error_handling:
        connection_errors:
        - WebSocket reconnection handled automatically with exponential backoff
        - Connection state preserved during temporary disconnections
        - Message queue maintained for offline message delivery
        - Graceful degradation when real-time features unavailable
        processing_errors:
        - Intent classification failures handled with fallback responses
        - AI service unavailability managed with cached responses
        - Database errors handled with retry logic and circuit breakers
        - Session corruption detected and recovered automatically
        user_experience_errors:
        - Invalid input handled with helpful error messages
        - Session timeout managed with automatic session extension
        - Message delivery failures retried with user notification
        - Escalation failures handled with alternative contact methods
      testing_strategy:
        unit_testing:
        - Chat service methods tested with mocked dependencies
        - Repository layer tested with isolated database transactions
        - Schema validation tested with comprehensive test cases
        - AI integration tested with deterministic mock responses
        integration_testing:
        - WebSocket connections tested with real-time message flows
        - Database operations tested with transaction consistency
        - API endpoints tested with full request/response cycles
        - Service integration tested with end-to-end workflows
        performance_testing:
        - Load testing for concurrent chat sessions and message throughput
        - Stress testing for WebSocket connection limits and stability
        - Endurance testing for long-running chat sessions
        - Scalability testing for horizontal scaling scenarios
      invariants:
      - All chat messages must be persisted with proper session association
      - WebSocket connections must maintain session state consistency
      - Intent classification must include confidence scoring for quality control
      - Agent persona must remain consistent throughout conversation sessions
      - User authentication must be validated for sensitive chat operations
      - Message delivery must be tracked and confirmed for reliability
      - Session management must prevent data loss during connection issues
      - AI responses must be generated within acceptable latency thresholds
      forbidden_states:
      - Chat messages without proper session context and metadata preservation
      - WebSocket connections without authentication validation for sensitive operations
      - Intent classification without confidence scoring and quality metrics
      - AI responses without persona consistency and context awareness
      - Session management without proper cleanup and resource management
      - Message processing without error handling and graceful degradation
      - Conversation data without proper encryption and privacy protection
      - Escalation workflows without human agent notification and handoff procedures
      depends_on:
      - communication
      - authentication
      - booking
      - core
      - analytics
      files:
        api_endpoints:
        - app/api/v1/endpoints/communication/chat_ws.py
        - app/api/v1/endpoints/communication/chat.py
        services:
        - app/services/chat_interface_service.py
        - app/services/chat_service.py
        - app/services/legacy_services/chat_interface.py
        database_models:
        - app/db/models/chat.py
        repositories:
        - app/db/manager/repositories/chat_session_repository.py
        - app/db/manager/repositories/chat_message_repository.py
        schemas:
        - app/db/schemas/chat.py
        - app/schemas/chat.py
        tests:
        - tests/unit/services/test_chat_interface_service.py
        - tests/unit/services/test_chat_service.py
        - tests/unit/repositories/test_chat_session_repository.py
        - tests/unit/repositories/test_chat_message_repository.py
      domain: communication
      subdomain: chat_chat
      path: communication/chat/chat.yaml
      file_size_kb: 23.89
    email_email:
      system: email
      type: subdomain
      parent_domain: communication
      purpose: Transactional emails, booking confirmations, flight notifications, and operational correspondence for charter
        aviation services
      description: "The email subdomain serves as the primary channel for booking confirmations, itinerary delivery, flight\
        \ updates, \npayment receipts, and operational communications in charter aviation. It includes automated transactional\
        \ emails \n(booking confirmations, payment receipts), scheduled notifications (flight reminders, weather alerts),\
        \ and \npersonalized communications (customer service follow-ups, promotional offers) that maintain professional \n\
        relationships with customers and operators.\n"
      intent_assertions:
        transactional_email_delivery:
        - Deliver booking confirmations, payment receipts, and itinerary updates with 99%+ reliability
        - Provide automated transactional emails for all booking lifecycle events
        - Ensure immediate delivery of time-sensitive flight notifications and updates
        - Maintain professional email communication standards for charter aviation industry
        template_management:
        - Manage dynamic email templates with variable substitution and personalization
        - Support passenger, operator, system, and authentication email categories
        - Provide template performance analytics and optimization recommendations
        - Enable A/B testing and template versioning for continuous improvement
        delivery_infrastructure:
        - Implement multi-provider email delivery with automatic failover (Mailgun, Mailtrap, SMTP)
        - Provide webhook-based delivery tracking with real-time status updates
        - Support high-volume email sending with rate limiting and queue management
        - Ensure email deliverability through reputation management and authentication
        analytics_tracking:
        - Track email performance metrics including open rates, click rates, and bounce rates
        - Provide template-level analytics for performance optimization
        - Monitor delivery success rates and provider performance
        - Generate actionable insights for email campaign optimization
      technical_assertions:
        email_providers:
          mailgun_provider:
            file: app/services/email_providers/mailgun_provider.py
            class: MailgunProvider
            purpose: Production email delivery via Mailgun API with webhook support
            features:
            - API delivery
            - webhook tracking
            - batch sending
            - analytics
            configuration:
            - MAILGUN_API_KEY
            - MAILGUN_DOMAIN
            - MAILGUN_WEBHOOK_SIGNING_KEY
          mailtrap_provider:
            file: app/services/email_providers/mailtrap_provider.py
            class: MailtrapProvider
            purpose: Development email testing via Mailtrap API for safe testing
            features:
            - test delivery
            - inbox capture
            - development safety
            - API integration
            configuration:
            - MAILTRAP_API_TOKEN
            - MAILTRAP_INBOX_ID
          smtp_provider:
            file: app/services/email_providers/smtp_provider.py
            class: SMTPProvider
            purpose: Generic SMTP email delivery as fallback option
            features:
            - SMTP protocol
            - TLS encryption
            - authentication
            - fallback delivery
            configuration:
            - FALLBACK_SMTP_SERVER
            - FALLBACK_SMTP_PORT
            - FALLBACK_SMTP_USER
          provider_factory:
            file: app/services/email_providers/factory.py
            class: EmailProviderFactory
            purpose: Provider selection and failover management
            features:
            - provider selection
            - automatic failover
            - configuration validation
            - environment-based routing
        core_services:
          email_service:
            file: app/services/email_service.py
            class: EmailService
            purpose: Primary email orchestration and delivery service
            lines: 1476
            features:
            - provider integration
            - template rendering
            - delivery tracking
            - fallback handling
            methods:
            - send_email(to_email, subject, html_content, **kwargs) -> Dict[str, Any]
            - send_template_email(template_name, context, to_email) -> Dict[str, Any]
            - send_booking_confirmation(booking_data, booking_id) -> Dict[str, Any]
            - send_login_code_email(to_email, login_code) -> Dict[str, Any]
            - send_registration_welcome_email(user_data) -> Dict[str, Any]
          template_service:
            file: app/services/template_service.py
            class: TemplateService
            purpose: Email template management and rendering with analytics
            lines: 1200
            features:
            - template CRUD
            - Jinja2 rendering
            - performance analytics
            - variable validation
            methods:
            - render_template(template_name, context) -> str
            - get_email_templates(page, per_page) -> Tuple[List[Dict], int]
            - create_email_template(template_data) -> Dict[str, Any]
            - get_template_usage_stats(template_id) -> Dict[str, Any]
          mailgun_webhook_service:
            file: app/services/mailgun_webhook_service.py
            class: MailgunWebhookService
            purpose: Webhook event processing for email delivery tracking
            lines: 342
            features:
            - webhook processing
            - signature verification
            - event tracking
            - analytics updates
            methods:
            - process_webhook_event(webhook_data) -> Dict[str, Any]
            - _update_template_statistics(template_id, event_type) -> None
            - _verify_webhook_signature(webhook_data) -> None
        database_models:
          email_templates:
            file: app/db/models/email.py
            table: email_templates
            purpose: Store email template metadata and performance statistics
            fields:
            - 'id: UUID (Primary Key)'
            - 'name: String (Unique template name)'
            - 'slug: String (URL-friendly identifier)'
            - 'file_path: String (Template file location)'
            - 'subject: String (Email subject template)'
            - 'description: Text (Template purpose description)'
            - 'template_type: String (passenger, operator, system, general)'
            - 'variables: JSONB (Available template variables)'
            - 'is_active: Boolean (Template status)'
            - 'usage_count: Integer (Total emails sent)'
            - 'open_count: Integer (Email opens)'
            - 'click_count: Integer (Link clicks)'
            - 'bounce_count: Integer (Email bounces)'
            - 'complained_count: Integer (Spam complaints)'
            - 'unsubscribed_count: Integer (Unsubscribes)'
            - 'last_sent_at: DateTime (Last usage timestamp)'
          email_sent_log:
            file: app/db/models/email.py
            table: email_sent_log
            purpose: Track individual sent emails for webhook processing and analytics
            fields:
            - 'id: UUID (Primary Key)'
            - 'template_id: UUID (Foreign Key to email_templates)'
            - 'mailgun_message_id: String (Unique provider message ID)'
            - 'recipient_email: String (Recipient address)'
            - 'recipient_name: String (Recipient display name)'
            - 'subject: String (Actual email subject sent)'
            - 'sent_at: DateTime (Send timestamp)'
            - 'delivered_at: DateTime (Delivery timestamp)'
            - 'opened_at: DateTime (First open timestamp)'
            - 'clicked_at: DateTime (First click timestamp)'
            - 'bounced_at: DateTime (Bounce timestamp)'
            - 'complained_at: DateTime (Spam complaint timestamp)'
            - 'unsubscribed_at: DateTime (Unsubscribe timestamp)'
            - 'bounce_reason: Text (Bounce error details)'
            - 'user_agent: String (Client user agent)'
            - 'ip_address: String (Client IP address)'
            - 'email_metadata: JSONB (Additional tracking data)'
        repositories:
          email_template_repository:
            file: app/db/manager/repositories/email_template_repository.py
            class: EmailTemplateRepository
            purpose: Email template data access with performance tracking
            lines: 591
            features:
            - template CRUD
            - analytics queries
            - performance optimization
            - search functionality
            methods:
            - 'create(template_data: EmailTemplateCreate) -> EmailTemplate'
            - 'get_by_id(template_id: UUID) -> Optional[EmailTemplate]'
            - 'get_by_name(name: str) -> Optional[EmailTemplate]'
            - 'get_by_slug(slug: str) -> Optional[EmailTemplate]'
            - list_templates(page, per_page, filters) -> Tuple[List[EmailTemplate], int]
            - 'update(template_id: UUID, template_data: EmailTemplateUpdate) -> EmailTemplate'
            - 'increment_usage_count(template_id: UUID) -> None'
            - 'increment_open_count(template_id: UUID) -> None'
            - 'increment_click_count(template_id: UUID) -> None'
            - 'increment_bounce_count(template_id: UUID) -> None'
            - 'get_template_usage_stats(template_id: UUID) -> Dict[str, Any]'
          email_sent_log_repository:
            file: app/db/manager/repositories/email_sent_log_repository.py
            class: EmailSentLogRepository
            purpose: Email delivery tracking and analytics data access
            lines: 573
            features:
            - delivery tracking
            - webhook updates
            - analytics queries
            - performance metrics
            methods:
            - 'create(log_data: EmailSentLogCreate) -> EmailSentLog'
            - 'get_by_mailgun_id(message_id: str) -> Optional[EmailSentLog]'
            - 'update_by_mailgun_id(message_id: str, event_data: Dict) -> Optional[EmailSentLog]'
            - get_delivery_stats(template_id, start_date, end_date) -> Dict[str, Any]
            - 'get_recent_activity(limit: int) -> List[EmailSentLog]'
        schemas:
          email_api_schemas:
            file: app/schemas/email.py
            purpose: API request/response schemas for email operations
            schemas:
            - 'EmailSendResponse: Email sending operation results'
            - 'EmailTemplateResponse: Template information with analytics'
            - 'EmailServiceStatus: Service configuration and status'
            - 'EmailPreviewRequest: Template preview parameters'
            - 'EmailTestRequest: Test email parameters'
          email_db_schemas:
            file: app/db/schemas/email_template.py
            purpose: Database entity schemas for email templates
            schemas:
            - 'EmailTemplateBase: Base template fields'
            - 'EmailTemplateCreate: Template creation data'
            - 'EmailTemplateUpdate: Template modification data'
            - 'EmailTemplate: Complete template entity'
            - 'EmailTemplateWithStats: Template with calculated analytics'
          email_sent_log_schemas:
            file: app/db/schemas/email_sent_log.py
            purpose: Database entity schemas for email delivery tracking
            schemas:
            - 'EmailSentLogBase: Base delivery log fields'
            - 'EmailSentLogCreate: Log creation data'
            - 'EmailSentLogUpdate: Log modification data'
            - 'EmailSentLog: Complete delivery log entity'
            - 'EmailSentLogWithStatus: Log with calculated status'
      behavioral_specifications:
        email_lifecycle:
          template_selection:
          - Select appropriate email template based on notification type and recipient category
          - Validate template variables and provide fallback values for missing data
          - Apply personalization based on user preferences and booking context
          - Ensure template compliance with charter aviation communication standards
          content_rendering:
          - Render email content using Jinja2 template engine with context data
          - Generate both HTML and plain text versions for optimal deliverability
          - Apply responsive design for mobile and desktop email clients
          - Include tracking pixels and analytics parameters for performance monitoring
          delivery_processing:
          - Select optimal email provider based on configuration and availability
          - Implement automatic failover to backup providers on delivery failures
          - Apply rate limiting and queue management for high-volume sending
          - Log delivery attempts and track message IDs for webhook processing
          webhook_processing:
          - Process delivery, open, click, bounce, and complaint events from providers
          - Update email sent logs with real-time delivery status information
          - Increment template statistics for performance analytics
          - Trigger follow-up actions based on delivery events (e.g., bounce handling)
        template_management:
          template_creation:
          - Validate template syntax and variable references during creation
          - Generate URL-friendly slugs for template identification
          - Categorize templates by type (passenger, operator, system, authentication)
          - Initialize performance tracking counters for new templates
          performance_tracking:
          - Track usage count, open rate, click rate, bounce rate for each template
          - Calculate delivery rate based on successful deliveries vs. total sends
          - Monitor complaint rate and unsubscribe rate for reputation management
          - Generate performance reports and optimization recommendations
          template_optimization:
          - Identify low-performing templates based on engagement metrics
          - Provide A/B testing framework for template variations
          - Recommend template improvements based on industry benchmarks
          - Archive inactive templates and maintain template version history
        provider_management:
          provider_selection:
          - Select primary provider based on environment (Mailgun for production, Mailtrap for development)
          - Validate provider configuration before attempting email delivery
          - Implement intelligent failover based on provider health and performance
          - Monitor provider reputation and delivery rates for optimal routing
          delivery_reliability:
          - Implement exponential backoff for temporary delivery failures
          - Maintain dead letter queue for permanently failed messages
          - Provide manual retry mechanisms for critical email deliveries
          - Monitor delivery success rates and alert on degraded performance
      api_endpoints:
        email_management:
        - endpoint: POST /api/v1/admin/email/send
          purpose: Send individual email with template or custom content
          authentication: Admin authentication required
          parameters:
          - to_email
          - subject
          - template_name
          - context
          - priority
          response: EmailSendResponse with delivery status and message ID
        - endpoint: POST /api/v1/admin/email/send-bulk
          purpose: Send bulk emails with batch processing and queue management
          authentication: Admin authentication required
          parameters:
          - recipients
          - template_name
          - context
          - batch_size
          response: Bulk sending status with individual delivery results
        - endpoint: GET /api/v1/admin/email/templates
          purpose: List email templates with pagination and filtering
          authentication: Admin authentication required
          parameters:
          - page
          - per_page
          - template_type
          - is_active
          response: Paginated list of templates with performance statistics
        - endpoint: POST /api/v1/admin/email/templates
          purpose: Create new email template with validation
          authentication: Admin authentication required
          parameters:
          - name
          - subject
          - file_path
          - template_type
          - variables
          response: Created template with generated ID and metadata
        - endpoint: GET /api/v1/admin/email/templates/{template_id}
          purpose: Get email template details with analytics
          authentication: Admin authentication required
          parameters:
          - template_id
          response: Complete template information with performance metrics
        - endpoint: PUT /api/v1/admin/email/templates/{template_id}
          purpose: Update email template with validation
          authentication: Admin authentication required
          parameters:
          - template_id
          - template_data
          response: Updated template with new metadata
        - endpoint: POST /api/v1/admin/email/templates/{template_id}/preview
          purpose: Preview email template with sample data
          authentication: Admin authentication required
          parameters:
          - template_id
          - sample_context
          response: Rendered HTML and text preview with variable validation
        - endpoint: GET /api/v1/admin/email/analytics
          purpose: Get email performance analytics and metrics
          authentication: Admin authentication required
          parameters:
          - start_date
          - end_date
          - template_id
          - metric_type
          response: Comprehensive analytics with charts and insights
        webhook_endpoints:
        - endpoint: POST /api/v1/webhooks/mailgun/events
          purpose: Process Mailgun webhook events for delivery tracking
          authentication: Webhook signature verification
          parameters:
          - webhook_payload
          response: Event processing status and updated statistics
        - endpoint: POST /api/v1/webhooks/mailtrap/events
          purpose: Process Mailtrap webhook events for development tracking
          authentication: API key verification
          parameters:
          - webhook_payload
          response: Event processing status for development environment
        provider_management:
        - endpoint: GET /api/v1/admin/email/providers/status
          purpose: Get email provider configuration and health status
          authentication: Admin authentication required
          parameters: []
          response: Provider status, configuration, and performance metrics
        - endpoint: POST /api/v1/admin/email/providers/test
          purpose: Test email provider configuration with sample email
          authentication: Admin authentication required
          parameters:
          - provider_name
          - test_email
          - test_subject
          response: Test results with delivery status and diagnostics
      scheduler_integration:
        email_automation:
        - task: Process Pending Notifications
          schedule: every 5 minutes
          purpose: Process queued email notifications and deliver via appropriate providers
          handler: notifications_service.py:process_pending_notifications
          batch_size: 50
          features:
          - batch processing
          - provider routing
          - delivery tracking
          - error handling
        - task: Retry Failed Email Deliveries
          schedule: every 30 minutes
          purpose: Retry failed email deliveries with exponential backoff
          handler: email_service.py:retry_failed_deliveries
          max_retries: 3
          features:
          - retry logic
          - exponential backoff
          - failure analysis
          - dead letter queue
        - task: Update Template Analytics
          schedule: every 15 minutes
          purpose: Process webhook events and update email template performance metrics
          handler: mailgun_webhook_service.py:process_analytics_updates
          batch_size: 100
          features:
          - analytics processing
          - performance tracking
          - webhook integration
          - metrics calculation
        - task: Clean Email History
          schedule: daily 2:00 AM
          purpose: Archive old email logs and clean up delivery tracking data
          handler: email_sent_log_repository.py:cleanup_old_logs
          retention_days: 365
          features:
          - data cleanup
          - archival
          - performance optimization
          - storage management
        - task: Generate Email Reports
          schedule: weekly Sunday 6:00 AM
          purpose: Generate weekly email performance reports for stakeholders
          handler: email_service.py:generate_weekly_reports
          features:
          - performance reporting
          - trend analysis
          - stakeholder notifications
          - dashboard updates
      integration_points:
        booking_lifecycle_integration:
        - trigger: booking_created
          action: send_booking_confirmation_email
          template: passenger_booking_confirmation
          priority: high
          tracking: booking_confirmation_analytics
        - trigger: booking_status_change
          action: send_status_update_email
          template: passenger_flight_update
          personalization: booking_context
          analytics: status_update_performance
        - trigger: payment_required
          action: send_payment_reminder_email
          template: passenger_payment_reminder
          urgency: time_sensitive
          follow_up: automated_reminder_sequence
        - trigger: flight_departure
          action: send_itinerary_email
          template: passenger_itinerary
          attachments: boarding_pass_pdf
          timing: 24_hours_before
        operator_workflow_integration:
        - trigger: quote_request_created
          action: send_quote_request_email
          template: operator_quote_request
          persona: formal_business
          tracking: quote_request_analytics
        - trigger: booking_confirmed
          action: send_operator_confirmation_email
          template: operator_booking_confirmation
          details: operational_requirements
          follow_up: readiness_confirmation
        - trigger: flight_schedule_change
          action: send_operator_update_email
          template: operator_flight_update
          urgency: immediate
          escalation: operations_team
        authentication_integration:
        - trigger: login_code_requested
          action: send_login_code_email
          template: auth_login_code
          priority: immediate
          expiration: 5_minutes
        - trigger: user_registration
          action: send_welcome_email
          template: auth_registration_welcome
          personalization: user_profile
          sequence: onboarding_email_series
        - trigger: security_alert
          action: send_security_notification_email
          template: auth_security_alert
          channels:
          - email
          - sms
          priority: critical
        notification_system_integration:
        - trigger: notification_created
          action: route_to_email_channel
          template: notification_template_mapping
          preferences: user_notification_settings
          delivery: channel_optimization
        - trigger: bulk_notification
          action: process_bulk_email_delivery
          batch_processing: queue_management
          rate_limiting: provider_constraints
          analytics: bulk_delivery_tracking
      performance_metrics:
        email_delivery:
        - metric: delivery_rate
          target: '>99%'
          current: 99.2%
          measurement: successful_deliveries / total_sends
        - metric: average_delivery_time
          target: <3 seconds
          current: 2.1 seconds
          measurement: time_from_send_to_delivery
        - metric: open_rate
          target: '>25%'
          current: 28.5%
          measurement: emails_opened / emails_delivered
        - metric: click_rate
          target: '>5%'
          current: 7.2%
          measurement: emails_clicked / emails_delivered
        - metric: bounce_rate
          target: <2%
          current: 0.8%
          measurement: emails_bounced / total_sends
        - metric: complaint_rate
          target: <0.1%
          current: 0.03%
          measurement: spam_complaints / emails_delivered
        template_performance:
        - metric: template_usage_distribution
          measurement: usage_count per template type
          insights: identify popular and underutilized templates
        - metric: template_engagement_rates
          measurement: open/click rates by template
          optimization: template content and design improvements
        - metric: template_conversion_rates
          measurement: desired actions per template
          business_impact: booking confirmations and payments
        provider_performance:
        - metric: provider_delivery_success
          measurement: successful_deliveries by provider
          reliability: provider_uptime_and_performance
        - metric: provider_response_time
          measurement: API_response_time by provider
          optimization: provider_selection_algorithm
        - metric: webhook_processing_time
          target: <1 second
          current: 0.7 seconds
          measurement: webhook_event_processing_latency
      implementation_status:
        completed_features:
          email_delivery_infrastructure: 95%
          template_management_system: 90%
          multi_provider_support: 88%
          webhook_tracking: 92%
          analytics_dashboard: 85%
          api_endpoints: 87%
          booking_integration: 89%
          authentication_integration: 91%
        current_gaps:
          advanced_personalization: Dynamic content personalization based on user behavior not fully implemented
          a_b_testing_framework: Template A/B testing infrastructure partially implemented
          advanced_analytics: Predictive analytics and machine learning insights not implemented
          internationalization: Multi-language template support partially implemented
          email_automation_workflows: Advanced drip campaigns and automation sequences not implemented
          deliverability_optimization: Advanced reputation management and ISP-specific optimization incomplete
        planned_features:
          ai_powered_optimization: AI-driven template optimization and send time optimization
          advanced_segmentation: Behavioral segmentation and dynamic content personalization
          predictive_analytics: Predictive email performance and engagement modeling
          enhanced_automation: Complex email automation workflows and trigger-based sequences
          compliance_management: GDPR, CAN-SPAM, and international email compliance automation
          integration_expansion: Enhanced integration with CRM, marketing automation, and analytics platforms
      files:
        core_services:
        - app/services/email_service.py
        - app/services/template_service.py
        - app/services/mailgun_webhook_service.py
        - app/services/notifications_service.py
        email_providers:
        - app/services/email_providers/factory.py
        - app/services/email_providers/base.py
        - app/services/email_providers/mailgun_provider.py
        - app/services/email_providers/mailtrap_provider.py
        - app/services/email_providers/smtp_provider.py
        database_models:
        - app/db/models/email.py
        - app/db/schemas/email_template.py
        - app/db/schemas/email_sent_log.py
        repositories:
        - app/db/manager/repositories/email_template_repository.py
        - app/db/manager/repositories/email_sent_log_repository.py
        api_endpoints:
        - app/api/v1/endpoints/communication/email_providers.py
        - app/api/v1/endpoints/communication/mailgun_webhook.py
        - app/api/v1/endpoints/admin/email_templates.py
        schemas:
        - app/schemas/email.py
        - app/schemas/admin_ui.py
        templates:
        - app/templates/passenger/booking_confirmation.html
        - app/templates/passenger/flight_update.html
        - app/templates/passenger/payment_reminder.html
        - app/templates/passenger/itinerary.html
        - app/templates/passenger/luggage_info.html
        - app/templates/operator/quote_request.html
        - app/templates/operator/booking_confirmation.html
        - app/templates/operator/flight_update.html
        - app/templates/operator/operational_memo.html
        - app/templates/operator/luggage_info.html
        - app/templates/system/admin_alert.html
        - app/templates/system/error_notification.html
        - app/templates/system/maintenance_notice.html
        - app/templates/system/performance_report.html
        - app/templates/auth/login_code.html
        - app/templates/auth/registration_welcome.html
        - app/templates/auth/security_alert.html
        configuration:
        - app/core/config.py
        - docs/EMAIL_PROVIDERS_SETUP.md
        - docs/EMAIL_MODELS_DOCUMENTATION.md
        - docs/EMAIL_TEMPLATE_PREVIEWS.md
        scripts:
        - scripts/populate_email_templates.py
        tests:
        - tests/unit/services/test_email_service.py
        - tests/unit/services/test_email_providers.py
        - tests/unit/repositories/test_email_template_repository.py
        - tests/unit/repositories/test_email_sent_log_repository.py
      domain: communication
      subdomain: email_email
      path: communication/email/email.yaml
      file_size_kb: 27.03
  authentication:
    main:
      system: authentication
      description: Villiers.ai Comprehensive Authentication System - World-Class Email-Based Passwordless Authentication with
        BIP39 Mnemonic Restoration Keys and Bulletproof Security. This system owns all user identity, authentication, registration,
        session management, and account recovery logic across the entire platform.
      intent_assertions:
      - Email-based passwordless authentication as the primary and only production method
      - Zero-friction onboarding with email verification codes - no passwords required
      - Client-side generated BIP39 mnemonic restoration keys for email account recovery
      - Complete account recovery capability when users lose email access
      - Seamless authentication state persistence across browser sessions and page reloads
      - Zero authentication failures - comprehensive error handling and retry logic
      - JWT token management with automatic refresh, rotation, and secure revocation
      - Anonymous sessions for unauthenticated users with seamless upgrade to authenticated state
      - All authentication flows must be tested with <1% failure rate end-to-end
      - Restoration keys generated client-side and never transmitted to server
      technical_assertions:
      - path: app/services/auth_service.py
        type: file
        description: Primary authentication orchestration service with email-based passwordless flow
      - path: app/services/email_service.py
        type: file
        description: Email delivery service for verification codes and account notifications
      - path: app/nostr/recovery.py
        type: file
        description: BIP39 mnemonic-based restoration key generation and recovery (client-side only)
      - path: app/core/security.py
        type: file
        description: JWT token creation, verification, and cryptographic operations
      - path: app/api/v1/endpoints/auth/auth.py
        type: file
        description: Authentication API endpoints with comprehensive error handling
      - path: app/db/models/auth.py
        type: file
        description: Authentication database models with optimized relationships
      - path: app/db/manager/repositories/revoked_token_repository.py
        type: file
        description: Token blacklist management with automated cleanup methods
      - path: app/api/deps.py
        type: file
        description: Authentication dependencies and middleware
      implementation_status:
        fully_implemented:
        - Email-based passwordless authentication flow
        - User registration with profile creation
        - JWT token lifecycle management with revocation
        - Token refresh with automatic rotation
        - Comprehensive audit logging and activity tracking
        - Database models with proper indexing and relationships
        - Error handling with centralized exception management
        partially_implemented:
        - BIP39 restoration key generation (backend utilities exist, frontend integration needed)
        - Account recovery using restoration keys (design complete, implementation needed)
        planned_future_enhancements:
        - Nostr protocol authentication (moved to future roadmap)
        - Challenge-response authentication (Nostr-based, future enhancement)
        - Client-side cryptographic key management for Nostr (future)
      authentication_architecture:
        primary_flow: email_based_passwordless
        restoration_method: bip39_mnemonic_keys
        description: Users authenticate with email verification codes and receive client-generated restoration keys for account
          recovery
        core_principles:
        - Email as primary identity anchor
        - Client-side restoration key generation using BIP39 standard
        - Server never stores or receives restoration keys
        - Restoration keys only used for email account recovery scenarios
        - Zero-knowledge recovery - server cannot access user's restoration capabilities
      current_authentication_flow:
        primary_method: email_based_passwordless
        description: Users register with email, receive verification codes, and authenticate with JWT tokens
        steps:
        - User provides email and profile information
        - System generates login code and sends via email
        - User enters verification code
        - System validates code and issues JWT tokens
        - Access token used for authenticated requests
        - Refresh token used for token renewal
        - Logout revokes tokens via blacklist
        restoration_key_flow:
        - During registration, client generates BIP39 mnemonic restoration key
        - Restoration key stored locally and optionally backed up by user
        - If email access lost, user can initiate recovery with restoration key
        - System validates restoration key against stored hash/identifier
        - User can update email address using restoration key authentication
        - New email verification required to complete recovery
        strengths:
        - Zero-friction onboarding - no passwords required
        - Works on all devices and browsers
        - Comprehensive token lifecycle management
        - Robust error handling and audit logging
        - Secure token revocation system
        - Client-side restoration key generation ensures user sovereignty
        - Account recovery possible even with complete email loss
        limitations:
        - Requires email access for regular authentication
        - Restoration key management responsibility lies with user
        - No offline authentication capability
      database_models:
      - model: NostrUser
        purpose: Primary user entity with email authentication and optional restoration key identifier
        fields:
        - id
        - email
        - display_name
        - is_active
        - is_superuser
        - user_metadata
        - airtable_id
        - restoration_key_hash
        indexes:
        - email
        - airtable_id
        - restoration_key_hash
        relationships:
        - bookings
        - quotes
        - activities
        - notifications
        - payments
      - model: LoginCode
        purpose: One-time codes for passwordless authentication
        fields:
        - email
        - code
        - expires_at
        - used
        - used_at
        indexes:
        - email
        - code
        cleanup: expired_codes_hourly
      - model: UserProfile
        purpose: Extended user profile information
        fields:
        - user_id
        - full_name
        - first_name
        - last_name
        - phone
        - company
        relationships:
        - user
      - model: RevokedToken
        purpose: JWT token blacklist for logout and security
        fields:
        - jti
        - user_id
        - token_type
        - revoked_at
        - expires_at
        - reason
        indexes:
        - jti
        - user_id_token_type
        - expires_at
        cleanup: expired_tokens_daily
      services:
      - service: AuthService
        path: app/services/auth_service.py
        purpose: Primary authentication orchestration service
        methods:
        - register_user
        - create_login_request
        - verify_login_code
        - refresh_token
        - logout
        dependencies:
        - db_manager
        - email_service
      - service: EmailService
        path: app/services/email_service.py
        purpose: Email delivery service for verification codes and notifications
        methods:
        - send_verification_code
        - send_login_alert
        - send_recovery_notification
        dependencies:
        - email_provider
        - template_engine
      - service: RestorationKeyService
        path: app/nostr/recovery.py
        purpose: BIP39 mnemonic-based restoration key generation and validation (client-side utilities)
        methods:
        - generate_recovery_phrase
        - validate_restoration_key
        - derive_key_identifier
        security: client_side_generation_only
      repositories:
      - repository: NostrUserRepository
        purpose: User data access and management
        methods:
        - get_user_by_email
        - get_user_by_restoration_key_hash
        - create_user
        - update_user
        validation: email_format_restoration_key_format
      - repository: RevokedTokenRepository
        path: app/db/manager/repositories/revoked_token_repository.py
        purpose: Token blacklist management and cleanup
        methods:
        - create_revoked_token
        - is_token_revoked
        - cleanup_expired_tokens
        - get_user_revoked_tokens
        maintenance: automated_cleanup
      schemas:
      - schema: RegisterRequest
        purpose: User registration input validation
        fields:
        - email
        - first_name
        - last_name
        - phone
        validation:
        - email_format
        - name_sanitization
        - phone_format
      - schema: RegisterResponse
        purpose: Registration response with tokens and status
        fields:
        - user_id
        - access_token
        - refresh_token
        - account_status
        - requires_verification
      - schema: VerifyRequest
        purpose: Login code verification input
        fields:
        - login_request_id
        - verification_code
        validation:
        - uuid_format
        - code_format
      - schema: TokenResponse
        purpose: Token refresh response
        fields:
        - access_token
        - refresh_token
        - token_type
        - expires_in
      - schema: RevokedTokenCreate
        purpose: Token revocation data
        fields:
        - jti
        - user_id
        - token_type
        - revoked_at
        - expires_at
        - reason
      security_modules:
      - module: app/core/security.py
        purpose: JWT token creation, verification, and management
        functions:
        - create_access_token
        - create_refresh_token
        - verify_token
        - generate_login_code
      - module: app/core/auth.py
        purpose: Authentication dependencies and user resolution
        functions:
        - get_current_user
        - get_current_superuser
        - get_optional_current_user
        - verify_api_key
      - module: app/nostr/keys.py
        purpose: Nostr cryptographic key operations
        functions:
        - generate_keypair
        - encrypt_nsec
        - decrypt_nsec
        - verify_signature
      behavior:
        email_authentication:
          passwordless_flow:
          - Users register with email and profile information only
          - Login codes generated server-side and sent via email
          - Verification codes have short expiry times (5-10 minutes)
          - Failed verification attempts are rate limited and logged
          restoration_key_management:
          - BIP39 mnemonic restoration keys generated client-side during registration
          - Restoration key identifiers derived and stored server-side for validation
          - Restoration keys never transmitted to server in plain text
          - Recovery process validates restoration key without revealing it to server
          - Email update requires both restoration key validation and new email verification
        session_management:
          jwt_tokens:
          - Issue JWT access tokens valid for 24 hours
          - Issue refresh tokens valid for 30 days with secure storage
          - Automatic token refresh 5 minutes before expiration
          - Secure token storage using httpOnly cookies where possible
          - Token revocation on logout with server-side invalidation
          anonymous_sessions:
          - Create anonymous session IDs for unauthenticated users
          - Allow anonymous users to use platform with limited functionality
          - Seamless upgrade from anonymous to authenticated session
          - Preserve anonymous session data during authentication upgrade
        authentication_endpoints:
          login_flows:
          - path: /api/v1/auth/request-login
            method: POST
            description: Email-based login request with verification code generation
            response_time: <100ms
          - path: /api/v1/auth/register
            method: POST
            description: Email-based registration with profile creation
            response_time: <100ms
          verification_endpoints:
          - path: /api/v1/auth/verify
            method: POST
            description: Login code verification and JWT token issuance
            response_time: <50ms
          recovery_endpoints:
          - path: /api/v1/auth/recovery/initiate
            method: POST
            description: Initiate account recovery with restoration key
            response_time: <100ms
          - path: /api/v1/auth/recovery/complete
            method: POST
            description: Complete account recovery and email update
            response_time: <100ms
          session_endpoints:
          - path: /api/v1/auth/logout
            method: POST
            description: Session termination with token revocation
          - path: /api/v1/auth/refresh-token
            method: POST
            description: Token refresh with automatic rotation
      invariants:
      - Restoration keys (BIP39 mnemonics) must never be transmitted to server in plain text
      - Server must only store restoration key identifiers/hashes, never the keys themselves
      - All JWT tokens must include unique JTI for revocation capability
      - Email verification codes must have short expiry times and be single-use
      - Token revocation must be checked on every authenticated request
      - Account recovery must verify restoration key against stored identifier
      - User sessions must maintain audit trail for security monitoring
      - Rate limiting must be enforced on all authentication endpoints
      - Email delivery failures must be logged and retried with exponential backoff
      - Restoration key generation must use cryptographically secure random sources
      forbidden_states:
      - Restoration keys transmitted in plain text to server
      - BIP39 mnemonic phrases stored on server in any form
      - JWT tokens without JTI for revocation tracking
      - Email verification codes with unlimited attempts or no expiry
      - Tokens accepted without revocation status checking
      - Account recovery without restoration key validation
      - User authentication without rate limiting protection
      - Tokens with predictable or non-unique identifiers
      - Authentication events without audit logging
      - Email delivery without proper error handling and retry logic
      depends_on:
      - system: database
        purpose: User data storage and token blacklist management
        components:
        - postgresql
        - async_sessions
        - repositories
      - system: communication
        purpose: Email delivery for login codes and recovery
        components:
        - email_service
        - template_engine
      - system: analytics
        purpose: Authentication activity tracking and monitoring
        components:
        - user_activity
        - security_events
      enforcement_hooks:
      - hook: authentication_validator
        frequency: every_request
        validates:
        - token_validity
        - revocation_status
        - user_active
      - hook: token_cleanup_job
        frequency: daily
        validates:
        - expired_tokens
        - cleanup_completion
      - hook: security_monitor
        frequency: real_time
        validates:
        - failed_attempts
        - rate_limits
        - suspicious_activity
      - hook: challenge_expiry_cleanup
        frequency: hourly
        validates:
        - expired_challenges
        - used_challenges
      error_handling: null
      authentication_errors:
      - Invalid credentials return generic error messages
      - Token expiry handled with refresh flow
      - Revoked tokens rejected with clear error
      - Rate limit exceeded returns 429 status
      - Service unavailable returns 503 status
      recovery_errors:
      - Invalid recovery tokens expire after 1 hour
      - Mismatch mnemonic verification fails securely
      - Recovery rate limiting prevents abuse
      - Email delivery failures logged and retried
      validation_errors:
      - Input sanitization prevents injection attacks
      - Email format validation with proper regex
      - JWT format validation with signature verification
      - Challenge format validation prevents manipulation
      scheduler_integration: null
      cleanup_tasks:
      - task: cleanup_expired_tokens
        frequency: daily_at_02:00
        purpose: Remove expired revoked tokens from blacklist
        batch_size: 1000
      - task: cleanup_expired_challenges
        frequency: hourly
        purpose: Remove expired authentication challenges
        retention: 24_hours
      - task: cleanup_expired_login_codes
        frequency: hourly
        purpose: Remove expired passwordless login codes
        retention: 1_hour
      monitoring_tasks:
      - task: security_metrics_collection
        frequency: every_15_minutes
        purpose: Collect authentication security metrics
        metrics:
        - failed_attempts
        - token_usage
        - recovery_requests
      - task: audit_log_aggregation
        frequency: daily
        purpose: Aggregate authentication audit events
        retention: 90_days
      files:
      - app/api/v1/endpoints/auth/auth.py
      - app/services/auth_service.py
      - app/services/nostr_auth_service.py
      - app/services/legacy_services/auth.py
      - app/nostr/auth.py
      - app/nostr/keys.py
      - app/nostr/recovery.py
      - app/core/auth.py
      - app/core/security.py
      - app/db/models/auth.py
      - app/schemas/auth.py
      - app/db/schemas/auth.py
      - app/db/manager/repositories/revoked_token_repository.py
      - app/api/deps.py
      - docs/authentication/AUTHENTICATION_WORKFLOW.md
      - docs/authentication/WEB_BROWSER_AUTH.md
      - docs/authentication/SECURE_LOGIN_ANALYSIS.md
      - docs/authentication/secure_login_challenge.py
      integrations:
        ui_consolidation:
          moved_from_ui_yaml:
          - Nostr authentication flow requirements and UX specifications
          - Authentication state persistence and management logic
          - Login/logout user experience and visual requirements
          - Authentication-related API endpoint declarations
          ownership: authentication.yaml now owns all identity and authentication UI logic
        api_consolidation:
          authentication_endpoints:
          - path: /api/v1/auth/login
            method: POST
            description: Nostr-based authentication with signature verification
          - path: /api/v1/auth/register
            method: POST
            description: Nostr-based user registration and identity creation
          - path: /api/v1/auth/verify
            method: POST
            description: JWT token verification and user identification
          - path: /api/v1/auth/logout
            method: POST
            description: Session termination with token revocation
          - path: /api/v1/auth/refresh-token
            method: POST
            description: Token refresh with automatic rotation
      monitoring:
        authentication_metrics:
          login_success_rate:
            frequency: every 5 minutes
            threshold: '>95% success rate for login attempts'
            escalation: Alert if success rate drops below threshold
          session_health:
            frequency: every 1 minute
            metrics:
            - Active authenticated sessions count
            - Anonymous session creation rate
            - Session upgrade success rate
            - Token refresh success rate
        security_monitoring:
          authentication_failures:
            tracking:
            - Failed login attempts by IP and frequency
            - Invalid token usage attempts
            - Suspicious authentication patterns
            - Potential brute force attack detection
      testing:
        end_to_end_validation:
          authentication_flows:
          - Complete registration flow with keypair generation and backup
          - Login flow with existing Nostr credentials
          - Browser extension authentication and integration
          - Anonymous session creation and upgrade to authenticated state
          - Session persistence across browser restart and page refresh
          - Logout flow with complete session termination
        visual_regression_testing:
          authentication_components:
          - Login modal rendering and responsiveness
          - Registration flow visual consistency
          - Error message display and formatting
          - Loading states and progress indicators
          - Mobile responsiveness for all authentication screens
        automated_testing:
          frequency: every 15 minutes
          coverage_requirement: '>98% for all authentication-critical code paths'
          performance_targets:
          - Authentication flow completion <30 seconds
          - Login modal render <100ms
          - JWT token verification <50ms
          - Session state update <10ms
      security:
        cryptographic_standards:
        - secp256k1 elliptic curve cryptography for all Nostr operations
        - Cryptographically secure random number generation (crypto.getRandomValues)
        - Proper signature verification using established Nostr libraries
        - Protection against timing attacks in signature verification
        data_protection:
        - Zero server-side private key storage or transmission
        - Minimal data collection - only public keys stored for identity
        - Secure session token storage with appropriate security flags
        - Protection against common web vulnerabilities (XSS, CSRF, etc.)
      performance:
        response_times:
          authentication: <100ms for all auth endpoints
          token_verification: <50ms for JWT validation
          session_operations: <100ms for session management
          ui_components: <100ms for component rendering
        availability:
          uptime_target: 99.99%
          downtime_budget: 4.32 minutes per month
          recovery_time: <30 seconds for authentication service failures
      consolidation_notes:
        authentication_ownership: All authentication logic consolidated into authentication.yaml
        ui_integration: Authentication UI components and flows managed by authentication.yaml
        api_endpoints: Authentication API endpoints removed from ui.yaml and api.yaml - owned by authentication.yaml
        responsibility: Authentication system focuses on identity, security, and user sovereignty
      provides:
      - Nostr-based identity management
      - JWT authentication services
      - User registration and onboarding
      - Session management and persistence
      - Zero-confusion authentication UX
      implementation_gaps:
        restoration_key_integration:
          status: design_complete_implementation_needed
          description: BIP39 restoration key system designed but not integrated
          current_state:
          - BIP39 mnemonic generation utilities exist in app/nostr/recovery.py
          - Restoration key concept documented in authentication workflow
          - Database schema can support restoration key identifiers
          missing_components:
          - Frontend restoration key generation during registration
          - API endpoints for restoration key-based recovery
          - Restoration key validation and email update flow
          - User interface for restoration key backup and recovery
          implementation_priority: high
          estimated_effort: 1-2 weeks
        frontend_authentication_components:
          status: not_implemented
          description: No frontend authentication components implemented
          current_state:
          - Backend API endpoints fully functional
          - Comprehensive error handling and validation
          missing_components:
          - React/TypeScript authentication components
          - Authentication state management (Redux/Context)
          - Login/registration UI components with restoration key generation
          - Token management and refresh logic
          - Restoration key backup and recovery UI
          implementation_priority: high
          estimated_effort: 1-2 weeks
        scheduler_integration:
          status: defined_not_integrated
          description: Cleanup methods exist but not scheduled for execution
          current_state:
          - RevokedTokenRepository.cleanup_expired_tokens() implemented
          - Cleanup task definitions in system specification
          - SchedulerService exists with system job registration
          missing_components:
          - Registration of authentication cleanup jobs in SchedulerService
          - Login code cleanup job implementation and registration
          implementation_priority: medium
          estimated_effort: 1-2 days
      restoration_key_system:
        purpose: Provide account recovery when users lose email access
        technology: BIP39 mnemonic phrases (12-24 words)
        generation: Client-side using cryptographically secure random
        storage: User responsibility - local backup, secure storage
        server_knowledge: Only stores hash/identifier for validation, never the key itself
        use_cases:
        - Email account compromised or lost
        - Email provider service discontinued
        - User wants to change primary email address
        - Account locked due to email delivery issues
        security_model:
        - Restoration key generated client-side using crypto.getRandomValues()
        - Server stores only a hash or public identifier derived from restoration key
        - Restoration key never transmitted to server in plain text
        - Recovery process validates restoration key without revealing it to server
        - User must verify new email address after restoration key recovery
        implementation_approach:
        - Generate 12-word BIP39 mnemonic during registration
        - Derive identifier from mnemonic for server storage
        - Provide secure backup options (download, print, copy)
        - Recovery flow validates mnemonic and allows email update
        - Require new email verification to complete recovery
      future_enhancements:
        nostr_protocol_integration:
          status: future_roadmap
          description: Advanced cryptographic authentication for sovereignty-focused users
          timeline: Phase 2 - after core email authentication is perfected
          features:
          - Challenge-response authentication with Nostr signatures
          - Client-side keypair generation and management
          - Integration with Nostr browser extensions
          - Offline authentication capabilities
          - Advanced multi-device key synchronization
        advanced_recovery_options:
          status: future_enhancement
          description: Additional recovery mechanisms beyond email and restoration keys
          features:
          - Multi-signature recovery with trusted contacts
          - Time-locked recovery mechanisms
          - Hardware security key integration
          - Biometric authentication options
      domain: authentication
      subdomain: null
      path: authentication/authentication.yaml
      file_size_kb: 23.96
  analytics:
    main:
      system: villiers_analytics
      description: Villiers.ai Analytics Domain - Comprehensive analytics and business intelligence system for data-driven
        decision making across all Villiers.ai operations including event tracking, user behavior analysis, conversion optimization,
        performance monitoring, and business intelligence reporting
      intent_assertions:
      - User activity tracking and behavioral analytics
      - System performance metrics and operational monitoring
      - Revenue optimization through detailed analytics
      - Business intelligence dashboards and reporting
      - Search analytics and feature usage tracking
      - Conversion funnel analysis and optimization
      - Quote accuracy tracking and operator performance metrics
      - Event-driven analytics and audit trails
      - Real-time dashboards and administrative reporting
      - Support revenue optimization through detailed analytics
      - Enable predictive analytics for demand forecasting
      - Provide actionable insights for business optimization
      - Support compliance and audit requirements
      - Improve user experience through behavioral analytics
      technical_assertions:
        analytics_service:
        - path: app/services/analytics_service.py
          purpose: Primary analytics service with centralized database manager pattern
          lines: 1198
          capabilities:
          - user_activity_tracking
          - system_metrics
          - conversion_analysis
          - business_intelligence
        - path: app/services/legacy_services/analytics.py
          purpose: Legacy analytics service (deprecated, migration in progress)
          lines: 895
          status: deprecated
        analytics_api:
        - path: app/api/v1/endpoints/analytics/analytics.py
          purpose: Analytics data collection and reporting endpoints
          lines: 424
          endpoints:
          - path: /api/v1/analytics/track
            method: POST
            purpose: Track user activity events from client side
          - path: /api/v1/analytics/track/page-view
            method: POST
            purpose: Track page view events
          - path: /api/v1/analytics/reports/active-users
            method: POST
            purpose: Generate active users analytics reports
          - path: /api/v1/analytics/reports/page-views
            method: POST
            purpose: Generate page views analytics reports
          - path: /api/v1/analytics/reports/conversion
            method: POST
            purpose: Generate conversion funnel analytics reports
          - path: /api/v1/analytics/reports/feature-usage
            method: POST
            purpose: Generate feature usage analytics reports
          - path: /api/v1/analytics/reports/search
            method: POST
            purpose: Generate search analytics and optimization reports
          - path: /api/v1/analytics/quote-accuracy
            method: POST
            purpose: Get quote estimate accuracy metrics
          - path: /api/v1/analytics/reports/today
            method: GET
            purpose: Get today's analytics summary for quick insights
          - path: /api/v1/analytics/reports/last-7-days
            method: GET
            purpose: Get last 7 days analytics summary
        admin_analytics:
        - path: app/api/v1/endpoints/admin/admin_ui.py
          purpose: Admin dashboard analytics endpoints
          lines: 294
          endpoints:
          - path: /api/v1/admin-ui/analytics
            method: GET
            purpose: Comprehensive analytics data for admin dashboards
          - path: /api/v1/admin-ui/dashboard
            method: GET
            purpose: Consolidated admin dashboard with key metrics
        analytics_core:
        - path: app/db/models/analytics.py
          purpose: Core analytics database models
          lines: 158
          models:
          - UserActivity - User interaction and behavior tracking
          - SystemMetric - System performance and operational metrics
          - ConversionFunnel - Booking conversion process tracking
          - SearchAnalytics - Search query pattern analysis
          - BookingAnalytics - Detailed booking process analytics
        analytics_schemas:
        - path: app/db/schemas/analytics.py
          purpose: Analytics API schemas and validation
          lines: 294
          schemas:
          - UserActivityCreate/Response - User activity data transfer
          - SystemMetricCreate/Response - System metrics data transfer
          - SearchAnalyticsBase/Response - Search metrics schemas
          - BookingAnalyticsBase/Response - Booking analytics schemas
          - AnalyticsReport - Standardized report format
          - ConversionFunnelBase/Response - Funnel tracking schemas
          - QuoteAccuracyMetrics - Quote performance analysis
          - AdminAnalyticsResponse - Comprehensive analytics response
          - UserMetrics - User-related analytics
          - RevenueMetrics - Financial performance analytics
          - PerformanceMetrics - System performance indicators
          - BusinessMetrics - Key business performance indicators
        analytics_repositories:
        - path: app/db/manager/repositories/user_activity_repository.py
          purpose: User activity data access layer with comprehensive analytics queries
          lines: 802
          operations:
          - activity_tracking
          - funnel_analysis
          - user_metrics
          - conversion_tracking
        - path: app/db/manager/repositories/system_metric_repository.py
          purpose: System metrics data access with performance analytics
          lines: 426
          operations:
          - metric_storage
          - performance_analysis
          - quote_accuracy
          - api_metrics
        analytics_automation:
        - task: Analytics Data Aggregation
          schedule: daily 2:00 AM
          purpose: Aggregate daily analytics metrics for reporting
        - task: Performance Metrics Collection
          schedule: every 5 minutes
          purpose: Collect system performance metrics
        - task: Conversion Funnel Analysis
          schedule: hourly
          purpose: Update conversion funnel metrics and alerts
      behavior:
        event_tracking:
          user_activity_capture:
            1: Client-side events trigger tracking endpoints
            2: AnalyticsService.track_user_activity processes events
            3: Activity stored with metadata and session context
            4: Real-time aggregation for dashboard updates
        system_metrics_collection:
          performance_monitoring:
            1: System components emit performance metrics
            2: Metrics captured via AnalyticsService.track_system_metric
            3: Metrics stored with dimensions and categories
            4: Automated alerts triggered for anomalies
        reporting_workflows:
          real_time_dashboards:
            dashboard_loading:
              1: Admin accesses dashboard endpoint
              2: Multiple analytics services queried in parallel
              3: Metrics aggregated and formatted for UI consumption
              4: Response cached for performance optimization
              5: Dashboard auto-refreshes with latest data
          scheduled_reporting:
            report_generation:
              1: Scheduler triggers report generation tasks
              2: Historical data aggregated by time periods
              3: Reports generated and stored for retrieval
              4: Stakeholders notified of report availability
        conversion_analysis:
          funnel_optimization:
            1: Conversion metrics analyzed for drop-off identification
            2: A/B testing data correlated with conversion rates
            3: Optimization recommendations generated
            4: Implementation impact tracked and measured
      invariants:
        data_integrity:
        - All analytics events must include timestamp and category classification
        - User activity must be trackable across sessions
        - System metrics must include timestamp and category classification
        - All measurement values must be validated before storage
        - Event ordering must be preserved in time-series data
        performance_requirements:
        - Analytics collection must not impact application performance
        - Dashboard queries must complete within 2000ms
        - Event tracking must complete within 100ms
        - Real-time metrics must be updated within 30 seconds
        - System metrics collection must not impact application performance
        privacy_compliance:
        - Analytics data retention must comply with privacy regulations
        - User consent must be verified before tracking personal data
        - Personal data must be anonymized in analytics aggregations
      forbidden_states:
        data_loss:
        - Analytics events must never be lost due to processing failures
        - Metrics must never be double-counted or duplicated
        - Analytics data must never be lost due to system failures
        privacy_violations:
        - Personal data must never be exposed in analytics APIs
        - Tracking must never continue after user opt-out
        performance_degradation:
        - Analytics collection must never block primary application flows
        - Dashboard queries must never timeout or fail silently
        - Memory usage must never exceed 500MB per analytics process
        - Analytics queries must never impact transactional performance
      depends_on:
        data_layer:
        - database
        - caching
        business_logic:
        - booking
        - authentication
        infrastructure:
        - api
        - monitoring
      provides:
        analytics_capabilities:
        - User behavior tracking and analysis
        - System performance monitoring and alerting
        - Revenue analytics and financial performance tracking
        - Conversion funnel optimization insights
        - Business intelligence dashboards and reporting
        - Quote accuracy and operator performance metrics
        - Search analytics and feature usage insights
      enforcement_hooks:
        monitoring:
          analytics_operations:
            location: /var/log/villiers/analytics.log
            content: All analytics operations with performance metrics and user context
            retention: 90 days
          privacy_compliance:
            location: /var/log/villiers/analytics_privacy.log
            content: User consent and data processing decisions
            retention: 3 years
        validation:
        - Validate all incoming analytics data for completeness
        - Check privacy compliance before data collection
        - Verify performance impact within acceptable limits
        - Monitor data quality and accuracy metrics
      security:
        access_control:
        - User activity tracking available to authenticated and anonymous users
        - Analytics APIs require admin-level authentication
        - System metrics collection requires service-level authentication
        data_protection:
        - Anonymous data aggregation preferred for reporting
        - Rate limiting on all analytics endpoints
        - Input validation on all tracking endpoints
        - Secure data transmission using HTTPS
      monitoring:
        metrics:
        - Event processing rate and queue depth
        - Analytics API response times and success rates
        - Dashboard query performance and cache hit rates
        - Data quality scores and validation failures
        - Memory usage and garbage collection in analytics processes
        - Real-time metrics update frequency and accuracy
        alerts:
        - Analytics API response time > 500ms
        - Event processing queue depth > 1000 items
        - Data validation failure rate > 5%
        - Dashboard queries failing > 5%
      performance:
        response_times:
          tracking: < 100ms for event tracking endpoints
          dashboard: < 2000ms for admin dashboard queries
          reports: < 5000ms for standard analytics reports
          metrics: < 500ms for real-time metrics endpoints
        throughput:
          events: 10,000+ events per minute sustained processing
          queries: 1,000+ concurrent dashboard queries
          concurrent_users: 500+ concurrent admin dashboard users
        resource_limits:
          memory_usage: < 500MB per analytics service instance
          cpu_utilization: < 70% during peak analytics processing
          cache_efficiency: '> 80% cache hit rate for dashboard queries'
      scalability:
        horizontal_scaling:
        - Analytics services can be horizontally scaled independently
        - Event processing can be distributed across multiple workers
        - Dashboard queries can be load balanced across read replicas
        data_partitioning:
        - Time-series data partitioned by date for efficient queries
        - System metrics partitioned by category and time range
        - Archive old analytics data to cold storage automatically
      availability:
        fault_tolerance:
        - Graceful degradation when analytics endpoints are unavailable
        - Event queuing for temporary service outages
        - Fallback to simplified metrics during system overload
        disaster_recovery:
          reporting_failures:
          - Automated failover to backup analytics infrastructure
          - Partial data reporting with clear data quality indicators
        notifications:
        - Email notifications for critical analytics system failures
        - Slack alerts for performance degradation
        - PagerDuty integration for system outages
      files:
      - app/services/analytics_service.py
      - app/services/legacy_services/analytics.py
      - app/api/v1/endpoints/analytics/analytics.py
      - app/api/v1/endpoints/admin/admin_ui.py
      - app/db/models/analytics.py
      - app/db/schemas/analytics.py
      - app/db/manager/repositories/user_activity_repository.py
      - app/db/manager/repositories/system_metric_repository.py
      domain: analytics
      subdomain: null
      path: analytics/analytics.yaml
      file_size_kb: 13.21
    admin_dashboards:
      system: villiers_analytics_admin_dashboards
      description: Admin Dashboard Analytics - Comprehensive business intelligence dashboards for administrative oversight
        and decision making including executive dashboards, operational metrics, financial reporting, and system health monitoring
      intent_assertions:
      - Consolidated administrative dashboards with key business metrics
      - Real-time operational oversight and system health monitoring
      - Financial performance tracking and revenue analytics
      - User engagement and growth metrics visualization
      - System performance and reliability monitoring
      - Customizable time ranges and metric breakdowns
      technical_assertions:
        dashboard_endpoints:
        - path: /api/v1/admin-ui/dashboard
          method: GET
          purpose: Consolidated admin dashboard data
          response: AdminDashboardResponse
        - path: /api/v1/admin-ui/analytics
          method: GET
          purpose: Comprehensive analytics data for admin dashboards
          response: AdminAnalyticsResponse
        service_integration:
        - path: app/api/v1/endpoints/admin/admin_ui.py
          methods:
          - get_admin_dashboard
          - get_admin_analytics
          purpose: Admin dashboard endpoints with consolidated data
        - path: app/services/analytics_service.py
          methods:
          - get_analytics_data
          - _get_user_metrics
          - _get_revenue_metrics
          - _get_performance_metrics
          - _get_business_metrics
          purpose: Analytics data aggregation for dashboards
        schema_definitions:
        - path: app/schemas/admin_ui.py
          schemas:
          - AdminDashboardResponse
          - AdminAnalyticsResponse
          - UserMetrics
          - RevenueMetrics
          - PerformanceMetrics
          - BusinessMetrics
          purpose: Structured dashboard data models
      behavior:
        dashboard_loading_workflow:
          1: Admin user accesses dashboard endpoint
          2: Multiple service queries executed in parallel
          3: User metrics aggregated (active users, growth, engagement)
          4: Revenue metrics calculated (MTD revenue, booking values, conversion)
          5: Performance metrics gathered (API response times, system health)
          6: Business metrics computed (CAC, LTV, retention rates)
          7: Data consolidated and formatted for UI consumption
          8: Response cached for performance optimization
        analytics_data_flow:
          1: Real-time metrics collected from operational systems
          2: Historical data aggregated by configurable time ranges
          3: Multi-dimensional analysis with breakdown options
          4: Currency conversion for financial metrics
          5: Data quality validation and completeness checks
      dashboard_capabilities:
        executive_overview:
        - Key performance indicators at-a-glance
        - Revenue trends and financial health
        - User growth and engagement metrics
        - System health and operational status
        operational_metrics:
        - Active user counts and engagement trends
        - Pending quotes and bookings requiring attention
        - System performance and error rates
        - Recent alerts and critical notifications
        financial_analytics:
        - Month-to-date and period revenue reporting
        - Revenue breakdown by service type and operator
        - Conversion rates and booking value analysis
        - Currency-aware financial reporting
        user_analytics:
        - User acquisition and retention metrics
        - Geographic distribution and country breakdown
        - Session duration and engagement analysis
        - User growth rates and cohort analysis
      key_metrics:
        business_health:
        - Active users today/this week/this month
        - Revenue MTD with growth percentage
        - Pending quotes and bookings count
        - System uptime and availability
        user_engagement:
        - Daily/Weekly/Monthly Active Users
        - New user registrations and growth rates
        - Average session duration
        - User retention and return rates
        financial_performance:
        - Total revenue by time period
        - Average booking value and trends
        - Revenue growth percentage
        - Conversion rates through booking funnel
        system_performance:
        - API response times (P95)
        - Error rates and system availability
        - Database performance metrics
        - Recent errors and system alerts
      customization_features:
        time_range_options:
        - Today, Yesterday, Last 24 Hours
        - Last 7 Days, Last 30 Days
        - This Month, Last Month
        - This Year, Custom Date Range
        metric_filtering:
        - Specific metric groups (users, revenue, performance, business)
        - Geographic breakdown by country
        - Service type breakdown
        - Time-based breakdown (daily, weekly, monthly)
        export_capabilities:
        - Dashboard data export to CSV/Excel
        - Scheduled report generation
        - Email report delivery
        - API data access for third-party tools
      performance_requirements:
        response_times:
        - Dashboard endpoint < 2000ms
        - Analytics endpoint < 5000ms
        - Real-time metrics update < 30 seconds
        concurrent_access:
        - Support 100+ concurrent admin users
        - Dashboard auto-refresh without performance impact
        - Efficient caching for repeated queries
        data_freshness:
        - Real-time data for current day metrics
        - Hourly aggregation for recent trends
        - Daily aggregation for historical analysis
      security_considerations:
        access_control:
        - Admin-level authentication required
        - Role-based dashboard access control
        - Audit logging for dashboard access
        data_protection:
        - Sensitive financial data encryption
        - PII anonymization in analytics views
        - Secure data transmission over HTTPS
      integration_points:
      - User service for authentication and preferences
      - Booking service for revenue and conversion metrics
      - Quote service for pending quote tracking
      - Health service for system status monitoring
      - Analytics service for comprehensive metrics
      - Scheduler service for automated report generation
      invariants:
      - Dashboard data must be accurate and consistent across all views
      - Real-time metrics must be updated within specified timeframes
      - Admin authentication must be verified before dashboard access
      - Financial data must be properly formatted and currency-aware
      forbidden_states:
      - Dashboard displaying stale or incorrect data
      - Unauthorized access to admin dashboard functionality
      - Performance degradation due to inefficient dashboard queries
      - Financial data exposure without proper authorization
      depends_on:
      - authentication
      - booking
      - communication
      - database
      - api
      provides:
      - executive_dashboard_insights
      - operational_metrics_visualization
      - financial_performance_reporting
      - user_analytics_dashboards
      files:
      - app/api/v1/endpoints/admin/admin_ui.py
      - app/services/analytics_service.py
      - app/schemas/admin_ui.py
      domain: analytics
      subdomain: admin_dashboards
      path: analytics/admin_dashboards.yaml
      file_size_kb: 6.66
    user_behavior:
      system: villiers_analytics_user_behavior
      description: User Behavior Analytics - Track and analyze user interactions, sessions, and behavioral patterns for UX
        optimization including user activity tracking, session analysis, feature usage patterns, and behavioral insights
      intent_assertions:
      - Comprehensive user journey tracking across all platform touchpoints
      - Session-based analytics with anonymous and authenticated user support
      - Feature usage analysis for product optimization
      - User engagement metrics and retention analysis
      - Page view tracking with performance correlation
      - Conversion funnel analysis from user behavior perspective
      technical_assertions:
        tracking_infrastructure:
        - path: app/services/analytics_service.py
          methods:
          - track_user_activity
          - track_page_view
          - track_feature_usage
          - track_booking_funnel
          purpose: Core user behavior tracking methods
        - path: app/db/models/analytics.py
          model: UserActivity
          purpose: User interaction storage with metadata and session context
        - path: app/db/manager/repositories/user_activity_repository.py
          methods:
          - create_user_activity
          - get_login_history
          - get_active_users
          - get_page_views
          purpose: User behavior data access and analysis
        api_endpoints:
        - path: /api/v1/analytics/track
          method: POST
          purpose: Generic user activity tracking endpoint
        - path: /api/v1/analytics/track/page-view
          method: POST
          purpose: Dedicated page view tracking endpoint
      behavior:
        user_session_tracking:
          1: User interaction triggers client-side tracking
          2: Session ID and user context captured
          3: Activity metadata enriched with device and location info
          4: Event stored with proper anonymization for privacy
          5: Real-time aggregation updates user engagement metrics
        behavioral_analysis:
          1: User activities aggregated by session and time periods
          2: Feature usage patterns identified and analyzed
          3: User journey paths mapped for optimization insights
          4: Engagement metrics calculated for retention analysis
      capabilities:
      - Anonymous and authenticated user tracking
      - Session-based activity correlation
      - Feature usage heatmaps and analytics
      - User journey visualization and analysis
      - Engagement metrics and retention rates
      - A/B testing support through user segmentation
      key_metrics:
      - Daily/Weekly/Monthly Active Users (DAU/WAU/MAU)
      - Session duration and page views per session
      - Feature adoption rates and usage frequency
      - User retention and churn analysis
      - Conversion rates by user segments
      - Time-to-value for new user onboarding
      privacy_compliance:
      - User consent verification before tracking
      - Anonymous user support without PII collection
      - Data retention policies aligned with GDPR/CCPA
      - User opt-out mechanisms and right to deletion
      - IP address anonymization and geographic aggregation
      invariants:
      - User activity must be trackable across sessions while respecting privacy
      - Anonymous users must be supported without compromising functionality
      - Session data must maintain integrity and temporal ordering
      - Feature usage tracking must not impact application performance
      forbidden_states:
      - Tracking personal data without explicit user consent
      - Session data corruption or loss during transitions
      - Performance degradation due to excessive tracking
      - Privacy violations through data correlation or inference
      depends_on:
      - authentication
      - database
      - api
      provides:
      - user_journey_analytics
      - behavioral_insights
      - engagement_metrics
      - retention_analysis
      files:
      - app/services/analytics_service.py
      - app/db/models/analytics.py
      - app/db/manager/repositories/user_activity_repository.py
      - app/api/v1/endpoints/analytics/analytics.py
      domain: analytics
      subdomain: user_behavior
      path: analytics/user_behavior.yaml
      file_size_kb: 3.78
    performance_monitoring:
      system: villiers_analytics_performance_monitoring
      description: Performance Monitoring Analytics - Monitor and analyze system performance, API metrics, and operational
        health for optimization including system metrics collection, API performance tracking, database monitoring, and alerting
      intent_assertions:
      - Real-time system performance monitoring and alerting
      - API endpoint performance tracking and optimization
      - Database query performance analysis and tuning
      - Quote accuracy monitoring for operator performance
      - Resource utilization tracking and capacity planning
      - Error rate monitoring and system health dashboards
      technical_assertions:
        metrics_collection:
        - path: app/services/analytics_service.py
          methods:
          - track_system_metric
          - track_api_latency
          - track_database_operation
          - track_quote_estimate_accuracy
          purpose: System performance metrics collection
        - path: app/db/models/analytics.py
          model: SystemMetric
          purpose: Performance metrics storage with dimensions and categories
        - path: app/db/manager/repositories/system_metric_repository.py
          methods:
          - create_system_metric
          - get_quote_accuracy_metrics
          - get_api_performance_metrics
          purpose: Performance metrics data access and analysis
        scheduler_integration:
        - path: app/core/scheduler_metrics.py
          component: SchedulerMonitor
          purpose: Scheduler performance tracking and job execution metrics
        - path: app/services/scheduler_service.py
          methods:
          - get_health_status
          - get_performance_metrics
          purpose: Scheduler health monitoring and reporting
        monitoring_infrastructure:
        - path: app/templates/system/performance_alert.html
          purpose: Performance alert email templates with metrics visualization
        - path: app/templates/system/error_alert.html
          purpose: Error alert templates with system context
      behavior:
        metrics_collection_workflow:
          1: System components emit performance metrics during operation
          2: Metrics captured with relevant dimensions and categories
          3: Real-time aggregation and threshold monitoring
          4: Alert triggering for performance degradation
          5: Historical trending and capacity planning analysis
        performance_analysis:
          1: API response times tracked per endpoint and method
          2: Database query performance monitored with slow query detection
          3: Resource utilization trends analyzed for scaling decisions
          4: Error rates correlated with system load and performance
      capabilities:
        system_monitoring:
        - API response time tracking with percentile analysis
        - Database query performance monitoring
        - Memory and CPU utilization tracking
        - Error rate monitoring with context
        - Scheduler job execution metrics
        performance_optimization:
        - Slow query identification and optimization recommendations
        - API endpoint performance comparison and optimization
        - Resource bottleneck identification
        - Performance trend analysis for capacity planning
        alerting_system:
        - Real-time threshold-based alerting
        - Performance degradation detection
        - System health status reporting
        - Email and webhook notification integration
      key_metrics:
        api_performance:
        - Response time percentiles (P50, P95, P99)
        - Throughput (requests per minute)
        - Error rates by endpoint and status code
        - Availability and uptime percentage
        database_performance:
        - Query execution time and slow query detection
        - Connection pool utilization
        - Cache hit ratios and memory usage
        - Lock wait times and deadlock detection
        system_health:
        - CPU and memory utilization trends
        - Disk usage and I/O performance
        - Network latency and throughput
        - Service availability and response times
        business_metrics:
        - Quote accuracy by operator and route
        - Booking conversion rates and funnel performance
        - Revenue per transaction and time period
        - Customer acquisition cost and lifetime value
      monitoring_thresholds:
        performance_alerts:
        - API response time > 500ms (P95)
        - Database query time > 1000ms
        - Error rate > 1% over 5-minute window
        - Memory usage > 80% sustained
        - CPU usage > 85% sustained
        critical_alerts:
        - API response time > 2000ms (P95)
        - Database query time > 5000ms
        - Error rate > 5% over 2-minute window
        - Memory usage > 95%
        - Service unavailable for > 1 minute
      integration_points:
      - Scheduler service for automated metrics collection
      - Email service for alert notifications
      - Dashboard service for real-time visualization
      - Logging service for metric correlation
      - External monitoring tools (Datadog, New Relic)
      invariants:
      - Performance metrics must be collected without impacting system performance
      - Alert thresholds must be validated and tuned to minimize false positives
      - Metrics data must maintain temporal consistency and ordering
      - System health checks must complete within specified timeouts
      forbidden_states:
      - Performance monitoring causing performance degradation
      - Alert fatigue due to excessive false positive alerts
      - Metrics collection failures going undetected
      - Critical system issues not triggering appropriate alerts
      depends_on:
      - database
      - api
      - communication
      - infrastructure
      provides:
      - system_performance_metrics
      - operational_health_monitoring
      - performance_optimization_insights
      - automated_alerting_system
      files:
      - app/services/analytics_service.py
      - app/db/models/analytics.py
      - app/db/manager/repositories/system_metric_repository.py
      - app/core/scheduler_metrics.py
      - app/services/scheduler_service.py
      - app/templates/system/performance_alert.html
      - app/templates/system/error_alert.html
      domain: analytics
      subdomain: performance_monitoring
      path: analytics/performance_monitoring.yaml
      file_size_kb: 5.73
domain_metrics:
  core:
    systems_count: 1
    total_lines: 473
    total_size_kb: 20.41
    subdomain_count: 0
    has_main_system: true
  aircraft:
    systems_count: 5
    total_lines: 551
    total_size_kb: 24.61
    subdomain_count: 4
    has_main_system: true
  booking:
    systems_count: 6
    total_lines: 2956
    total_size_kb: 140.26
    subdomain_count: 5
    has_main_system: true
  airport:
    systems_count: 3
    total_lines: 510
    total_size_kb: 23.77
    subdomain_count: 2
    has_main_system: true
  communication:
    systems_count: 3
    total_lines: 2034
    total_size_kb: 93.85
    subdomain_count: 2
    has_main_system: true
  authentication:
    systems_count: 1
    total_lines: 575
    total_size_kb: 23.96
    subdomain_count: 0
    has_main_system: true
  analytics:
    systems_count: 4
    total_lines: 763
    total_size_kb: 29.38
    subdomain_count: 3
    has_main_system: true
constraints:
  data_sovereignty:
  - All aircraft data must come from database - no hardcoded data
  - All operator information must be dynamically loaded from database
  - All pricing must be calculated from database rules and historical data
  - No in-memory storage for production data - PostgreSQL only
  performance_requirements:
  - Trip search responses must complete within 2000ms
  - Quote aggregation must complete within 30000ms
  - Chat responses must complete within 2000ms
  - Database queries must complete within 200ms
  - API health checks must complete within 100ms
  security_constraints:
  - All customer data must be encrypted at rest and in transit
  - Payment processing must be PCI DSS compliant
  - Authentication must use secure token management
  - All API endpoints must have proper authentication
  integration_requirements:
  - Real GPT API calls only - no mocking in production
  - All operator communications must be logged and auditable
  - Email processing must handle structured and unstructured formats
  - Chat interface must maintain conversation context
enforcement_hooks:
- database_consistency_validator
- performance_monitoring
- security_compliance_check
- integration_health_monitor
- data_sovereignty_enforcer
