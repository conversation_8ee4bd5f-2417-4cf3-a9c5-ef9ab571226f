# Authentication & Authorization
# This section covers authentication methods and security patterns

openapi: 3.0.3
info:
  title: Authentication API
  description: |
    Authentication and authorization patterns for secure API access.
    Covers JWT tokens, OAuth2, API keys, and role-based access control.
  version: 1.0.0

servers:
  - url: https://auth.example.com/v1
    description: Authentication server

paths:
  /auth/login:
    post:
      summary: User login
      description: Authenticate user with email and password
      operationId: login
      tags:
        - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                  format: email
                  example: <EMAIL>
                password:
                  type: string
                  format: password
                  example: secretpassword123
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  accessToken:
                    type: string
                    description: JWT access token
                    example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
                  refreshToken:
                    type: string
                    description: Refresh token for obtaining new access tokens
                  expiresIn:
                    type: integer
                    description: Token expiration time in seconds
                    example: 3600
                  user:
                    $ref: '#/components/schemas/User'
        '401':
          description: Invalid credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthError'

  /auth/refresh:
    post:
      summary: Refresh access token
      description: Get a new access token using refresh token
      operationId: refreshToken
      tags:
        - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - refreshToken
              properties:
                refreshToken:
                  type: string
                  description: Valid refresh token
      responses:
        '200':
          description: Token refreshed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  accessToken:
                    type: string
                    description: New JWT access token
                  expiresIn:
                    type: integer
                    description: Token expiration time in seconds
        '401':
          description: Invalid refresh token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthError'

  /auth/logout:
    post:
      summary: User logout
      description: Invalidate current session and tokens
      operationId: logout
      tags:
        - Authentication
      security:
        - BearerAuth: []
      responses:
        '200':
          description: Logout successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Successfully logged out
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthError'

  /auth/profile:
    get:
      summary: Get user profile
      description: Retrieve authenticated user's profile information
      operationId: getProfile
      tags:
        - Authentication
      security:
        - BearerAuth: []
      responses:
        '200':
          description: Profile retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthError'

components:
  schemas:
    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        email:
          type: string
          format: email
          example: <EMAIL>
        name:
          type: string
          example: John Doe
        role:
          type: string
          enum: [admin, user, moderator]
          example: user
        permissions:
          type: array
          items:
            type: string
          example: [read:users, write:posts]
        createdAt:
          type: string
          format: date-time
          example: 2023-01-01T00:00:00Z
        lastLoginAt:
          type: string
          format: date-time
          example: 2023-12-01T10:30:00Z

    AuthError:
      type: object
      required:
        - error
        - message
      properties:
        error:
          type: string
          enum: [invalid_credentials, token_expired, invalid_token, unauthorized]
          example: invalid_credentials
        message:
          type: string
          example: Invalid email or password
        code:
          type: integer
          example: 401

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: |
        JWT Bearer token authentication.
        
        To authenticate with the API:
        1. Login with your credentials to get an access token
        2. Include the token in the Authorization header: `Bearer <token>`
        3. Use the refresh token to get new access tokens when they expire
        
        Token format: `Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key
      description: |
        API Key authentication for service-to-service communication.
        
        Include your API key in the X-API-Key header:
        `X-API-Key: your-api-key-here`

    OAuth2:
      type: oauth2
      description: OAuth2 authentication flow
      flows:
        authorizationCode:
          authorizationUrl: https://auth.example.com/oauth/authorize
          tokenUrl: https://auth.example.com/oauth/token
          scopes:
            read: Read access to resources
            write: Write access to resources
            admin: Administrative access

security:
  - BearerAuth: []
  - ApiKeyAuth: []
  - OAuth2: [read, write]

tags:
  - name: Authentication
    description: User authentication and session management
