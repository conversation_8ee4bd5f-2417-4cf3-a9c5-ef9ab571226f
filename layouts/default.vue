<template>
  <div class="min-h-screen bg-background">
    <SidebarProvider>
      <!-- Sidebar -->
      <Sidebar class="border-r" collapsible="icon">
        <SidebarHeader class="border-b px-6 py-4 group-data-[collapsible=icon]:px-2">
          <div class="flex items-center gap-2 group-data-[collapsible=icon]:justify-center">
            <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
              <FileText class="h-4 w-4" />
            </div>
            <div class="group-data-[collapsible=icon]:hidden">
              <h1 class="text-lg font-semibold">YAML Viewer</h1>
              <p class="text-xs text-muted-foreground">Documentation Site</p>
            </div>
          </div>
        </SidebarHeader>

        <SidebarContent class="px-4 py-4 group-data-[collapsible=icon]:px-2">
          <YamlNavigation />
        </SidebarContent>

        <SidebarFooter class="border-t px-6 py-4 group-data-[collapsible=icon]:px-2">
          <div class="flex items-center justify-between group-data-[collapsible=icon]:justify-center">
            <div class="text-xs text-muted-foreground group-data-[collapsible=icon]:hidden">
              Built with Nuxt.js
            </div>
            <div class="hidden md:block group-data-[collapsible=icon]:block">
              <ThemeToggle />
            </div>
          </div>
        </SidebarFooter>
      </Sidebar>

      <!-- Main Content -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Header -->
        <header class="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div class="flex h-14 items-center px-4 md:px-6 gap-4">
            <SidebarTrigger />
            <div class="flex-1 max-w-md">
              <SearchBar />
            </div>
            <div class="md:hidden">
              <ThemeToggle />
            </div>

          </div>
        </header>

        <!-- Content Area -->
        <main class="flex-1 overflow-auto">
          <div class="container mx-auto px-4 py-4 md:px-6 md:py-6">
            <slot />
          </div>
        </main>
      </div>
    </SidebarProvider>
    <Toaster />
  </div>
</template>

<script setup lang="ts">
import { FileText } from 'lucide-vue-next'
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarProvider,
  SidebarTrigger,
} from '~/components/ui/sidebar'

import { Toaster } from '~/components/ui/sonner'

// Set page title
useHead({
  title: 'YAML Viewer - Documentation Site',
  meta: [
    { name: 'description', content: 'Modern documentation site for viewing and navigating YAML files with syntax highlighting' }
  ]
})
</script>
