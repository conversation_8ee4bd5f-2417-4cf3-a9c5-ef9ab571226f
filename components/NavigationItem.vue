<template>
  <SidebarMenuItem>
    <!-- Folder Item -->
    <template v-if="item.type === 'folder'">
      <SidebarMenuButton @click="toggleExpanded" class="w-full justify-between h-auto min-h-[44px] py-2"
        :class="{ 'bg-accent': isExpanded }">
        <div class="flex items-center gap-2">
          <Folder class="h-4 w-4" :class="{ 'text-primary': isExpanded }" />
          <span>{{ item.title }}</span>
        </div>
        <ChevronRight class="h-4 w-4 transition-transform duration-200" :class="{ 'rotate-90': isExpanded }" />
      </SidebarMenuButton>

      <!-- Children -->
      <Collapsible v-model:open="isExpanded">
        <CollapsibleContent>
          <SidebarMenuSub v-if="item.children && item.children.length > 0">
            <template v-for="child in item.children" :key="child.path">
              <NavigationItem :item="child" :level="level + 1" />
            </template>
          </SidebarMenuSub>
        </CollapsibleContent>
      </Collapsible>
    </template>

    <!-- File Item -->
    <template v-else>
      <SidebarMenuButton :as="NuxtLink" :to="item.path" class="w-full h-auto min-h-[44px] py-2"
        :class="{ 'bg-accent text-accent-foreground': isActive }">
        <div class="flex items-start gap-2 py-1">
          <FileText class="h-4 w-4 mt-0.5 flex-shrink-0" :class="{ 'text-primary': isActive }" />
          <div class="flex-1 text-left min-w-0">
            <div class="font-medium">{{ item.title }}</div>
            <div v-if="item.description" class="text-xs text-muted-foreground line-clamp-1 mt-0.5">
              {{ item.description }}
            </div>
          </div>
        </div>
      </SidebarMenuButton>
    </template>
  </SidebarMenuItem>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { ChevronRight, Folder, FileText } from 'lucide-vue-next'
import {
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
} from '~/components/ui/sidebar'
import {
  Collapsible,
  CollapsibleContent,
} from '~/components/ui/collapsible'

interface NavigationItem {
  title: string
  path: string
  type: 'file' | 'folder'
  children?: NavigationItem[]
  description?: string
}

interface Props {
  item: NavigationItem
  level: number
}

const props = defineProps<Props>()
const route = useRoute()

const isExpanded = ref(false)

// Check if current route matches this item or any of its children
const isActive = computed(() => {
  if (props.item.type === 'file') {
    return route.path === props.item.path
  }
  return false
})

// Check if this folder should be expanded based on current route
const shouldExpand = computed(() => {
  if (props.item.type === 'folder' && props.item.children) {
    return props.item.children.some(child =>
      route.path.startsWith(child.path) ||
      (child.children && child.children.some(grandchild => route.path.startsWith(grandchild.path)))
    )
  }
  return false
})

// Auto-expand if current route is within this folder
watchEffect(() => {
  if (shouldExpand.value) {
    isExpanded.value = true
  }
})

const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}
</script>

<style scoped>
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
