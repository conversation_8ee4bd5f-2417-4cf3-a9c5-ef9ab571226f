<template>
  <SidebarMenuItem>
    <!-- Folder Item -->
    <template v-if="item.type === 'folder'">
      <!-- Collapsed state: Use Popover -->
      <template v-if="isCollapsed">
        <div :title="item.title">
          <Popover>
            <PopoverTrigger as-child>
              <SidebarMenuButton class="w-full mx-auto py-2 justify-center h-10" :class="{
                'bg-accent': isExpanded
              }">
                <Folder class="h-4 w-4 flex-shrink-0" :class="{ 'text-primary': isExpanded }" />
              </SidebarMenuButton>
            </PopoverTrigger>
            <PopoverContent side="right" align="start" class="w-64 p-2">
              <div class="space-y-1">
                <div class="font-medium text-sm px-2 py-1 border-b mb-2">{{ item.title }}</div>
                <template v-for="child in item.children" :key="child.path">
                  <NuxtLink :to="child.path" class="block">
                    <div class="flex items-center gap-2 px-2 py-2 rounded hover:bg-accent cursor-pointer">
                      <FileText class="h-4 w-4 flex-shrink-0" />
                      <div class="flex-1 min-w-0">
                        <div class="font-medium text-sm">{{ child.title }}</div>
                        <div v-if="child.description" class="text-xs text-muted-foreground truncate">
                          {{ child.description }}
                        </div>
                      </div>
                    </div>
                  </NuxtLink>
                </template>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </template>

      <!-- Expanded state: Use normal collapsible -->
      <template v-else>
        <SidebarMenuButton @click="toggleExpanded" class="w-full py-2 justify-between h-auto" :class="{
          'bg-accent': isExpanded
        }">
          <div class="flex items-center gap-2 text-wrap">
            <Folder class="h-4 w-4 flex-shrink-0" :class="{ 'text-primary': isExpanded }" />
            <span>{{ item.title }}</span>
          </div>
          <ChevronRight class="h-4 w-4 transition-transform duration-200" :class="{ 'rotate-90': isExpanded }" />
        </SidebarMenuButton>

        <!-- Children -->
        <Collapsible v-model:open="isExpanded">
          <CollapsibleContent>
            <SidebarMenuSub v-if="item.children && item.children.length > 0">
              <template v-for="child in item.children" :key="child.path">
                <NavigationItem :item="child" :level="level + 1" />
              </template>
            </SidebarMenuSub>
          </CollapsibleContent>
        </Collapsible>
      </template>
    </template>

    <!-- File Item -->
    <template v-else>
      <NuxtLink :to="item.path" class="block">
        <SidebarMenuButton class="w-full py-2" :class="{
          'bg-accent text-accent-foreground': isActive,
          'h-10 justify-center': isCollapsed,
          'h-auto': !isCollapsed
        }" :title="isCollapsed ? `${item.title}${item.description ? ` - ${item.description}` : ''}` : undefined">
          <div v-if="!isCollapsed" class="flex items-start gap-2">
            <FileText class="h-4 w-4 mt-0.5 flex-shrink-0" :class="{ 'text-primary': isActive }" />
            <div class="flex-1 text-left min-w-0">
              <div class="font-medium">{{ item.title }}</div>
              <div v-if="item.description" class="text-xs text-muted-foreground line-clamp-1 mt-0.5">
                {{ item.description }}
              </div>
            </div>
          </div>
          <FileText v-else class="h-4 w-4" :class="{ 'text-primary': isActive }" />
        </SidebarMenuButton>
      </NuxtLink>
    </template>
  </SidebarMenuItem>
</template>

<script setup lang="ts">
import { ref, computed, watchEffect } from 'vue'
import { useRoute } from 'vue-router'
import { ChevronRight, Folder, FileText } from 'lucide-vue-next'
import {
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  useSidebar,
} from '~/components/ui/sidebar'
import {
  Collapsible,
  CollapsibleContent,
} from '~/components/ui/collapsible'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '~/components/ui/popover'


interface NavigationItem {
  title: string
  path: string
  type: 'file' | 'folder'
  children?: NavigationItem[]
  description?: string
}

interface Props {
  item: NavigationItem
  level: number
}

const props = defineProps<Props>()
const route = useRoute()
const { state, isMobile } = useSidebar()

const isExpanded = ref(false)
const isCollapsed = computed(() => state.value === 'collapsed' && !isMobile.value)

// Check if current route matches this item or any of its children
const isActive = computed(() => {
  if (props.item.type === 'file') {
    return route.path === props.item.path
  }
  return false
})

// Check if this folder should be expanded based on current route
const shouldExpand = computed(() => {
  if (props.item.type === 'folder' && props.item.children) {
    return props.item.children.some(child =>
      route.path.startsWith(child.path) ||
      (child.children && child.children.some(grandchild => route.path.startsWith(grandchild.path)))
    )
  }
  return false
})

// Auto-expand if current route is within this folder
watchEffect(() => {
  if (shouldExpand.value) {
    isExpanded.value = true
  }
})

const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}
</script>

<style scoped>
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
