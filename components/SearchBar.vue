<template>
  <div class="relative max-w-md">
    <div class="relative">
      <Search class="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
      <Input
        v-model="searchQuery"
        placeholder="Search documentation..."
        class="pl-9 pr-10"
        @keydown.enter="performSearch"
        @input="onSearchInput"
      />
      <div class="absolute right-3 top-1/2 -translate-y-1/2">
        <kbd class="pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100">
          <span class="text-xs">⌘</span>K
        </kbd>
      </div>
    </div>

    <!-- Search Results Dropdown -->
    <div
      v-if="showResults && (searchResults.length > 0 || searchQuery.length > 0)"
      class="absolute top-full left-0 right-0 mt-2 bg-popover border rounded-md shadow-lg z-50 max-h-96 overflow-auto"
    >
      <div v-if="searchResults.length === 0 && searchQuery.length > 0" class="p-4 text-center text-muted-foreground">
        No results found for "{{ searchQuery }}"
      </div>
      
      <div v-else class="py-2">
        <div
          v-for="(result, index) in searchResults"
          :key="result.path"
          class="px-4 py-2 hover:bg-accent cursor-pointer"
          :class="{ 'bg-accent': selectedIndex === index }"
          @click="selectResult(result)"
          @mouseenter="selectedIndex = index"
        >
          <div class="flex items-center gap-3">
            <FileText class="h-4 w-4 text-muted-foreground" />
            <div class="flex-1 min-w-0">
              <div class="font-medium truncate">{{ result.title }}</div>
              <div v-if="result.description" class="text-sm text-muted-foreground truncate">
                {{ result.description }}
              </div>
              <div class="text-xs text-muted-foreground mt-1">
                {{ result.path }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { Search, FileText } from 'lucide-vue-next'
import { Input } from '~/components/ui/input'
import Fuse from 'fuse.js'

interface SearchResult {
  title: string
  path: string
  description?: string
  content?: string
}

const router = useRouter()
const searchQuery = ref('')
const showResults = ref(false)
const selectedIndex = ref(0)

// Mock search data - in a real app, this would come from the content API
const searchData = ref<SearchResult[]>([
  {
    title: 'Basics & Fundamentals',
    path: '/api/basics',
    description: 'Core concepts of the OpenAPI specification',
    content: 'openapi authentication api endpoints'
  },
  {
    title: 'Authentication',
    path: '/api/authentication',
    description: 'Authentication methods and security patterns',
    content: 'jwt oauth2 bearer token login logout'
  },
  {
    title: 'Database Configuration',
    path: '/configuration/database',
    description: 'Database connection and management settings',
    content: 'postgresql redis connection pool backup'
  },
  {
    title: 'Server Configuration',
    path: '/configuration/server',
    description: 'Main server configuration settings',
    content: 'cors tls ssl load balancer rate limiting'
  },
  {
    title: 'Docker Deployment',
    path: '/deployment/docker',
    description: 'Docker and Docker Compose configuration',
    content: 'docker compose containers nginx prometheus'
  }
])

// Initialize Fuse.js for fuzzy search
const fuse = computed(() => new Fuse(searchData.value, {
  keys: [
    { name: 'title', weight: 0.4 },
    { name: 'description', weight: 0.3 },
    { name: 'content', weight: 0.2 },
    { name: 'path', weight: 0.1 }
  ],
  threshold: 0.4,
  includeScore: true,
  minMatchCharLength: 2
}))

// Search results
const searchResults = computed(() => {
  if (!searchQuery.value || searchQuery.value.length < 2) {
    return []
  }
  
  const results = fuse.value.search(searchQuery.value)
  return results.slice(0, 8).map(result => result.item)
})

const onSearchInput = () => {
  showResults.value = searchQuery.value.length > 0
  selectedIndex.value = 0
}

const performSearch = () => {
  if (searchResults.value.length > 0) {
    selectResult(searchResults.value[selectedIndex.value])
  }
}

const selectResult = (result: SearchResult) => {
  router.push(result.path)
  searchQuery.value = ''
  showResults.value = false
}

// Keyboard navigation
const handleKeydown = (event: KeyboardEvent) => {
  if (!showResults.value || searchResults.value.length === 0) return

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      selectedIndex.value = Math.min(selectedIndex.value + 1, searchResults.value.length - 1)
      break
    case 'ArrowUp':
      event.preventDefault()
      selectedIndex.value = Math.max(selectedIndex.value - 1, 0)
      break
    case 'Escape':
      showResults.value = false
      searchQuery.value = ''
      break
  }
}

// Global keyboard shortcut (Cmd+K)
const handleGlobalKeydown = (event: KeyboardEvent) => {
  if ((event.metaKey || event.ctrlKey) && event.key === 'k') {
    event.preventDefault()
    const input = document.querySelector('input[placeholder="Search documentation..."]') as HTMLInputElement
    if (input) {
      input.focus()
    }
  }
}

// Click outside to close
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.relative')) {
    showResults.value = false
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
  document.addEventListener('keydown', handleGlobalKeydown)
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  document.removeEventListener('keydown', handleGlobalKeydown)
  document.removeEventListener('click', handleClickOutside)
})
</script>
