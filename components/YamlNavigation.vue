<template>
  <div class="space-y-0">
    <!-- Search Input - hidden when collapsed -->
    <div v-if="!isCollapsed" class="relative mb-4">
      <Search class="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
      <Input v-model="searchQuery" placeholder="Search files..." class="pl-9" />
    </div>

    <!-- Navigation Tree -->
    <SidebarGroup class="p-0">
      <SidebarGroupLabel v-if="!isCollapsed">Documentation</SidebarGroupLabel>
      <SidebarGroupContent>
        <!-- Debug info -->
        <div v-if="!navigation || navigation.length === 0" class="p-4 text-sm text-muted-foreground">
          Loading navigation... ({{ navigation?.length || 0 }} items)
        </div>

        <SidebarMenu :class="isCollapsed ? 'gap-1' : 'gap-0'" v-else>
          <template v-for="(item, index) in filteredNavigation" :key="item.path">
            <NavigationItem :item="item" :level="0" />
            <div v-if="index < filteredNavigation.length - 1 && !isCollapsed" class="h-1"></div>
          </template>
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Search } from 'lucide-vue-next'
import { Input } from '~/components/ui/input'
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  useSidebar,
} from '~/components/ui/sidebar'

interface NavigationItem {
  title: string
  path: string
  type: 'file' | 'folder'
  children?: NavigationItem[]
  description?: string
}

const searchQuery = ref('')
const { state } = useSidebar()
const isCollapsed = computed(() => state.value === 'collapsed')

// Build navigation tree from content files
function buildNavigationTree(files: any[]): NavigationItem[] {
  if (!files || files.length === 0) {
    console.log('No files provided to buildNavigationTree')
    return []
  }

  const tree: { [key: string]: any } = {}

  files.forEach(file => {
    if (!file._path) return

    const pathParts = file._path.split('/').filter(Boolean)
    let currentLevel = tree

    pathParts.forEach((part, index) => {
      const isLast = index === pathParts.length - 1
      const currentPath = '/' + pathParts.slice(0, index + 1).join('/')

      if (isLast) {
        // This is a file
        currentLevel[part] = {
          title: file.title || part.charAt(0).toUpperCase() + part.slice(1).replace(/[-_]/g, ' '),
          path: currentPath,
          type: 'file',
          description: file.description || ''
        }
      } else {
        // This is a folder
        if (!currentLevel[part]) {
          currentLevel[part] = {
            title: part.charAt(0).toUpperCase() + part.slice(1).replace(/[-_]/g, ' '),
            path: currentPath,
            type: 'folder',
            children: {}
          }
        }
        currentLevel = currentLevel[part].children
      }
    })
  })

  return convertTreeToArray(tree)
}

function convertTreeToArray(tree: any): NavigationItem[] {
  return Object.values(tree).map((item: any) => {
    if (item.type === 'folder' && item.children) {
      return {
        ...item,
        children: convertTreeToArray(item.children)
      }
    }
    return item
  }).sort((a: any, b: any) => {
    // Sort folders first, then files
    if (a.type === 'folder' && b.type === 'file') return -1
    if (a.type === 'file' && b.type === 'folder') return 1
    return a.title.localeCompare(b.title)
  })
}

// Load navigation data server-side using useAsyncData
const { data: navigation } = await useAsyncData('navigation-tree', async () => {
  try {
    console.log('Loading navigation server-side...')

    // Try multiple approaches to get content
    let files = null

    // Approach 1: Use our custom content list API
    try {
      files = await $fetch('/api/content-list')
    } catch (apiError) {
      console.log('Content-list API call failed:', apiError)
    }

    // Approach 2: Direct Nuxt Content API call
    if (!files || files.length === 0) {
      try {
        files = await $fetch('/api/_content/query')
      } catch (apiError) {
        console.log('_content API call failed:', apiError)
      }
    }

    // Approach 3: Use queryContent if API failed
    if (!files || files.length === 0) {
      try {
        files = await queryContent().find()
      } catch (queryError) {
        console.log('queryContent failed:', queryError)
      }
    }

    if (files && files.length > 0) {
      return buildNavigationTree(files)
    } else {
      console.log('No content files found, using fallback')
      // Fallback to static navigation
      return [
        {
          title: 'API Documentation',
          path: '/api',
          type: 'folder',
          children: [
            {
              title: 'Basics & Fundamentals',
              path: '/api/basics',
              type: 'file',
              description: 'Core concepts of the OpenAPI specification'
            },
            {
              title: 'Authentication',
              path: '/api/authentication',
              type: 'file',
              description: 'Authentication methods and security patterns'
            }
          ]
        },
        {
          title: 'Configuration',
          path: '/configuration',
          type: 'folder',
          children: [
            {
              title: 'Database',
              path: '/configuration/database',
              type: 'file',
              description: 'Database connection and management settings'
            },
            {
              title: 'Server',
              path: '/configuration/server',
              type: 'file',
              description: 'Main server configuration settings'
            }
          ]
        },
        {
          title: 'Deployment',
          path: '/deployment',
          type: 'folder',
          children: [
            {
              title: 'Docker',
              path: '/deployment/docker',
              type: 'file',
              description: 'Docker and Docker Compose configuration'
            }
          ]
        }
      ]
    }
  } catch (error) {
    console.error('Failed to load navigation:', error)
    return []
  }
}, {
  default: () => []
})



// Filter navigation based on search query
const filteredNavigation = computed(() => {
  if (!searchQuery.value || !navigation.value) {
    return navigation.value || []
  }

  const filterItems = (items: NavigationItem[]): NavigationItem[] => {
    return items.reduce((filtered: NavigationItem[], item) => {
      const matchesSearch = item.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        item.description?.toLowerCase().includes(searchQuery.value.toLowerCase())

      if (item.type === 'folder' && item.children) {
        const filteredChildren = filterItems(item.children)
        if (filteredChildren.length > 0 || matchesSearch) {
          filtered.push({
            ...item,
            children: filteredChildren
          })
        }
      } else if (matchesSearch) {
        filtered.push(item)
      }

      return filtered
    }, [])
  }

  return filterItems(navigation.value)
})


</script>
