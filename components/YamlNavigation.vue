<template>
  <div class="space-y-4">
    <!-- Search Input -->
    <div class="relative mb-6">
      <Search class="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
      <Input v-model="searchQuery" placeholder="Search files..." class="pl-9" />
    </div>

    <!-- Navigation Tree -->
    <SidebarGroup>
      <SidebarGroupLabel>Documentation</SidebarGroupLabel>
      <SidebarGroupContent>
        <SidebarMenu class="space-y-2">
          <template v-for="item in filteredNavigation" :key="item.path">
            <NavigationItem :item="item" :level="0" />
          </template>
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Search } from 'lucide-vue-next'
import { Input } from '~/components/ui/input'
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
} from '~/components/ui/sidebar'

interface NavigationItem {
  title: string
  path: string
  type: 'file' | 'folder'
  children?: NavigationItem[]
  description?: string
}

const searchQuery = ref('')

// Mock navigation data - in a real app, this would come from the content API
const navigation = ref<NavigationItem[]>([
  {
    title: 'API Documentation',
    path: '/api',
    type: 'folder',
    children: [
      {
        title: 'Basics & Fundamentals',
        path: '/api/basics',
        type: 'file',
        description: 'Core concepts of the OpenAPI specification'
      },
      {
        title: 'Authentication',
        path: '/api/authentication',
        type: 'file',
        description: 'Authentication methods and security patterns'
      }
    ]
  },
  {
    title: 'Configuration',
    path: '/configuration',
    type: 'folder',
    children: [
      {
        title: 'Database',
        path: '/configuration/database',
        type: 'file',
        description: 'Database connection and management settings'
      },
      {
        title: 'Server',
        path: '/configuration/server',
        type: 'file',
        description: 'Main server configuration settings'
      }
    ]
  },
  {
    title: 'Deployment',
    path: '/deployment',
    type: 'folder',
    children: [
      {
        title: 'Docker',
        path: '/deployment/docker',
        type: 'file',
        description: 'Docker and Docker Compose configuration'
      }
    ]
  }
])

// Filter navigation based on search query
const filteredNavigation = computed(() => {
  if (!searchQuery.value) {
    return navigation.value
  }

  const filterItems = (items: NavigationItem[]): NavigationItem[] => {
    return items.reduce((filtered: NavigationItem[], item) => {
      const matchesSearch = item.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        item.description?.toLowerCase().includes(searchQuery.value.toLowerCase())

      if (item.type === 'folder' && item.children) {
        const filteredChildren = filterItems(item.children)
        if (filteredChildren.length > 0 || matchesSearch) {
          filtered.push({
            ...item,
            children: filteredChildren
          })
        }
      } else if (matchesSearch) {
        filtered.push(item)
      }

      return filtered
    }, [])
  }

  return filterItems(navigation.value)
})

// Load navigation data from content API
onMounted(async () => {
  try {
    // In a real implementation, you would fetch this from the Nuxt Content API
    // const { data } = await $fetch('/api/_content/query')
    // navigation.value = buildNavigationTree(data)
  } catch (error) {
    console.error('Failed to load navigation:', error)
  }
})
</script>
