<template>
  <div class="space-y-3">
    <!-- Search Input -->
    <div class="relative mb-4">
      <Search class="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
      <Input v-model="searchQuery" placeholder="Search files..." class="pl-9" />
    </div>

    <!-- Navigation Tree -->
    <SidebarGroup>
      <SidebarGroupLabel>Documentation</SidebarGroupLabel>
      <SidebarGroupContent>
        <SidebarMenu class="space-y-2">
          <template v-for="(item, index) in filteredNavigation" :key="item.path">
            <NavigationItem :item="item" :level="0" />
            <div v-if="index < filteredNavigation.length - 1" class="h-1"></div>
          </template>
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Search } from 'lucide-vue-next'
import { Input } from '~/components/ui/input'
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
} from '~/components/ui/sidebar'

interface NavigationItem {
  title: string
  path: string
  type: 'file' | 'folder'
  children?: NavigationItem[]
  description?: string
}

const searchQuery = ref('')

// Load navigation data from Nuxt Content
const navigation = ref<NavigationItem[]>([])

// Build navigation tree from content files
const buildNavigationTree = (files: any[]): NavigationItem[] => {
  const tree: { [key: string]: NavigationItem } = {}

  files.forEach(file => {
    const pathParts = file._path.split('/').filter(Boolean)
    let currentLevel = tree

    pathParts.forEach((part, index) => {
      const isLast = index === pathParts.length - 1
      const currentPath = '/' + pathParts.slice(0, index + 1).join('/')

      if (isLast) {
        // This is a file
        const parentPath = pathParts.slice(0, -1).join('/')
        const parent = parentPath ? getNestedItem(tree, parentPath.split('/').filter(Boolean)) : tree

        if (!parent[part]) {
          parent[part] = {
            title: file.title || part.charAt(0).toUpperCase() + part.slice(1),
            path: currentPath,
            type: 'file',
            description: file.description
          }
        }
      } else {
        // This is a folder
        if (!currentLevel[part]) {
          currentLevel[part] = {
            title: part.charAt(0).toUpperCase() + part.slice(1),
            path: currentPath,
            type: 'folder',
            children: []
          }
        }
        currentLevel = currentLevel[part].children as any
      }
    })
  })

  return convertTreeToArray(tree)
}

const getNestedItem = (tree: any, pathParts: string[]) => {
  let current = tree
  pathParts.forEach(part => {
    if (!current[part]) {
      current[part] = { children: {} }
    }
    current = current[part].children || current[part]
  })
  return current
}

const convertTreeToArray = (tree: any): NavigationItem[] => {
  return Object.values(tree).map((item: any) => {
    if (item.type === 'folder' && item.children) {
      return {
        ...item,
        children: convertTreeToArray(item.children)
      }
    }
    return item
  })
}

// Filter navigation based on search query
const filteredNavigation = computed(() => {
  if (!searchQuery.value) {
    return navigation.value
  }

  const filterItems = (items: NavigationItem[]): NavigationItem[] => {
    return items.reduce((filtered: NavigationItem[], item) => {
      const matchesSearch = item.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        item.description?.toLowerCase().includes(searchQuery.value.toLowerCase())

      if (item.type === 'folder' && item.children) {
        const filteredChildren = filterItems(item.children)
        if (filteredChildren.length > 0 || matchesSearch) {
          filtered.push({
            ...item,
            children: filteredChildren
          })
        }
      } else if (matchesSearch) {
        filtered.push(item)
      }

      return filtered
    }, [])
  }

  return filterItems(navigation.value)
})

// Load navigation data from content API
onMounted(async () => {
  try {
    const { data } = await $fetch('/api/_content/query')
    navigation.value = buildNavigationTree(data)
  } catch (error) {
    console.error('Failed to load navigation:', error)
  }
})
</script>
