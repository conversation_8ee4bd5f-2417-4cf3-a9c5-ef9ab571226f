<template>
  <div class="yaml-viewer">
    <!-- Header -->
    <div class="mb-6">
      <div class="flex items-center justify-between mb-2">
        <div>
          <h1 class="text-2xl font-bold">{{ title }}</h1>
          <p v-if="description" class="text-muted-foreground mt-1">{{ description }}</p>
        </div>
        <div class="flex items-center gap-2">
          <Button variant="outline" size="sm" @click="copyToClipboard" class="gap-2">
            <Copy class="h-4 w-4" />
            Copy
          </Button>
          <Button variant="outline" size="sm" @click="downloadFile" class="gap-2">
            <Download class="h-4 w-4" />
            Download
          </Button>
        </div>
      </div>

      <!-- Breadcrumb -->
      <div class="flex items-center gap-2 text-sm text-muted-foreground">
        <Home class="h-4 w-4" />
        <template v-for="(crumb, index) in breadcrumbs" :key="index">
          <ChevronRight class="h-4 w-4" />
          <NuxtLink v-if="crumb.path" :to="crumb.path" class="hover:text-foreground transition-colors">
            {{ crumb.title }}
          </NuxtLink>
          <span v-else class="text-foreground">{{ crumb.title }}</span>
        </template>
      </div>
    </div>

    <!-- Content -->
    <div class="border rounded-lg overflow-hidden">
      <!-- Toolbar -->
      <div class="flex items-center justify-between px-4 py-2 bg-muted/50 border-b">
        <div class="flex items-center gap-2">
          <FileText class="h-4 w-4" />
          <span class="text-sm font-medium">{{ filename }}</span>
          <span class="text-xs text-muted-foreground">YAML</span>
        </div>
        <div class="flex items-center gap-2">
          <Button variant="ghost" size="sm" @click="toggleLineNumbers" class="text-xs">
            {{ showLineNumbers ? 'Hide' : 'Show' }} Lines
          </Button>
          <Button variant="ghost" size="sm" @click="toggleWordWrap" class="text-xs">
            {{ wordWrap ? 'No Wrap' : 'Wrap' }}
          </Button>
        </div>
      </div>

      <!-- Code Content -->
      <div class="relative">
        <div ref="codeContainer" class="overflow-auto max-h-[70vh]" :class="{
          'whitespace-pre-wrap': wordWrap,
          'whitespace-pre': !wordWrap
        }">
          <pre class="p-4 text-sm leading-relaxed" :class="{
            'pl-12': showLineNumbers,
            'pl-4': !showLineNumbers
          }"><code v-html="highlightedCode"></code></pre>

          <!-- Line Numbers -->
          <div v-if="showLineNumbers"
            class="absolute left-0 top-0 p-4 pr-2 text-xs text-muted-foreground select-none pointer-events-none border-r bg-gray-50 dark:bg-gray-800">
            <div v-for="(line, index) in lines" :key="index" class="leading-relaxed">
              {{ index + 1 }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Metadata -->
    <div v-if="metadata" class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
      <div class="p-4 border rounded-lg">
        <h3 class="font-medium mb-2">File Info</h3>
        <div class="space-y-1 text-sm text-muted-foreground">
          <div>Size: {{ formatFileSize(content.length) }}</div>
          <div>Lines: {{ lines.length }}</div>
          <div>Type: YAML</div>
        </div>
      </div>

      <div v-if="metadata.lastModified" class="p-4 border rounded-lg">
        <h3 class="font-medium mb-2">Last Modified</h3>
        <div class="text-sm text-muted-foreground">
          {{ formatDate(metadata.lastModified) }}
        </div>
      </div>

      <div v-if="metadata.tags && metadata.tags.length > 0" class="p-4 border rounded-lg">
        <h3 class="font-medium mb-2">Tags</h3>
        <div class="flex flex-wrap gap-1">
          <span v-for="tag in metadata.tags" :key="tag"
            class="px-2 py-1 text-xs bg-secondary text-secondary-foreground rounded">
            {{ tag }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Copy, Download, Home, ChevronRight, FileText } from 'lucide-vue-next'
import { Button } from '~/components/ui/button'
import { toast } from 'vue-sonner'

interface Props {
  content: string
  title: string
  description?: string
  filename: string
  breadcrumbs?: Array<{ title: string; path?: string }>
  metadata?: {
    lastModified?: string
    tags?: string[]
    [key: string]: any
  }
}

const props = defineProps<Props>()


const codeContainer = ref<HTMLElement>()
const showLineNumbers = ref(true)
const wordWrap = ref(false)

// Split content into lines
const lines = computed(() => props.content.split('\n'))

// Simple YAML syntax highlighting (basic implementation)
const highlightedCode = computed(() => {
  return props.content
    .replace(/^(\s*)([\w-]+)(\s*)(:)/gm, '$1<span class="text-blue-600 dark:text-blue-400 font-medium">$2</span>$3<span class="text-gray-500">$4</span>')
    .replace(/:\s*([^#\n]+)/g, ': <span class="text-green-600 dark:text-green-400">$1</span>')
    .replace(/(#.*$)/gm, '<span class="text-gray-500 italic">$1</span>')
    .replace(/^(\s*-\s*)/gm, '$1<span class="text-orange-500">•</span> ')
    .replace(/(['"])(.*?)\1/g, '<span class="text-amber-600 dark:text-amber-400">$1$2$1</span>')
})

const toggleLineNumbers = () => {
  showLineNumbers.value = !showLineNumbers.value
}

const toggleWordWrap = () => {
  wordWrap.value = !wordWrap.value
}

const copyToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(props.content)
    toast.success('YAML content copied to clipboard')
  } catch (error) {
    toast.error('Failed to copy to clipboard')
  }
}

const downloadFile = () => {
  const blob = new Blob([props.content], { type: 'text/yaml' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = props.filename.endsWith('.yaml') ? props.filename : `${props.filename}.yaml`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style scoped>
@reference "@/assets/css/tailwind.css";

.yaml-viewer {
  @apply w-full;
}

/* Custom scrollbar for code container */
.overflow-auto::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.overflow-auto::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

.overflow-auto::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}
</style>
