# YAML Viewer - Modern Documentation Site

A modern documentation site built with Nuxt.js and Shadcn Vue components for viewing and navigating YAML files with syntax highlighting, search functionality, and responsive design.

## ✨ Features

- **🎨 Modern UI**: Built with Shadcn Vue components and Tailwind CSS
- **📁 Hierarchical Navigation**: Tree-style sidebar navigation for organized YAML files
- **🔍 Powerful Search**: Fuzzy search functionality to find files and content
- **🌈 Syntax Highlighting**: Beautiful YAML syntax highlighting with proper formatting
- **📱 Responsive Design**: Works perfectly on desktop, tablet, and mobile devices
- **🌙 Dark Mode**: Toggle between light and dark themes
- **📋 Copy & Download**: Easy copy to clipboard and file download functionality
- **⚡ Fast Performance**: Built on Nuxt.js 3 for optimal performance
- **♿ Accessible**: Proper accessibility features and keyboard navigation

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ or Bun
- Git

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd yaml-viewer
```

2. Install dependencies using Bun (recommended):
```bash
bun install
```

Or with npm/yarn:
```bash
npm install
# or
yarn install
```

3. Start the development server:
```bash
bun run dev
```

The application will be available at `http://localhost:3000`

## Development Server

Start the development server on `http://localhost:3000`:

```bash
# npm
npm run dev

# pnpm
pnpm dev

# yarn
yarn dev

# bun
bun run dev
```

## Production

Build the application for production:

```bash
# npm
npm run build

# pnpm
pnpm build

# yarn
yarn build

# bun
bun run build
```

Locally preview production build:

```bash
# npm
npm run preview

# pnpm
pnpm preview

# yarn
yarn preview

# bun
bun run preview
```

Check out the [deployment documentation](https://nuxt.com/docs/getting-started/deployment) for more information.
