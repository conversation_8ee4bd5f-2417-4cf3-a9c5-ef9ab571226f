import { readFile } from 'fs/promises'
import { join } from 'path'

export default defineEventHandler(async (event) => {
  const query = getQuery(event)
  const path = query.path as string

  if (!path) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Path parameter is required'
    })
  }

  try {
    // Construct the file path
    const filePath = join(process.cwd(), 'content', `${path}.yaml`)
    
    // Read the raw file content
    const content = await readFile(filePath, 'utf-8')
    
    return {
      content,
      path
    }
  } catch (error) {
    throw createError({
      statusCode: 404,
      statusMessage: 'YAML file not found'
    })
  }
})
