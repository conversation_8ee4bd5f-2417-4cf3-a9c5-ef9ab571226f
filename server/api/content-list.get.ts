import { readdir, stat } from 'fs/promises'
import { join } from 'path'

interface ContentFile {
  _path: string
  title: string
  description?: string
  _file?: string
  _extension?: string
}

async function scanContentDirectory(dir: string, basePath: string = ''): Promise<ContentFile[]> {
  const files: ContentFile[] = []
  const contentDir = join(process.cwd(), 'content', dir)
  
  try {
    const entries = await readdir(contentDir)
    
    for (const entry of entries) {
      const fullPath = join(contentDir, entry)
      const stats = await stat(fullPath)
      const relativePath = basePath ? `${basePath}/${entry}` : entry
      
      if (stats.isDirectory()) {
        // Recursively scan subdirectories
        const subFiles = await scanContentDirectory(join(dir, entry), relativePath)
        files.push(...subFiles)
      } else if (entry.endsWith('.yaml') || entry.endsWith('.yml')) {
        // This is a YAML file
        const nameWithoutExt = entry.replace(/\.(yaml|yml)$/, '')
        const pathWithoutExt = basePath ? `/${basePath}/${nameWithoutExt}` : `/${nameWithoutExt}`
        
        files.push({
          _path: pathWithoutExt,
          title: nameWithoutExt.charAt(0).toUpperCase() + nameWithoutExt.slice(1).replace(/[-_]/g, ' '),
          description: `YAML configuration file`,
          _file: entry,
          _extension: entry.split('.').pop()
        })
      }
    }
  } catch (error) {
    console.error(`Error scanning directory ${dir}:`, error)
  }
  
  return files
}

export default defineEventHandler(async (event) => {
  try {
    const files = await scanContentDirectory('')
    console.log('Content files found:', files)
    return files
  } catch (error) {
    console.error('Error loading content files:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to load content files'
    })
  }
})
