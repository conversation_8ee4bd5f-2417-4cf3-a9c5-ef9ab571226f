import { defineContentConfig } from '@nuxt/content'

export default defineContentConfig({
  // Content configuration
  sources: {
    content: {
      driver: 'fs',
      prefix: '/',
      base: './content'
    }
  },
  
  // Markdown configuration
  markdown: {
    // Enable syntax highlighting
    highlight: {
      theme: {
        default: 'github-light',
        dark: 'github-dark'
      },
      langs: ['yaml', 'yml', 'json', 'javascript', 'typescript', 'bash', 'shell']
    }
  },

  // YAML configuration
  yaml: {
    // Enable YAML parsing
    enabled: true
  }
})
